# SpaceTaskSim 项目代码文档

## 项目概述

SpaceTaskSim是一个天基协同计算仿真平台，用于模拟卫星、地面站和移动用户之间的通信、计算、感知任务执行。该项目支持多种强化学习算法进行任务调度优化，包括基于世界模型的强化学习(MBRL)方法。

### 主要功能
- **多节点仿真**: 支持卫星(SAT)、地面站(GS)、移动用户(MU)的仿真
- **任务调度**: 支持通信、计算、感知三类任务的调度
- **强化学习**: 集成多种RL算法(D3QN, COMA-AC, MAPPO等)
- **世界模型**: 基于Transformer的世界模型预测
- **资源管理**: 通信、计算、感知、存储资源的统一管理
- **轨道仿真**: 支持LEO、MEO、GEO、SSO等轨道类型
- **可视化评估**: 提供详细的性能评估和可视化

## 项目结构

```
SpaceTaskSim/
├── spacetasksim/                    # 核心仿真模块
│   ├── entity/                      # 实体类
│   ├── enum/                        # 枚举定义
│   ├── manager/                     # 管理器类
│   ├── scheduler/                   # 调度器
│   ├── task/                        # 任务相关
│   ├── utils/                       # 工具类
│   ├── spacetasksim_env.py         # 主环境类
│   ├── spacetasksim_scheduler.py   # 调度器接口
│   └── spacetasksim_evaluation.py  # 评估模块
├── train/                          # 训练模块
│   ├── random/                     # 随机策略
│   ├── greedy/                     # 贪心策略
│   ├── heuristic/                  # 启发式策略
│   ├── predict/                    # 预测策略
│   ├── D3QN/                       # D3QN算法
│   ├── COMA_AC/                    # COMA-AC算法
│   ├── PTMAPPO_Reptile/           # PTMAPPO算法
│   ├── WMAPPO/                     # 世界模型MAPPO
│   ├── WMAPPO_lr13/               # 不同学习率的WMAPPO
│   ├── WMAPPO_lr15/               # 不同学习率的WMAPPO
│   └── WMAPPO_lr55/               # 不同学习率的WMAPPO
├── examples/                       # 示例代码
├── load_*/                         # 不同负载配置
├── train_*/                        # 不同训练配置
└── datasets/                       # 数据集
```

## 核心模块详解

### 1. SpaceTaskSimEnv - 主环境类

**文件位置**: `spacetasksim/spacetasksim_env.py`

这是整个仿真系统的核心类，负责环境的初始化、状态更新和仿真步进。

#### 主要属性
- `simulation_time`: 当前仿真时间
- `simulation_interval`: 仿真时间间隔
- `schedule_interval`: 调度时间间隔
- `flight_interval`: 轨道更新间隔
- `time_accuracy_digit`: 时间精度位数

#### 核心管理器
- `grid_manager`: 网格管理器
- `flight_manager`: 轨道管理器
- `node_manager`: 节点管理器
- `view_manager`: 视野管理器
- `task_manager`: 任务管理器
- `resource_manager`: 资源管理器

#### 主要方法

<augment_code_snippet path="spacetasksim/spacetasksim_env.py" mode="EXCERPT">
````python
def __init__(self, config):
    """环境初始化"""
    # 配置参数解析
    # 管理器初始化
    # 决策变量初始化

def step(self):
    """仿真步进"""
    # 更新轨道和节点状态
    # 更新视野关系
    # 执行任务调度
    # 更新通信、计算、感知
    # 更新任务状态
    # 释放资源
````
</augment_code_snippet>

### 2. 实体类 (Entity)

#### 2.1 Satellite - 卫星类

**文件位置**: `spacetasksim/entity/satellite.py`

卫星类继承自ComputeNode、RelayNode、SenseNode，具备计算、中继、感知三种功能。

<augment_code_snippet path="spacetasksim/entity/satellite.py" mode="EXCERPT">
````python
class Satellite(ComputeNode, RelayNode, SenseNode):
    def __init__(self, simulation_time, id, orbit_type, latitude, longitude, 
                 altitude, v_vector, function_enum_list, calculate_obj, 
                 compute_profile=None, relay_profile=None, sense_profile=None, name=None):
        # 多重继承初始化
        # 轨道类型设置
        # 功能配置
````
</augment_code_snippet>

#### 2.2 GroundStation - 地面站类

**文件位置**: `spacetasksim/entity/ground_station.py`

地面站类继承自TaskGenNode，主要负责任务生成。

<augment_code_snippet path="spacetasksim/entity/ground_station.py" mode="EXCERPT">
````python
class GroundStation(TaskGenNode):
    def __init__(self, simulation_time, id, latitude, longitude, altitude,
                 province_zh, province_en, city_zh, city_en, function_enum_list,
                 task_generation_profile=None):
        # 地理位置信息
        # 任务生成配置
````
</augment_code_snippet>

#### 2.3 MobileUser - 移动用户类

**文件位置**: `spacetasksim/entity/mobile_user.py`

移动用户类也继承自TaskGenNode，代表移动终端用户。

### 3. 枚举类 (Enum)

#### 3.1 NodeTypeEnum - 节点类型枚举

<augment_code_snippet path="spacetasksim/enum/node_type_enum.py" mode="EXCERPT">
````python
class NodeTypeEnum(Enum):
    GS = 1   # ground station 地面站节点
    MU = 2   # mobile user 移动用户节点
    SAT = 3  # satellite 卫星节点
````
</augment_code_snippet>

#### 3.2 OrbitEnum - 轨道类型枚举

<augment_code_snippet path="spacetasksim/enum/orbit_enum.py" mode="EXCERPT">
````python
class OrbitEnum(Enum):
    LEO = 1  # Low Earth orbit 低地球轨道（400-2000km）
    MEO = 2  # Medium Earth Orbit 中地球轨道（2000-35786km）
    GEO = 3  # Geostationary Orbit 地球静止同步轨道（35786km）
    SSO = 4  # Sun-synchronous Orbit 太阳同步轨道
````
</augment_code_snippet>

#### 3.3 TaskTypeEnum - 任务类型枚举

<augment_code_snippet path="spacetasksim/enum/task_type_enum.py" mode="EXCERPT">
````python
class TaskTypeEnum(Enum):
    COMPUTATION = 1    # 计算任务
    COMMUNICATION = 2  # 通信任务
    SENSING = 3        # 遥感任务
````
</augment_code_snippet>

#### 3.4 FunctionEnum - 功能类型枚举

<augment_code_snippet path="spacetasksim/enum/function_enum.py" mode="EXCERPT">
````python
class FunctionEnum(Enum):
    COMPUTATION = 1  # 计算功能
    RELAY = 2        # 中继功能
    SENSING = 3      # 遥感功能
    TASK_GEN = 4     # 任务生成功能
````
</augment_code_snippet>

### 4. 管理器类 (Manager)

#### 4.1 ResourceManager - 资源管理器

**文件位置**: `spacetasksim/manager/resource_manager.py`

统一管理通信、计算、感知、存储四类资源。

<augment_code_snippet path="spacetasksim/manager/resource_manager.py" mode="EXCERPT">
````python
class ResourceManager():
    def __init__(self, state_sync_interval, time_accuracy_digit, config_resource, config_view, nodes):
        self.communication_manager = CommunicationManager(...)
        self.computation_manager = ComputationManager(...)
        self.sensing_manager = SensingManager(...)
        self.storage_manager = StorageManager(...)
````
</augment_code_snippet>

#### 4.2 TaskManager - 任务管理器

**文件位置**: `spacetasksim/manager/task_manager.py`

负责任务的生成、调度、执行和状态管理。

### 5. 调度器 (Scheduler)

#### 5.1 SpaceTaskSimScheduler - 主调度器

**文件位置**: `spacetasksim/spacetasksim_scheduler.py`

提供各种调度器的统一接口。

<augment_code_snippet path="spacetasksim/spacetasksim_scheduler.py" mode="EXCERPT">
````python
class SpaceTaskSimScheduler:
    @staticmethod
    def getResourceScheduler():
        return ResourceScheduler()
    
    @staticmethod
    def getTaskScheduler():
        return TaskScheduler()
    
    @staticmethod
    def getAlgorithmScheduler():
        return AlgorithmScheduler()
````
</augment_code_snippet>

### 6. 任务类 (Task)

#### 6.1 SimpleTask - 基础任务类

**文件位置**: `spacetasksim/task/simple_task.py`

所有任务的基类，定义了任务的基本属性和状态管理。

<augment_code_snippet path="spacetasksim/task/simple_task.py" mode="EXCERPT">
````python
class SimpleTask:
    def __init__(self, task_id, init_node_id, arrival_time, ttl, deadline, steps):
        self._task_id = task_id
        self._init_node_id = init_node_id
        self._arrival_time = arrival_time
        self._ttl = ttl
        self._deadline = deadline
        self._state = StateEnum.INITIALIZED
````
</augment_code_snippet>

### 7. 评估模块

#### SpaceTaskSimEvaluation - 评估类

**文件位置**: `spacetasksim/spacetasksim_evaluation.py`

提供详细的性能评估指标和可视化功能。

<augment_code_snippet path="spacetasksim/spacetasksim_evaluation.py" mode="EXCERPT">
````python
class SpaceTaskSimEvaluation:
    def __init__(self, env: SpaceTaskSimEnv, sche_strategy_module, base_path="./evaluation/"):
        # 评估指标初始化
        # 完成率、失败率、平均完成时间等
        
    def evaluateStep(self):
        # 单步评估
        # 统计完成和失败的任务数量
````
</augment_code_snippet>

## 训练模块详解

### 1. 策略模块架构

所有策略模块都继承自`BaseStrategyModule`，实现统一的接口：

- `initialize()`: 策略初始化
- `scheduleStep()`: 执行调度决策
- `updateExperience()`: 更新经验
- `reset()`: 重置策略状态

### 2. 强化学习算法

#### 2.1 D3QN算法

**文件位置**: `train/D3QN/`

实现了Dueling Double Deep Q-Network算法，用于离散动作空间的任务调度。

<augment_code_snippet path="train/D3QN/strategy.py" mode="EXCERPT">
````python
class D3QN_StrategyModule(BaseStrategyModule):
    def initialize(self, env: SpaceTaskSimEnv, last_episode=0, final=False, eval=False):
        # D3QN环境初始化
        # 模型加载
        
    def train(self):
        # 模型训练
        loss = self.D3QN_env.train()
        return loss
````
</augment_code_snippet>

#### 2.2 MAPPO算法

**文件位置**: `train/WMAPPO/`

实现了Multi-Agent Proximal Policy Optimization算法。

<augment_code_snippet path="train/WMAPPO/strategy.py" mode="EXCERPT">
````python
class MAPPOStrategyModule(BaseStrategyModule):
    def initialize(self, env: SpaceTaskSimEnv, last_episode=0, final=False, eval=False):
        # MAPPO环境初始化
        # 多智能体配置
````
</augment_code_snippet>

### 3. 世界模型 (World Model)

#### 3.1 TransformerWorldModel - Transformer世界模型

**文件位置**: `train/WMAPPO/model/transformer_world_model.py`

基于Transformer架构的世界模型，用于预测环境状态转移。

<augment_code_snippet path="train/WMAPPO/model/transformer_world_model.py" mode="EXCERPT">
````python
class TransformerWorldModel(nn.Module):
    def __init__(self, state_dim, action_dim, d_model=256, nhead=8, num_layers=6):
        # Transformer编码器
        # 状态和动作嵌入层
        # 输出投影层
        
    def forward(self, states, actions, mask=None):
        # 状态和动作嵌入
        # Transformer编码
        # 预测下一状态
````
</augment_code_snippet>

#### 3.2 MBRL策略模块

**文件位置**: `train/WMAPPO/mbrl_strategy.py`

集成世界模型的强化学习策略，支持基于模型的规划。

### 4. 数据收集和处理

#### 4.1 StateSequenceCollector - 状态序列收集器

**文件位置**: `train/WMAPPO/data_collection/state_collector.py`

收集环境状态序列数据用于世界模型训练。

#### 4.2 DataProcessor - 数据处理器

**文件位置**: `train/WMAPPO/model/data_processor.py`

处理和预处理状态序列数据。

## 配置系统

### 配置文件结构

项目使用YAML格式的配置文件，主要包含以下部分：

```yaml
config_simulation:
  simulation_time: 600.0        # 仿真时长
  simulation_interval: 1.0      # 仿真间隔
  schedule_interval: 10.0       # 调度间隔
  flight_interval: 10.0         # 轨道更新间隔

config_node:
  config_constellation:         # 星座配置
    constellation_name: "starlink"
    orbit_altitude: 550         # 轨道高度(km)
    inclination: 53.0          # 轨道倾角(度)
    
config_resource:
  config_communication:         # 通信资源配置
    G2S:                       # 地面到卫星
      bandwidth: [0.25, 0.50]  # 带宽范围(GHz)
      power_dbw: [10, 10]      # 发射功率(dBW)
    S2S:                       # 星间链路
      bandwidth: [0.10, 0.20]
      power_dbw: [5, 5]
      
  config_computation:          # 计算资源配置
    cycle_per_byte: 17         # 每字节计算周期数
    satellite:
      cpu: 4                   # CPU核心数
      frequency: [2.0, 8.0]    # 频率范围(GHz)
      
  config_sensing:              # 感知资源配置
    elevation_mask_angle: 10   # 最小仰角(度)
    accuracy_range: [0.1, 1.0] # 精度范围
```

## 工具类 (Utils)

### 1. geo_util - 地理工具类

**文件位置**: `spacetasksim/utils/geo_util.py`

提供地理计算相关的工具函数：

- 坐标系转换 (LLA ↔ ECEF ↔ ECI)
- 距离和角度计算
- 轨道参数计算
- 地理区域判断

### 2. file_util - 文件工具类

**文件位置**: `spacetasksim/utils/file_util.py`

提供数据序列化和文件操作功能：

<augment_code_snippet path="spacetasksim/utils/file_util.py" mode="EXCERPT">
````python
class TaskInfoSerializer:
    """任务信息序列化器"""
    def add(self, arrival_time, task_id, task_type, init_node_id, ...):
        # 添加任务记录
        
    def save(self):
        # 保存到CSV文件
````
</augment_code_snippet>

## 使用示例

### 1. 基本仿真运行

```python
import yaml
from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from train import Random_StrategyModule

# 加载配置
with open('config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 创建环境和策略
env = SpaceTaskSimEnv(config)
strategy = Random_StrategyModule()
evaluation = SpaceTaskSimEvaluation(env, strategy)

# 初始化
strategy.initialize(env)

# 运行仿真
while not env.isDone():
    strategy.scheduleStep()
    env.step()
    evaluation.evaluateStep()

# 获取结果
completion_ratio = evaluation.getCompletionRatio()
print(f"任务完成率: {completion_ratio:.4f}")
```

### 2. 强化学习训练

```python
from train import D3QN_StrategyModule

# 创建D3QN策略
strategy = D3QN_StrategyModule()
strategy.initialize(env, last_episode=0, final=True)

for episode in range(1000):
    while not env.isDone():
        strategy.scheduleStep()
        env.step()
        strategy.updateExperience()
        
        # 定期训练
        if step % 10 == 0:
            loss = strategy.train()
    
    # 保存模型
    if episode % 100 == 0:
        strategy.saveModel(episode, False, completion_ratio)
    
    env.reset()
    strategy.reset()
```

### 3. 世界模型训练

```python
from train.WMAPPO import MBRLStrategyModule

# 创建MBRL策略
strategy = MBRLStrategyModule()
strategy.initialize(env, use_world_model=True)

# 运行带世界模型的仿真
while not env.isDone():
    strategy.scheduleStep()
    env.step()
    strategy.updateExperience()
```

## Web界面

### Flask应用

**文件位置**: `examples/app.py`

提供Web界面用于可视化仿真过程和结果：

<augment_code_snippet path="examples/app.py" mode="EXCERPT">
````python
app = Flask(__name__)
CORS(app)  # 允许跨域请求

@app.route('/api/simulation/start', methods=['POST'])
def start_simulation():
    # 启动仿真
    
@app.route('/api/simulation/status', methods=['GET'])
def get_simulation_status():
    # 获取仿真状态
````
</augment_code_snippet>

## 性能优化

### 1. 内存管理
- 使用对象池减少内存分配
- 及时清理过期数据
- 优化数据结构存储

### 2. 计算优化
- 向量化计算
- 并行处理
- 缓存频繁计算结果

### 3. 仿真加速
- 自适应时间步长
- 事件驱动仿真
- 多线程并行

## 扩展指南

### 1. 添加新的节点类型

1. 在`spacetasksim/enum/node_type_enum.py`中添加新枚举
2. 在`spacetasksim/entity/`中创建新的节点类
3. 在`NodeManager`中添加节点创建逻辑

### 2. 添加新的任务类型

1. 在`spacetasksim/enum/task_type_enum.py`中添加新枚举
2. 在`spacetasksim/task/`中创建新的任务类
3. 在`TaskManager`中添加任务处理逻辑

### 3. 添加新的强化学习算法

1. 在`train/`下创建新的算法目录
2. 实现继承自`BaseStrategyModule`的策略类
3. 在`train/__init__.py`中导出新算法

## 常见问题

### 1. 内存不足
- 减少仿真时长或增加仿真间隔
- 关闭不必要的数据记录
- 使用更高效的数据结构

### 2. 仿真速度慢
- 增加仿真间隔
- 减少节点数量
- 优化算法实现

### 3. 训练不收敛
- 调整学习率和网络结构
- 增加训练数据
- 检查奖励函数设计

## 总结

SpaceTaskSim是一个功能完整的天基协同计算仿真平台，具有以下特点：

1. **模块化设计**: 清晰的架构分层，便于扩展和维护
2. **多算法支持**: 集成多种强化学习算法
3. **世界模型**: 支持基于模型的强化学习
4. **可视化**: 提供Web界面和详细的评估报告
5. **配置灵活**: 支持多种仿真场景配置
6. **性能优化**: 针对大规模仿真进行了优化

该平台为天基协同计算研究提供了强有力的仿真工具，支持算法验证、性能评估和系统优化等多种应用场景。

# SpaceTaskSim 详细代码文档 - 第1部分：项目概述与架构

## 目录
- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [项目结构](#项目结构)
- [核心设计理念](#核心设计理念)
- [技术栈](#技术栈)

## 项目概述

### 项目简介
SpaceTaskSim是一个专业的天基协同计算仿真平台，专门用于模拟和研究卫星网络中的任务调度、资源分配和协同计算问题。该项目支持多种节点类型（卫星、地面站、移动用户）之间的通信、计算、感知任务执行，并集成了多种强化学习算法用于智能调度优化。

### 主要功能特性

#### 1. 多节点仿真支持
- **卫星节点(SAT)**: 支持LEO、MEO、GEO、SSO等多种轨道类型
- **地面站节点(GS)**: 支持全球地面站网络仿真
- **移动用户节点(MU)**: 支持移动终端用户仿真

#### 2. 多任务类型支持
- **通信任务(COMMUNICATION)**: 数据传输和中继任务
- **计算任务(COMPUTATION)**: 边缘计算和卸载任务
- **感知任务(SENSING)**: 遥感和数据采集任务

#### 3. 资源管理系统
- **通信资源**: 带宽、功率、波束管理
- **计算资源**: CPU频率、核心数管理
- **感知资源**: 相机精度、感知时间管理
- **存储资源**: 存储空间分配和管理

#### 4. 强化学习算法集成
- **传统算法**: Random、Greedy、Heuristic、Predict
- **深度强化学习**: D3QN、COMA-AC、MAPPO
- **世界模型**: 基于Transformer的MBRL算法

#### 5. 轨道仿真系统
- **实时轨道计算**: 基于ephem库的精确轨道预测
- **多轨道类型**: 支持各种卫星轨道参数
- **视野管理**: 动态计算节点间可见性

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    SpaceTaskSim 系统架构                      │
├─────────────────────────────────────────────────────────────┤
│  Web Interface Layer (Flask API)                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Frontend UI   │  │   REST APIs     │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Strategy Layer (强化学习策略层)                              │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│  │ Random  │ │ Greedy  │ │  D3QN   │ │ WMAPPO  │           │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Simulation Core Layer (仿真核心层)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              SpaceTaskSimEnv                            │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │ Scheduler   │ │ Evaluation  │ │ Environment │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Manager Layer (管理器层)                                    │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌────────┐│
│  │  Node   │ │  Task   │ │Resource │ │ Flight  │ │  View  ││
│  │Manager  │ │Manager  │ │Manager  │ │Manager  │ │Manager ││
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └────────┘│
├─────────────────────────────────────────────────────────────┤
│  Entity Layer (实体层)                                       │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│  │Satellite│ │ Ground  │ │ Mobile  │ │  Task   │           │
│  │         │ │Station  │ │  User   │ │Entities │           │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Utility Layer (工具层)                                      │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│  │Geo Utils│ │File Utils│ │Math Utils│ │Log Utils│          │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件关系

```mermaid
graph TB
    A[SpaceTaskSimEnv] --> B[NodeManager]
    A --> C[TaskManager]
    A --> D[ResourceManager]
    A --> E[FlightManager]
    A --> F[ViewManager]
    A --> G[GridManager]
    
    B --> H[Satellite]
    B --> I[GroundStation]
    B --> J[MobileUser]
    
    C --> K[CommunicateTask]
    C --> L[ComputeTask]
    C --> M[SenseTask]
    
    D --> N[CommunicationManager]
    D --> O[ComputationManager]
    D --> P[SensingManager]
    D --> Q[StorageManager]
    
    R[StrategyModule] --> S[BaseStrategyModule]
    S --> T[Random_StrategyModule]
    S --> U[D3QN_StrategyModule]
    S --> V[WMAPPO_StrategyModule]
```

## 项目结构

### 详细目录结构

```
SpaceTaskSim/
├── spacetasksim/                           # 核心仿真模块
│   ├── entity/                             # 实体类定义
│   │   ├── abstract/                       # 抽象基类
│   │   │   ├── base_node.py               # 节点基类
│   │   │   ├── compute_node.py            # 计算节点基类
│   │   │   ├── relay_node.py              # 中继节点基类
│   │   │   ├── sense_node.py              # 感知节点基类
│   │   │   └── task_gen_node.py           # 任务生成节点基类
│   │   ├── satellite.py                   # 卫星实体类
│   │   ├── ground_station.py              # 地面站实体类
│   │   ├── mobile_user.py                 # 移动用户实体类
│   │   └── __init__.py
│   ├── enum/                              # 枚举定义
│   │   ├── node_type_enum.py              # 节点类型枚举
│   │   ├── orbit_enum.py                  # 轨道类型枚举
│   │   ├── task_type_enum.py              # 任务类型枚举
│   │   ├── step_type_enum.py              # 步骤类型枚举
│   │   ├── function_enum.py               # 功能类型枚举
│   │   ├── state_enum.py                  # 状态枚举
│   │   ├── link_enum.py                   # 链路类型枚举
│   │   ├── fail_result_enum.py            # 失败结果枚举
│   │   └── __init__.py
│   ├── manager/                           # 管理器类
│   │   ├── resource_managers/             # 资源管理器子模块
│   │   │   ├── communication_manager.py   # 通信资源管理器
│   │   │   ├── computation_manager.py     # 计算资源管理器
│   │   │   ├── sensing_manager.py         # 感知资源管理器
│   │   │   ├── storage_manager.py         # 存储资源管理器
│   │   │   └── __init__.py
│   │   ├── node_manager.py                # 节点管理器
│   │   ├── task_manager.py                # 任务管理器
│   │   ├── resource_manager.py            # 资源管理器
│   │   ├── flight_manager.py              # 轨道管理器
│   │   ├── view_manager.py                # 视野管理器
│   │   ├── grid_manager.py                # 网格管理器
│   │   └── __init__.py
│   ├── scheduler/                         # 调度器模块
│   │   ├── base_scheduler.py              # 调度器基类
│   │   ├── resource_scheduler.py          # 资源调度器
│   │   ├── task_scheduler.py              # 任务调度器
│   │   ├── view_scheduler.py              # 视野调度器
│   │   ├── node_scheduler.py              # 节点调度器
│   │   ├── algorithm_scheduler.py         # 算法调度器
│   │   └── __init__.py
│   ├── task/                              # 任务相关类
│   │   ├── simple_task.py                 # 基础任务类
│   │   ├── communicate_task.py            # 通信任务类
│   │   ├── compute_task.py                # 计算任务类
│   │   ├── sense_task.py                  # 感知任务类
│   │   ├── step.py                        # 任务步骤类
│   │   └── __init__.py
│   ├── sche_strategy/                     # 调度策略模块
│   │   ├── base_strategy.py               # 基础策略类
│   │   └── __init__.py
│   ├── utils/                             # 工具类
│   │   ├── geo_util.py                    # 地理计算工具
│   │   ├── file_util.py                   # 文件操作工具
│   │   ├── math_util.py                   # 数学计算工具
│   │   ├── log_util.py                    # 日志工具
│   │   ├── node_util.py                   # 节点工具
│   │   └── __init__.py
│   ├── node_config/                       # 节点配置文件
│   │   ├── constellation/                 # 星座配置
│   │   └── station/                       # 地面站配置
│   ├── spacetasksim_env.py               # 主环境类
│   ├── spacetasksim_scheduler.py         # 调度器接口
│   ├── spacetasksim_evaluation.py        # 评估模块
│   └── __init__.py
├── train/                                 # 训练模块
│   ├── random/                           # 随机策略
│   │   ├── strategy.py                   # 随机策略实现
│   │   └── main.py                       # 主程序
│   ├── greedy/                           # 贪心策略
│   │   ├── strategy.py                   # 贪心策略实现
│   │   └── main.py                       # 主程序
│   ├── heuristic/                        # 启发式策略
│   │   ├── strategy.py                   # 启发式策略实现
│   │   └── main.py                       # 主程序
│   ├── predict/                          # 预测策略
│   │   ├── strategy.py                   # 预测策略实现
│   │   └── main.py                       # 主程序
│   ├── D3QN/                             # D3QN算法
│   │   ├── strategy.py                   # D3QN策略实现
│   │   ├── main.py                       # 主程序
│   │   ├── model/                        # 模型定义
│   │   ├── env/                          # 环境包装
│   │   └── utils/                        # 工具函数
│   ├── COMA_AC/                          # COMA-AC算法
│   │   ├── strategy.py                   # COMA-AC策略实现
│   │   ├── main.py                       # 主程序
│   │   ├── model/                        # 模型定义
│   │   ├── env/                          # 环境包装
│   │   └── utils/                        # 工具函数
│   ├── PTMAPPO_Reptile/                  # PTMAPPO算法
│   │   ├── strategy.py                   # PTMAPPO策略实现
│   │   ├── main.py                       # 主程序
│   │   ├── model/                        # 模型定义
│   │   ├── env/                          # 环境包装
│   │   └── utils/                        # 工具函数
│   ├── WMAPPO/                           # 世界模型MAPPO
│   │   ├── strategy.py                   # MAPPO策略实现
│   │   ├── mbrl_strategy.py              # MBRL策略实现
│   │   ├── main.py                       # 主程序
│   │   ├── model/                        # 模型定义
│   │   │   ├── transformer_world_model.py # Transformer世界模型
│   │   │   ├── data_processor.py         # 数据处理器
│   │   │   └── trainer.py                # 模型训练器
│   │   ├── data_collection/              # 数据收集
│   │   │   ├── state_collector.py        # 状态收集器
│   │   │   └── collect_data.py           # 数据收集脚本
│   │   ├── env/                          # 环境包装
│   │   ├── utils/                        # 工具函数
│   │   ├── train_world_model.py          # 世界模型训练
│   │   ├── run_world_model_pipeline.py   # 完整流水线
│   │   ├── example_usage.py              # 使用示例
│   │   └── README.md                     # 说明文档
│   ├── WMAPPO_lr13/                      # 不同学习率的WMAPPO
│   ├── WMAPPO_lr15/                      # 不同学习率的WMAPPO
│   ├── WMAPPO_lr55/                      # 不同学习率的WMAPPO
│   └── __init__.py
├── examples/                             # 示例代码
│   ├── app.py                            # Flask Web应用
│   ├── test_main.py                      # 测试主程序
│   ├── position_output.py                # 位置输出示例
│   └── config.yaml                       # 示例配置文件
├── load_*/                               # 不同负载配置目录
│   ├── config.yaml                       # 负载配置文件
│   └── main.py                           # 主程序
├── train_*/                              # 不同训练配置目录
│   ├── config.yaml                       # 训练配置文件
│   └── main.py                           # 主程序
├── datasets/                             # 数据集
│   ├── China_province.geojson            # 中国省份地理数据
│   └── other_datasets/                   # 其他数据集
├── fig_draw/                             # 图表绘制
│   ├── integrated/                       # 集成图表
│   └── individual/                       # 单独图表
├── models/                               # 预训练模型
│   ├── D3QN/                            # D3QN模型
│   ├── COMA_AC/                         # COMA-AC模型
│   └── WMAPPO/                          # WMAPPO模型
├── files/                                # 文件存储
│   ├── topology_files/                   # 拓扑文件
│   ├── grid_files/                       # 网格文件
│   └── other_files/                      # 其他文件
├── evaluation/                           # 评估结果
│   ├── episode/                          # 单轮评估
│   └── final/                            # 最终评估
├── logs/                                 # 日志文件
├── requirements.txt                      # Python依赖
├── README.md                             # 项目说明
└── setup.py                             # 安装脚本
```

## 核心设计理念

### 1. 模块化设计
- **高内聚低耦合**: 每个模块职责单一，模块间依赖最小化
- **接口标准化**: 统一的接口设计，便于扩展和维护
- **分层架构**: 清晰的分层结构，便于理解和开发

### 2. 可扩展性
- **策略模式**: 支持多种调度策略的插拔式设计
- **工厂模式**: 统一的对象创建机制
- **观察者模式**: 事件驱动的状态更新机制

### 3. 高性能
- **缓存机制**: 频繁访问数据的缓存优化
- **向量化计算**: 利用NumPy进行高效数值计算
- **内存管理**: 合理的内存分配和回收策略

### 4. 可配置性
- **YAML配置**: 灵活的配置文件系统
- **参数化设计**: 关键参数可通过配置文件调整
- **多场景支持**: 支持不同仿真场景的配置

## 技术栈

### 核心依赖
- **Python 3.8+**: 主要编程语言
- **NumPy**: 数值计算库
- **PyTorch**: 深度学习框架
- **ephem**: 天体力学计算库
- **Flask**: Web框架
- **PyYAML**: YAML配置文件解析

### 科学计算库
- **SciPy**: 科学计算库
- **pandas**: 数据处理库
- **matplotlib**: 数据可视化库
- **seaborn**: 统计数据可视化库

### 地理信息处理
- **geopandas**: 地理数据处理
- **shapely**: 几何对象处理
- **geopy**: 地理编码和距离计算

### 强化学习相关
- **gym**: 强化学习环境接口
- **tensorboard**: 训练过程可视化
- **wandb**: 实验管理和可视化

### Web和可视化
- **Flask-CORS**: 跨域资源共享
- **colorama**: 终端颜色输出
- **tqdm**: 进度条显示

### 开发工具
- **pytest**: 单元测试框架
- **black**: 代码格式化工具
- **flake8**: 代码质量检查
- **mypy**: 类型检查工具

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- SpaceTaskSimEnv核心环境类的详细实现
- 所有管理器类的详细方法和属性
- 实体类的完整接口说明
- 枚举类的详细定义

请查看《SpaceTaskSim_详细代码文档_02_核心环境与管理器.md》获取更多详细信息。

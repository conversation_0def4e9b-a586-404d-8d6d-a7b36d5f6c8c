# SpaceTaskSim 详细代码文档 - 第2部分：核心环境类

## 目录
- [SpaceTaskSimEnv 核心环境类](#spacetasksimenv-核心环境类)
- [主要属性详解](#主要属性详解)
- [核心方法详解](#核心方法详解)
- [生命周期管理](#生命周期管理)

## SpaceTaskSimEnv 核心环境类

**文件位置**: `spacetasksim/spacetasksim_env.py`

SpaceTaskSimEnv是整个仿真系统的核心类，负责环境的初始化、状态更新和仿真步进。它协调所有管理器的工作，提供统一的仿真接口。

### 类定义

SpaceTaskSimEnv是整个仿真系统的主要类，提供通信、计算、能耗、任务执行的仿真功能，支持地面站、移动用户和卫星的仿真，同时可以仿真卫星轨迹。该类为智能体提供与环境交互的API接口，支持深度强化学习智能体、基于规则的智能体或人工用户。

## 主要属性详解

### 时间相关属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `simulation_time` | float | 当前仿真时间（秒） |
| `simulation_interval` | float | 仿真时间间隔（秒） |
| `schedule_interval` | float | 调度时间间隔（秒） |
| `flight_interval` | float | 轨道更新间隔（秒） |
| `state_sync_interval` | float | 状态同步间隔（秒） |
| `time_accuracy_digit` | int | 时间精度位数 |
| `max_simulation_time` | float | 最大仿真时间（秒） |
| `epoch` | str | 仿真起始时间戳 |

### 管理器实例

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `grid_manager` | GridManager | 网格管理器，管理地理网格 |
| `flight_manager` | FlightManager | 轨道管理器，管理卫星轨道 |
| `node_manager` | NodeManager | 节点管理器，管理所有节点 |
| `view_manager` | ViewManager | 视野管理器，管理节点间可见性 |
| `task_manager` | TaskManager | 任务管理器，管理任务生成和执行 |
| `resource_manager` | ResourceManager | 资源管理器，管理所有资源 |

### 决策相关属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `scheduled_communication_steps` | dict | 已调度的通信步骤 |
| `scheduled_computation_steps` | dict | 已调度的计算步骤 |
| `scheduled_sensing_steps` | dict | 已调度的感知步骤 |
| `resource_waiting_communication_steps` | dict | 等待资源的通信步骤 |
| `resource_waiting_computation_steps` | dict | 等待资源的计算步骤 |
| `resource_waiting_sensing_steps` | dict | 等待资源的感知步骤 |

### 节点集合属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `SATs` | dict | 卫星节点字典 |
| `GSs` | dict | 地面站节点字典 |
| `MUs` | dict | 移动用户节点字典 |
| `init_cn_transit_sat_ids` | list | 初始过境中国的卫星ID列表 |
| `realtime_cn_transit_sat_ids` | list | 实时过境中国的卫星ID列表 |

## 核心方法详解

### 构造函数

1. __init__(config, init=True)
功能描述：
初始化SpaceTaskSim仿真环境，设置基本参数和管理器。解析配置参数，初始化时间相关属性，初始化决策相关数据结构，调用管理器初始化方法。
参数：
config (dict): 配置字典，包含所有仿真参数。
init (bool, 可选): 是否立即初始化管理器，默认为True。
返回值：
无返回值。该函数完成环境的初始化设置。

**详细实现逻辑**:
1. **配置解析**: 从config字典中提取各种配置参数
2. **时间初始化**: 设置仿真时间、间隔等时间相关参数
3. **数据结构初始化**: 初始化各种字典和列表用于存储决策和状态
4. **管理器初始化**: 根据init参数决定是否立即初始化所有管理器

### 管理器初始化

2. _managersInit()
功能描述：
按照指定顺序初始化所有管理器模块。初始化顺序很重要，后续管理器依赖前面管理器的数据。
参数：
无参数。
返回值：
无返回值。该函数完成所有管理器的初始化。
执行顺序：
1. GridManager - 网格管理器
2. FlightManager - 轨道管理器
3. NodeManager - 节点管理器
4. ViewManager - 视野管理器
5. TaskManager - 任务管理器
6. ResourceManager - 资源管理器



### 仿真步进方法

3. step()
功能描述：
执行一个仿真时间步骤，更新系统状态。检查是否需要更新轨道和节点状态，执行调度，更新各种状态，释放资源，推进仿真时间。
参数：
无参数。
返回值：
bool类型，表示仿真是否结束。True表示仿真结束，False表示继续。



### 状态更新方法

#### 轨道更新
4. _updateFlight()
功能描述：
更新所有卫星的轨道状态和位置信息。调用FlightManager更新所有卫星位置，计算卫星速度向量，更新轨道参数。
参数：
无参数。
返回值：
无返回值。该函数完成轨道状态的更新。

#### 节点更新
5. _updateNode()
功能描述：
更新节点状态和位置信息。获取最新的卫星信息，更新节点位置和属性，同步节点状态到各管理器。
参数：
无参数。
返回值：
无返回值。该函数完成节点状态的更新。

#### 视野更新
6. _updateView()
功能描述：
更新节点间的视野关系和可见性。计算节点间可见性，更新过境中国的卫星列表，建立空间索引加速查询。
参数：
无参数。
返回值：
无返回值。该函数完成视野关系的更新。

#### 调度更新
```python
def _updateSchedule(self):
    """
    执行任务调度

    功能:
        1. 调度通信步骤
        2. 调度计算步骤
        3. 调度感知步骤
    """
    self.task_manager.scheduleCommunicationSteps(
        self.simulation_time,
        self.scheduled_communication_steps,
        self.to_start_communication_step_ids
    )
    self.task_manager.scheduleComputingSteps(
        self.simulation_time,
        self.scheduled_computation_steps,
        self.to_start_computation_step_ids
    )
    self.task_manager.scheduleSensingSteps(
        self.simulation_time,
        self.scheduled_sensing_steps,
        self.to_start_sensing_step_ids
    )
```

### 资源管理方法

#### 资源分配更新
```python
def _updateResourceAllocation(self):
    """
    更新资源分配状态

    功能:
        1. 检查等待资源的步骤
        2. 尝试分配可用资源
        3. 更新资源分配状态
    """
    # 处理等待通信资源的步骤
    for step_id in list(self.resource_waiting_communication_steps.keys()):
        step_info = self.resource_waiting_communication_steps[step_id]
        # 尝试分配通信资源
        if self._tryAllocateCommunicationResource(step_id, step_info):
            del self.resource_waiting_communication_steps[step_id]
            self.to_start_communication_step_ids.append(step_id)

    # 处理等待计算资源的步骤
    for step_id in list(self.resource_waiting_computation_steps.keys()):
        step_info = self.resource_waiting_computation_steps[step_id]
        # 尝试分配计算资源
        if self._tryAllocateComputationResource(step_id, step_info):
            del self.resource_waiting_computation_steps[step_id]
            self.to_start_computation_step_ids.append(step_id)

    # 处理等待感知资源的步骤
    for step_id in list(self.resource_waiting_sensing_steps.keys()):
        step_info = self.resource_waiting_sensing_steps[step_id]
        # 尝试分配感知资源
        if self._tryAllocateSensingResource(step_id, step_info):
            del self.resource_waiting_sensing_steps[step_id]
            self.to_start_sensing_step_ids.append(step_id)
```

#### 资源释放
```python
def _updateResourceRelease(self):
    """
    释放已完成任务的资源

    功能:
        1. 获取已完成的步骤列表
        2. 释放对应的资源
        3. 更新资源状态
    """
    finished_steps = self.task_manager.getLastSimIntervalFinishedSteps()

    for step in finished_steps:
        step_id = step.step_id
        step_type = step.step_type

        if step_type == StepTypeEnum.COMMUNICATION:
            self.resource_manager.communication_manager.releaseResource(step_id)
        elif step_type == StepTypeEnum.COMPUTATION:
            self.resource_manager.computation_manager.releaseResource(step_id)
            node_id = step.node_id
            self.resource_manager.storage_manager.releaseResource(node_id, step_id)
        elif step_type == StepTypeEnum.SENSING:
            self.resource_manager.sensing_manager.releaseResource(step_id)
            node_id = step.node_id
            self.resource_manager.storage_manager.releaseResource(node_id, step_id)

    # 更新资源管理器状态
    self.resource_manager.update(self.simulation_time)
```

### 任务管理方法

```python
def _updateTask(self):
    """
    更新任务状态

    功能:
        1. 获取过境卫星信息
        2. 确定活跃节点列表
        3. 更新任务管理器状态
    """
    sat_transit_infos = self.view_manager.getTransitSatInfos()
    active_node_ids = self.init_cn_transit_sat_ids  # 使用初始过境卫星列表
    nodes = self.node_manager.getAllNodes()

    self.task_manager.update(
        self.simulation_time,
        self.simulation_interval,
        self.schedule_interval,
        sat_transit_infos,
        nodes,
        active_node_ids
    )
```

## 生命周期管理

### 重置方法
7. reset()
功能描述：
重置仿真环境到初始状态。重置仿真时间，清空决策缓存，重置所有管理器，重新初始化节点引用。
参数：
无参数。
返回值：
无返回值。该函数完成环境的重置操作。
```

### 结束判断
8. isDone()
功能描述：
判断仿真是否已经结束。检查仿真时间是否达到最大仿真时间。
参数：
无参数。
返回值：
bool类型，True表示仿真结束，False表示继续。
```

### 资源清理
9. close()
功能描述：
关闭仿真环境并清理资源。保存仿真数据，关闭文件句柄，清理内存。
参数：
无参数。
返回值：
无返回值。该函数完成环境的清理操作。
```

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 所有管理器类的详细实现
- NodeManager、TaskManager、ResourceManager等的完整API
- 管理器间的协作机制

请查看《SpaceTaskSim_详细代码文档_03_管理器类详解.md》获取更多详细信息。

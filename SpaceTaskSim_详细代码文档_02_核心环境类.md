# SpaceTaskSim 详细代码文档 - 第2部分：核心环境类

## 目录
- [SpaceTaskSimEnv 核心环境类](#spacetasksimenv-核心环境类)
- [主要属性详解](#主要属性详解)
- [核心方法详解](#核心方法详解)
- [生命周期管理](#生命周期管理)

## SpaceTaskSimEnv 核心环境类

**文件位置**: `spacetasksim/spacetasksim_env.py`

SpaceTaskSimEnv是整个仿真系统的核心类，负责环境的初始化、状态更新和仿真步进。它协调所有管理器的工作，提供统一的仿真接口。

### 类定义

```python
class SpaceTaskSimEnv():
    """
    SpaceTaskSimEnv is the main class for the spacetasksim environment.
    It provides the simulation of communication, computation, energy consumption,
    task execution on GUs(ground stations), MUs(mobile users) and SATs(satellites).
    It can also simulate satellite trajectories.
    This class also provides the APIs for the agent to interact with the environment.
    The agent can be a DRL agent, a rule-based agent, or a human user.
    """
```

## 主要属性详解

### 时间相关属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `simulation_time` | float | 当前仿真时间（秒） |
| `simulation_interval` | float | 仿真时间间隔（秒） |
| `schedule_interval` | float | 调度时间间隔（秒） |
| `flight_interval` | float | 轨道更新间隔（秒） |
| `state_sync_interval` | float | 状态同步间隔（秒） |
| `time_accuracy_digit` | int | 时间精度位数 |
| `max_simulation_time` | float | 最大仿真时间（秒） |
| `epoch` | str | 仿真起始时间戳 |

### 管理器实例

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `grid_manager` | GridManager | 网格管理器，管理地理网格 |
| `flight_manager` | FlightManager | 轨道管理器，管理卫星轨道 |
| `node_manager` | NodeManager | 节点管理器，管理所有节点 |
| `view_manager` | ViewManager | 视野管理器，管理节点间可见性 |
| `task_manager` | TaskManager | 任务管理器，管理任务生成和执行 |
| `resource_manager` | ResourceManager | 资源管理器，管理所有资源 |

### 决策相关属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `scheduled_communication_steps` | dict | 已调度的通信步骤 |
| `scheduled_computation_steps` | dict | 已调度的计算步骤 |
| `scheduled_sensing_steps` | dict | 已调度的感知步骤 |
| `resource_waiting_communication_steps` | dict | 等待资源的通信步骤 |
| `resource_waiting_computation_steps` | dict | 等待资源的计算步骤 |
| `resource_waiting_sensing_steps` | dict | 等待资源的感知步骤 |

### 节点集合属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `SATs` | dict | 卫星节点字典 |
| `GSs` | dict | 地面站节点字典 |
| `MUs` | dict | 移动用户节点字典 |
| `init_cn_transit_sat_ids` | list | 初始过境中国的卫星ID列表 |
| `realtime_cn_transit_sat_ids` | list | 实时过境中国的卫星ID列表 |

## 核心方法详解

### 构造函数

```
__init__(config, init=True)
功能描述：初始化SpaceTaskSim仿真环境，设置基本参数和管理器
参数：
  - config (dict): 配置字典，包含所有仿真参数
  - init (bool): 是否立即初始化管理器，默认True
返回值：无
```

**详细实现逻辑**:
1. **配置解析**: 从config字典中提取各种配置参数
2. **时间初始化**: 设置仿真时间、间隔等时间相关参数
3. **数据结构初始化**: 初始化各种字典和列表用于存储决策和状态
4. **管理器初始化**: 根据init参数决定是否立即初始化所有管理器

### 管理器初始化

```
_managersInit()
功能描述：按照指定顺序初始化所有管理器模块
参数：无
返回值：无
执行顺序：
  1. GridManager - 网格管理器
  2. FlightManager - 轨道管理器
  3. NodeManager - 节点管理器
  4. ViewManager - 视野管理器
  5. TaskManager - 任务管理器
  6. ResourceManager - 资源管理器
```

**详细实现**:
```python
def _managersInit(self):
    # 1. 初始化网格管理器
    self.grid_manager = GridManager(self.config['config_grid'])
    grids = self.grid_manager.getAllGrids()

    # 2. 初始化轨道管理器
    self.flight_manager = FlightManager(
        self.simulation_time,
        self.time_accuracy_digit,
        self.epoch,
        self.config['config_node']['config_constellation'],
        self.config['config_topology']
    )
    sat_infos = self.flight_manager.getSatInfos()

    # 3. 初始化节点管理器
    self.node_manager = NodeManager(
        self.simulation_time,
        self.time_accuracy_digit,
        self.config['config_node'],
        sat_infos
    )
    stations = self.node_manager.getNodes(NodeTypeEnum.GS)
    satellites = self.node_manager.getNodes(NodeTypeEnum.SAT)

    # 4. 初始化视野管理器
    self.view_manager = ViewManager(
        self.simulation_time,
        self.time_accuracy_digit,
        self.epoch,
        self.config['config_view'],
        self.config['config_topology'],
        satellites
    )

    # 5. 获取所有节点用于后续初始化
    all_nodes = self.node_manager.getAllNodes()

    # 6. 初始化任务管理器
    self.task_manager = TaskManager(
        self.time_accuracy_digit,
        self.config['config_task'],
        self.config['config_resource'],
        grids,
        stations
    )

    # 7. 初始化资源管理器
    self.resource_manager = ResourceManager(
        self.state_sync_interval,
        self.time_accuracy_digit,
        self.config['config_resource'],
        self.config['config_view'],
        all_nodes
    )

    # 8. 设置节点引用
    self.SATs = satellites
    self.GSs = stations
    self.MUs = self.node_manager.getNodes(NodeTypeEnum.MU)
```

### 仿真步进方法

```
step()
功能描述：执行一个仿真时间步骤，更新系统状态
参数：无
返回值：bool - 仿真是否结束
执行流程：
  1. 检查是否需要更新轨道和节点状态
  2. 检查是否需要执行调度
  3. 更新通信、计算、感知状态
  4. 更新任务状态
  5. 释放资源
  6. 推进仿真时间
```

**详细实现逻辑**:
```python
def step(self):
    print(Fore.GREEN + 'simulation_time: ', round(self.simulation_time, self.time_accuracy_digit))

    # 1. 检查是否需要更新轨道相关状态（按flight_interval间隔）
    if math_util.float_mod(self.simulation_time, self.flight_interval, self.time_accuracy_digit) < 1e-9:
        self._updateFlight()    # 更新卫星轨道
        self._updateNode()      # 更新节点状态
        self._updateView()      # 更新视野关系

    # 2. 检查是否需要执行调度（按schedule_interval间隔）
    if math_util.float_mod(self.simulation_time, self.schedule_interval, self.time_accuracy_digit) < 1e-9:
        self._updateResourceAllocation()  # 更新资源分配
        self._updateSchedule()            # 执行任务调度
        self._clearDecision()             # 清理决策缓存

    # 3. 每个仿真步骤都要执行的更新
    self._updateWirelessCommunication()  # 更新无线通信状态
    self._updateComputation()            # 更新计算状态
    self._updateSensing()                # 更新感知状态
    self._updateTask()                   # 更新任务状态
    self._updateResourceRelease()        # 释放完成的资源

    # 4. 推进仿真时间
    self.simulation_time = round(
        self.simulation_time + self.simulation_interval,
        self.time_accuracy_digit
    )

    # 5. 返回仿真是否结束
    return self.isDone()
```

### 状态更新方法

#### 轨道更新
```python
def _updateFlight(self):
    """
    更新卫星轨道状态

    功能:
        1. 调用FlightManager更新所有卫星位置
        2. 计算卫星速度向量
        3. 更新轨道参数
    """
    self.flight_manager.update(self.simulation_time)
```

#### 节点更新
```python
def _updateNode(self):
    """
    更新节点状态

    功能:
        1. 获取最新的卫星信息
        2. 更新节点位置和属性
        3. 同步节点状态到各管理器
    """
    sat_infos = self.flight_manager.getSatInfos()
    self.node_manager.update(self.simulation_time, sat_infos)
    self.SATs = self.node_manager.getNodes(NodeTypeEnum.SAT)
```

#### 视野更新
```python
def _updateView(self):
    """
    更新视野关系

    功能:
        1. 计算节点间可见性
        2. 更新过境中国的卫星列表
        3. 建立空间索引加速查询
    """
    self.view_manager.update(self.simulation_time, self.SATs, self.GSs)
    id_list = self.view_manager.getSatIdsTransitChina()
    if len(self.init_cn_transit_sat_ids) == 0:
        self.init_cn_transit_sat_ids = deepcopy(id_list)
    self.realtime_cn_transit_sat_ids = id_list
```

#### 调度更新
```python
def _updateSchedule(self):
    """
    执行任务调度

    功能:
        1. 调度通信步骤
        2. 调度计算步骤
        3. 调度感知步骤
    """
    self.task_manager.scheduleCommunicationSteps(
        self.simulation_time,
        self.scheduled_communication_steps,
        self.to_start_communication_step_ids
    )
    self.task_manager.scheduleComputingSteps(
        self.simulation_time,
        self.scheduled_computation_steps,
        self.to_start_computation_step_ids
    )
    self.task_manager.scheduleSensingSteps(
        self.simulation_time,
        self.scheduled_sensing_steps,
        self.to_start_sensing_step_ids
    )
```

### 资源管理方法

#### 资源分配更新
```python
def _updateResourceAllocation(self):
    """
    更新资源分配状态

    功能:
        1. 检查等待资源的步骤
        2. 尝试分配可用资源
        3. 更新资源分配状态
    """
    # 处理等待通信资源的步骤
    for step_id in list(self.resource_waiting_communication_steps.keys()):
        step_info = self.resource_waiting_communication_steps[step_id]
        # 尝试分配通信资源
        if self._tryAllocateCommunicationResource(step_id, step_info):
            del self.resource_waiting_communication_steps[step_id]
            self.to_start_communication_step_ids.append(step_id)

    # 处理等待计算资源的步骤
    for step_id in list(self.resource_waiting_computation_steps.keys()):
        step_info = self.resource_waiting_computation_steps[step_id]
        # 尝试分配计算资源
        if self._tryAllocateComputationResource(step_id, step_info):
            del self.resource_waiting_computation_steps[step_id]
            self.to_start_computation_step_ids.append(step_id)

    # 处理等待感知资源的步骤
    for step_id in list(self.resource_waiting_sensing_steps.keys()):
        step_info = self.resource_waiting_sensing_steps[step_id]
        # 尝试分配感知资源
        if self._tryAllocateSensingResource(step_id, step_info):
            del self.resource_waiting_sensing_steps[step_id]
            self.to_start_sensing_step_ids.append(step_id)
```

#### 资源释放
```python
def _updateResourceRelease(self):
    """
    释放已完成任务的资源

    功能:
        1. 获取已完成的步骤列表
        2. 释放对应的资源
        3. 更新资源状态
    """
    finished_steps = self.task_manager.getLastSimIntervalFinishedSteps()

    for step in finished_steps:
        step_id = step.step_id
        step_type = step.step_type

        if step_type == StepTypeEnum.COMMUNICATION:
            self.resource_manager.communication_manager.releaseResource(step_id)
        elif step_type == StepTypeEnum.COMPUTATION:
            self.resource_manager.computation_manager.releaseResource(step_id)
            node_id = step.node_id
            self.resource_manager.storage_manager.releaseResource(node_id, step_id)
        elif step_type == StepTypeEnum.SENSING:
            self.resource_manager.sensing_manager.releaseResource(step_id)
            node_id = step.node_id
            self.resource_manager.storage_manager.releaseResource(node_id, step_id)

    # 更新资源管理器状态
    self.resource_manager.update(self.simulation_time)
```

### 任务管理方法

```python
def _updateTask(self):
    """
    更新任务状态

    功能:
        1. 获取过境卫星信息
        2. 确定活跃节点列表
        3. 更新任务管理器状态
    """
    sat_transit_infos = self.view_manager.getTransitSatInfos()
    active_node_ids = self.init_cn_transit_sat_ids  # 使用初始过境卫星列表
    nodes = self.node_manager.getAllNodes()

    self.task_manager.update(
        self.simulation_time,
        self.simulation_interval,
        self.schedule_interval,
        sat_transit_infos,
        nodes,
        active_node_ids
    )
```

## 生命周期管理

### 重置方法
```
reset()
功能描述：重置仿真环境到初始状态
参数：无
返回值：无
功能：
  1. 重置仿真时间
  2. 清空决策缓存
  3. 重置所有管理器
  4. 重新初始化节点引用
```
```

### 结束判断
```
isDone()
功能描述：判断仿真是否已经结束
参数：无
返回值：bool - True表示仿真结束，False表示继续
结束条件：仿真时间达到最大仿真时间
```
```

### 资源清理
```
close()
功能描述：关闭仿真环境并清理资源
参数：无
返回值：无
功能：
  1. 保存仿真数据
  2. 关闭文件句柄
  3. 清理内存
```
```

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 所有管理器类的详细实现
- NodeManager、TaskManager、ResourceManager等的完整API
- 管理器间的协作机制

请查看《SpaceTaskSim_详细代码文档_03_管理器类详解.md》获取更多详细信息。

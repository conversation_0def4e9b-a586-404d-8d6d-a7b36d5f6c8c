# SpaceTaskSim 详细代码文档 - 第2部分：核心环境类

## 目录
- [SpaceTaskSimEnv 核心环境类](#spacetasksimenv-核心环境类)
- [核心方法详解](#核心方法详解)
- [状态更新方法](#状态更新方法)
- [环境管理方法](#环境管理方法)

## SpaceTaskSimEnv 核心环境类

**文件位置**: `spacetasksim/spacetasksim_env.py`

SpaceTaskSimEnv是整个仿真系统的主要类，提供通信、计算、能耗、任务执行的仿真功能，支持地面站、移动用户和卫星的仿真，同时可以仿真卫星轨迹。该类为智能体提供与环境交互的API接口，支持深度强化学习智能体、基于规则的智能体或人工用户。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `simulation_time` | float | 当前仿真时间 |
| `max_simulation_time` | float | 最大仿真时间 |
| `simulation_interval` | float | 仿真时间步长 |
| `schedule_interval` | float | 调度时间间隔 |
| `flight_interval` | float | 轨道更新间隔 |
| `state_sync_interval` | float | 状态同步间隔 |
| `time_accuracy_digit` | int | 时间精度位数 |
| `epoch` | str | 仿真起始时间戳 |

### 管理器组件

| 管理器名称 | 类型 | 描述 |
|-----------|------|------|
| `grid_manager` | GridManager | 网格管理器 |
| `flight_manager` | FlightManager | 轨道管理器 |
| `node_manager` | NodeManager | 节点管理器 |
| `view_manager` | ViewManager | 视野管理器 |
| `task_manager` | TaskManager | 任务管理器 |
| `resource_manager` | ResourceManager | 资源管理器 |

## 核心方法详解

### 1. __init__(config, init=True)
功能描述：
初始化SpaceTaskSim仿真环境，设置基本参数和管理器。解析配置参数，初始化时间相关属性，初始化决策相关数据结构，调用管理器初始化方法。
参数：
config (dict): 配置字典，包含所有仿真参数。
init (bool, 可选): 是否立即初始化管理器，默认为True。
返回值：
无返回值。该函数完成环境的初始化设置。

### 2. _managersInit()
功能描述：
按照指定顺序初始化所有管理器模块。初始化顺序很重要，后续管理器依赖前面管理器的数据。
参数：
无参数。
返回值：
无返回值。该函数完成所有管理器的初始化。
执行顺序：
1. GridManager - 网格管理器
2. FlightManager - 轨道管理器
3. NodeManager - 节点管理器
4. ViewManager - 视野管理器
5. TaskManager - 任务管理器
6. ResourceManager - 资源管理器

### 3. step()
功能描述：
执行一个仿真时间步骤，更新系统状态。检查是否需要更新轨道和节点状态，执行调度，更新各种状态，释放资源，推进仿真时间。
参数：
无参数。
返回值：
bool类型，表示仿真是否结束。True表示仿真结束，False表示继续。

## 状态更新方法

### 4. _updateFlight()
功能描述：
更新所有卫星的轨道状态和位置信息。调用FlightManager更新所有卫星位置，计算卫星速度向量，更新轨道参数。
参数：
无参数。
返回值：
无返回值。该函数完成轨道状态的更新。

### 5. _updateNode()
功能描述：
更新节点状态和位置信息。获取最新的卫星信息，更新节点位置和属性，同步节点状态到各管理器。
参数：
无参数。
返回值：
无返回值。该函数完成节点状态的更新。

### 6. _updateView()
功能描述：
更新节点间的视野关系和可见性。计算节点间可见性，更新过境中国的卫星列表，建立空间索引加速查询。
参数：
无参数。
返回值：
无返回值。该函数完成视野关系的更新。

### 7. _updateSchedule()
功能描述：
执行任务调度决策。调用策略模块进行调度决策，分配资源，更新任务状态。
参数：
无参数。
返回值：
无返回值。该函数完成调度决策的执行。

### 8. _updateTask()
功能描述：
更新任务执行状态。检查任务完成情况，更新任务进度，处理任务失败。
参数：
无参数。
返回值：
无返回值。该函数完成任务状态的更新。

### 9. _updateWirelessCommunication()
功能描述：
更新无线通信状态。处理通信步骤的执行，更新通信进度，检查通信完成情况。
参数：
无参数。
返回值：
无返回值。该函数完成通信状态的更新。

### 10. _updateComputation()
功能描述：
更新计算任务状态。处理计算步骤的执行，更新计算进度，检查计算完成情况。
参数：
无参数。
返回值：
无返回值。该函数完成计算状态的更新。

### 11. _updateSensing()
功能描述：
更新感知任务状态。处理感知步骤的执行，更新感知进度，检查感知完成情况。
参数：
无参数。
返回值：
无返回值。该函数完成感知状态的更新。

## 环境管理方法

### 12. reset()
功能描述：
重置仿真环境到初始状态。重置仿真时间，清空决策缓存，重置所有管理器，重新初始化节点引用。
参数：
无参数。
返回值：
无返回值。该函数完成环境的重置操作。

### 13. isDone()
功能描述：
判断仿真是否已经结束。检查仿真时间是否达到最大仿真时间。
参数：
无参数。
返回值：
bool类型，True表示仿真结束，False表示继续。

### 14. close()
功能描述：
关闭仿真环境并清理资源。保存仿真数据，关闭文件句柄，清理内存。
参数：
无参数。
返回值：
无返回值。该函数完成环境的清理操作。

### 15. getObservation()
功能描述：
获取当前环境的观测状态。收集各管理器的状态信息，构建观测向量。
参数：
无参数。
返回值：
dict类型，包含环境状态信息的观测字典。

### 16. getReward()
功能描述：
计算当前步骤的奖励值。根据任务完成情况、资源利用率等计算奖励。
参数：
无参数。
返回值：
float类型，当前步骤的奖励值。

### 17. getInfo()
功能描述：
获取环境的额外信息。包含调试信息、统计数据等。
参数：
无参数。
返回值：
dict类型，包含环境额外信息的字典。

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- NodeManager节点管理器的完整API
- TaskManager任务管理器的详细功能
- ResourceManager资源管理器的核心方法

请查看《SpaceTaskSim_详细代码文档_03_管理器类详解.md》获取更多详细信息。

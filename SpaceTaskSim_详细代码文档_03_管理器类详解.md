# SpaceTaskSim 详细代码文档 - 第3部分：管理器类详解

## 目录
- [NodeManager 节点管理器](#nodemanager-节点管理器)
- [TaskManager 任务管理器](#taskmanager-任务管理器)
- [ResourceManager 资源管理器](#resourcemanager-资源管理器)
- [FlightManager 轨道管理器](#flightmanager-轨道管理器)
- [ViewManager 视野管理器](#viewmanager-视野管理器)

## NodeManager 节点管理器

**文件位置**: `spacetasksim/manager/node_manager.py`

NodeManager负责管理所有类型的节点（卫星、地面站、移动用户），包括节点的创建、更新、查询等操作。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_satellites` | dict | 卫星节点字典 {sat_id: Satellite} |
| `_ground_stations` | dict | 地面站节点字典 {gs_id: GroundStation} |
| `_mobile_users` | dict | 移动用户节点字典 {mu_id: MobileUser} |
| `_time_accuracy_digit` | int | 时间精度位数 |
| `_config_node` | dict | 节点配置信息 |

### 核心方法

#### 构造函数
1. __init__(start_simulation_time, time_accuracy_digit, config_node, sat_infos)
功能描述：
初始化节点管理器，创建各类型节点。初始化基本属性，创建卫星节点，创建地面站节点，创建移动用户节点。
参数：
start_simulation_time (float): 仿真开始时间。
time_accuracy_digit (int): 时间精度位数。
config_node (dict): 节点配置字典。
sat_infos (dict): 卫星信息字典。
返回值：
无返回值。该函数完成节点管理器的初始化。

#### 节点查询方法
2. getNodes(node_type_enum=None)
功能描述：
获取指定类型的节点或所有节点。根据节点类型枚举返回对应的节点字典，如果为None则返回所有类型节点的嵌套字典。
参数：
node_type_enum (NodeTypeEnum, 可选): 节点类型枚举，可选SAT/GS/MU，默认None返回所有。
返回值：
dict类型，节点字典或包含所有类型节点的嵌套字典。

3. getNodeById(node_id)
功能描述：
根据节点ID获取对应的节点对象。在所有类型的节点中搜索指定ID的节点。
参数：
node_id (str): 节点唯一标识符。
返回值：
Node对象，找到的节点，未找到返回None。

```python
def getNodesById(self, id_list):
    """
    根据ID列表批量获取节点

    Args:
        id_list (list): 节点ID列表

    Returns:
        dict: {node_id: node_object} 匹配的节点字典
    """
    matched_nodes = {}
    for node_id in id_list:
        node = self.getNodeById(node_id)
        if node is not None:
            matched_nodes[node_id] = node
    return matched_nodes
```

#### 卫星专用方法
4. getSats(orbit_type_enum=None)
功能描述：
获取指定轨道类型的卫星或所有卫星。根据轨道类型过滤卫星，如果为None则返回按轨道类型分组的所有卫星。
参数：
orbit_type_enum (OrbitEnum, 可选): 轨道类型枚举，可选LEO/MEO/GEO/SSO，默认None返回所有。
返回值：
dict类型，卫星字典或按轨道类型分组的卫星字典。

5. getSatPositions()
功能描述：
获取所有卫星的位置和轨道信息。遍历所有卫星，获取其轨道类型和位置信息。
参数：
无参数。
返回值：
dict类型，格式为{sat_id: {'orbit': orbit_name, 'position': (lat, lon, alt)}}。

#### 更新方法
6. update(simulation_time, sat_infos)
功能描述：
更新所有节点的状态和位置信息。更新所有卫星的位置和速度，更新节点的其他动态属性。
参数：
simulation_time (float): 当前仿真时间。
sat_infos (dict): 卫星信息字典。
返回值：
无返回值。该函数完成节点状态的更新。

## TaskManager 任务管理器

**文件位置**: `spacetasksim/manager/task_manager.py`

TaskManager负责任务的生成、调度、执行状态管理，是整个仿真系统的核心组件之一。

### 类属性

#### 任务存储
| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_executing_tasks` | dict | 正在执行的任务 {TaskTypeEnum: {task_id: task}} |
| `_finished_tasks` | dict | 已完成的任务 {TaskTypeEnum: {task_id: task}} |
| `_failed_tasks` | dict | 已失败的任务 {TaskTypeEnum: {task_id: task}} |
| `_to_scheduled_steps` | dict | 待调度的步骤 {StepTypeEnum: {step_id: step}} |

#### 任务配置
| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_comp_task_size` | list | 计算任务大小范围 [min, max] MB |
| `_comp_task_TTL_range` | list | 计算任务TTL范围 [min, max] 秒 |
| `_comm_task_size` | list | 通信任务大小范围 [min, max] MB |
| `_comm_task_TTL_range` | list | 通信任务TTL范围 [min, max] 秒 |
| `_sensing_task_TTL_range` | list | 感知任务TTL范围 [min, max] 秒 |

#### 统计信息
| 属性名 | 类型 | 描述 |
|--------|------|------|
| `sum_num` | int | 总任务数量 |
| `sum_comm_num` | int | 通信任务数量 |
| `sum_comp_num` | int | 计算任务数量 |
| `sum_sens_num` | int | 感知任务数量 |

### 核心方法

#### 构造函数
1. __init__(time_accuracy_digit, config_task, config_resource, grids, stations)
功能描述：
初始化任务管理器，设置任务生成和调度参数。解析任务配置参数，计算地面任务需求，初始化任务存储结构，设置任务生成概率。
参数：
time_accuracy_digit (int): 时间精度位数。
config_task (dict): 任务配置字典。
config_resource (dict): 资源配置字典。
grids (dict): 网格信息。
stations (dict): 地面站信息。
返回值：
无返回值。该函数完成任务管理器的初始化。

#### 任务生成方法
2. _generateTasks(current_time, simulation_interval, sat_transit, nodes, task_node_ids=None)
功能描述：
根据配置参数生成各类型新任务。根据概率模型生成通信任务，根据负载模型生成计算任务，根据覆盖需求生成感知任务，将新任务添加到执行队列。
参数：
current_time (float): 当前时间。
simulation_interval (float): 仿真间隔。
sat_transit (dict): 过境卫星信息。
nodes (dict): 节点信息。
task_node_ids (list, 可选): 指定的任务生成节点ID列表。
返回值：
无返回值。该函数完成任务的生成。

#### 通信任务生成
```python
def _generateCommTasks(self, current_time, simulation_interval, sat_transit, nodes, task_node_ids):
    """
    生成通信任务

    Args:
        current_time (float): 当前时间
        simulation_interval (float): 仿真间隔
        sat_transit (dict): 过境卫星信息
        nodes (dict): 节点信息
        task_node_ids (list): 任务生成节点ID列表

    生成逻辑:
        1. 遍历所有地面站
        2. 根据泊松分布确定任务生成数量
        3. 随机选择目标地面站
        4. 创建通信任务和步骤
        5. 添加到待调度队列
    """
```

#### 计算任务生成
```python
def _generateCompTasks(self, current_time, simulation_interval, sat_transit, nodes, task_node_ids):
    """
    生成计算任务

    Args:
        current_time (float): 当前时间
        simulation_interval (float): 仿真间隔
        sat_transit (dict): 过境卫星信息
        nodes (dict): 节点信息
        task_node_ids (list): 任务生成节点ID列表

    生成逻辑:
        1. 遍历活跃的卫星节点
        2. 根据计算负载确定任务数量
        3. 随机生成任务参数（大小、TTL等）
        4. 创建计算任务和步骤
        5. 添加到待调度队列
    """
```

#### 感知任务生成
```python
def _generateSensingTasks(self, current_time, simulation_interval, sat_transit, nodes, task_node_ids):
    """
    生成感知任务

    Args:
        current_time (float): 当前时间
        simulation_interval (float): 仿真间隔
        sat_transit (dict): 过境卫星信息
        nodes (dict): 节点信息
        task_node_ids (list): 任务生成节点ID列表

    生成逻辑:
        1. 遍历地面站
        2. 根据感知需求生成任务
        3. 随机选择感知目标位置
        4. 创建感知任务和步骤
        5. 添加到待调度队列
    """
```

#### 任务调度方法
```python
def scheduleCommunicationSteps(self, current_time, scheduled_steps, to_start_step_ids):
    """
    调度通信步骤

    Args:
        current_time (float): 当前时间
        scheduled_steps (dict): 已调度步骤字典
        to_start_step_ids (list): 待启动步骤ID列表

    功能:
        1. 从待调度队列获取通信步骤
        2. 检查调度决策是否存在
        3. 启动已调度的步骤
        4. 更新步骤状态
    """
```

```python
def scheduleComputingSteps(self, current_time, scheduled_steps, to_start_step_ids):
    """
    调度计算步骤

    Args:
        current_time (float): 当前时间
        scheduled_steps (dict): 已调度步骤字典
        to_start_step_ids (list): 待启动步骤ID列表

    功能:
        1. 从待调度队列获取计算步骤
        2. 检查调度决策和资源可用性
        3. 启动已调度的步骤
        4. 分配计算资源
    """
```

```python
def scheduleSensingSteps(self, current_time, scheduled_steps, to_start_step_ids):
    """
    调度感知步骤

    Args:
        current_time (float): 当前时间
        scheduled_steps (dict): 已调度步骤字典
        to_start_step_ids (list): 待启动步骤ID列表

    功能:
        1. 从待调度队列获取感知步骤
        2. 检查调度决策和资源可用性
        3. 启动已调度的步骤
        4. 分配感知资源
    """
```

#### 状态查询方法
```python
def getToScheduledSteps(self):
    """
    获取待调度的步骤

    Returns:
        dict: {StepTypeEnum: {step_id: step}} 待调度步骤字典
    """
    return self._to_scheduled_steps
```

```python
def getLastSimIntervalFinishedTasks(self):
    """
    获取上个仿真间隔完成的任务

    Returns:
        dict: {TaskTypeEnum: [task_list]} 完成任务字典
    """
    return self._last_sim_interval_finished_tasks
```

```python
def getLastSimIntervalFailedTasks(self):
    """
    获取上个仿真间隔失败的任务

    Returns:
        dict: {TaskTypeEnum: [task_list]} 失败任务字典
    """
    return self._last_sim_interval_failed_tasks
```

```python
def getLastSimIntervalFinishedSteps(self):
    """
    获取上个仿真间隔完成的步骤

    Returns:
        list: 完成步骤列表
    """
    return self._last_sim_interval_finished_steps
```

#### 任务检查方法
```python
def _checkSteps(self, current_time):
    """
    检查步骤状态并更新任务状态

    Args:
        current_time (float): 当前时间

    功能:
        1. 检查所有执行中的步骤
        2. 更新完成的步骤状态
        3. 检查任务是否全部完成
        4. 处理超时的步骤和任务
        5. 更新统计信息
    """
```

#### 更新方法
```python
def update(self, current_time, simulation_interval, sche_interval, sat_transit, nodes, task_node_ids=None):
    """
    更新任务管理器状态

    Args:
        current_time (float): 当前时间
        simulation_interval (float): 仿真间隔
        sche_interval (float): 调度间隔
        sat_transit (dict): 过境卫星信息
        nodes (dict): 节点信息
        task_node_ids (list): 任务生成节点ID列表

    功能:
        1. 更新任务生成因子
        2. 生成新任务
        3. 检查步骤状态
        4. 输出统计信息
    """
    current_time = round(current_time, self._time_accuracy_digit)
    self._updateTaskGenFactor(simulation_interval, sat_transit, nodes)
    self._generateTasks(current_time, simulation_interval, sat_transit, nodes, task_node_ids)
    self._checkSteps(current_time)
    print(f"sum_num: {self.sum_num}, sum_comm: {self.sum_comm_num}, "
          f"sum_comp: {self.sum_comp_num}, sum_sens: {self.sum_sens_num}")
```

## ResourceManager 资源管理器

**文件位置**: `spacetasksim/manager/resource_manager.py`

ResourceManager是资源管理的顶层类，统一管理通信、计算、感知、存储四类资源。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `communication_manager` | CommunicationManager | 通信资源管理器 |
| `computation_manager` | ComputationManager | 计算资源管理器 |
| `sensing_manager` | SensingManager | 感知资源管理器 |
| `storage_manager` | StorageManager | 存储资源管理器 |
| `_time_accuracy_digit` | int | 时间精度位数 |
| `_config_resource` | dict | 资源配置信息 |

### 核心方法

#### 构造函数
```python
def __init__(self, state_sync_interval, time_accuracy_digit, config_resource, config_view, nodes):
    """
    初始化资源管理器

    Args:
        state_sync_interval (float): 状态同步间隔
        time_accuracy_digit (int): 时间精度位数
        config_resource (dict): 资源配置字典
        config_view (dict): 视野配置字典
        nodes (dict): 节点信息字典

    功能:
        1. 初始化各子资源管理器
        2. 为所有节点初始化资源
        3. 设置资源记录和导出配置
    """
```

#### 资源初始化
```python
def _initialize(self, nodes: dict):
    """
    为所有节点初始化资源

    Args:
        nodes (dict): {NodeTypeEnum: {node_id: node}} 节点字典

    功能:
        1. 遍历所有节点
        2. 根据节点功能初始化对应资源
        3. 存储资源对所有节点都要初始化
    """
    for node_type_enum, nodes in nodes.items():
        for node_id, node in nodes.items():
            function_enum_list = node.getFunctionEnumList()

            # 所有节点都需要存储资源
            self.storage_manager._init_storage_resources(node)

            # 根据功能初始化对应资源
            if FunctionEnum.RELAY in function_enum_list:
                self.communication_manager._init_communication_resources(node)
            if FunctionEnum.COMPUTATION in function_enum_list:
                self.computation_manager._init_computation_resources(node)
            if FunctionEnum.SENSING in function_enum_list:
                self.sensing_manager._init_sensing_resources(node)
```

#### 更新方法
```python
def update(self, current_time):
    """
    更新所有资源管理器状态

    Args:
        current_time (float): 当前时间

    功能:
        1. 更新各子资源管理器
        2. 记录资源使用情况（如果启用）
    """
    self.communication_manager.update(current_time)
    self.computation_manager.update(current_time)
    self.sensing_manager.update(current_time)
    self.storage_manager.update(current_time)

    if self._record:
        self._addResourceRecord(current_time)
```

#### 资源记录
```python
def _addResourceRecord(self, current_time):
    """
    记录资源使用情况

    Args:
        current_time (float): 当前时间

    功能:
        1. 获取跟踪节点的资源使用情况
        2. 记录到序列化器中
        3. 定期保存到文件
    """
    for node_id in self._track_ids:
        # 获取各类资源使用情况
        storage_usage = self.storage_manager.getResourceUsage(node_id)
        comm_usage = self.communication_manager.getResourceUsage(node_id)
        comp_usage = self.computation_manager.getResourceUsage(node_id)
        sensing_usage = self.sensing_manager.getResourceUsage(node_id)

        # 记录到序列化器
        round_time = round(current_time, self._time_accuracy_digit)
        if round_time % 1 == 0:  # 每秒记录一次
            self._resource_serializer.add(
                current_time, node_id,
                comm_usage.get('s2s_bandwidth_receive', 0),
                comm_usage.get('s2s_bandwidth_transmit', 0),
                comm_usage.get('g2s_bandwidth_receive', 0),
                comm_usage.get('g2s_bandwidth_transmit', 0),
                comm_usage.get('s2g_bandwidth_receive', 0),
                comm_usage.get('s2g_bandwidth_transmit', 0),
                comp_usage.get('idle_cpu_frequency', 0),
                sensing_usage.get('idle_camera_accuracy_list', []),
                storage_usage.get('free_space', 0)
            )
```

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- FlightManager和ViewManager的完整实现
- 各种资源管理器的详细API
- 实体类的完整接口说明

请查看《SpaceTaskSim_详细代码文档_04_轨道视野管理器.md》获取更多详细信息。

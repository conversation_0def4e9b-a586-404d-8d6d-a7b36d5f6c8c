# SpaceTaskSim 详细代码文档 - 第4部分：轨道视野管理器

## 目录
- [FlightManager 轨道管理器](#flightmanager-轨道管理器)
- [ViewManager 视野管理器](#viewmanager-视野管理器)
- [GridManager 网格管理器](#gridmanager-网格管理器)
- [资源管理器子模块](#资源管理器子模块)

## FlightManager 轨道管理器

**文件位置**: `spacetasksim/manager/flight_manager.py`

FlightManager负责管理卫星的轨道运动，包括轨道计算、位置更新、速度计算等功能。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_satellites` | dict | 卫星轨道信息的嵌套字典结构 |
| `_observer` | ephem.Observer | ephem库的观测者对象 |
| `_epoch` | ephem.Date | 仿真起始时间 |
| `_satellite_id_counter` | int | 卫星ID计数器 |
| `_mode` | str | 运行模式：'real'(实时计算) 或 'stored'(预存数据) |
| `_flight_serializer` | FlightDataSerializer | 轨道数据序列化器 |

### 卫星数据结构

FlightManager中的卫星数据采用以下嵌套结构：

卫星数据采用嵌套字典结构，包含星座名称、轨道壳层名称、轨道面名称和卫星ID的层次结构。每个卫星包含ephem卫星对象、纬度、经度、高度、速度向量和轨道类型等信息。

### 核心方法

#### 构造函数
1. __init__(start_simulation_time, time_accuracy_digit, epoch, config_flight, config_topology)
功能描述：
初始化轨道管理器，设置卫星轨道计算参数。初始化ephem观测者对象，设置运行模式(real/stored)，初始化轨道数据序列化器，根据模式加载或创建卫星轨道。
参数：
start_simulation_time (float): 仿真开始时间。
time_accuracy_digit (int): 时间精度位数。
epoch (str): 仿真起始时间戳。
config_flight (dict): 轨道配置字典。
config_topology (dict): 拓扑配置字典。
返回值：
无返回值。该函数完成轨道管理器的初始化。

2. _initSatellites()
功能描述：
初始化卫星轨道。解析星座配置文件，创建ephem卫星对象，计算初始位置和速度，构建卫星数据结构。支持starlink_v1、starlink_v2、geo_sensing、sso_sensing等星座类型。
参数：
无参数。
返回值：
无返回值。该函数完成卫星轨道的初始化。

#### 轨道更新
3. update(simulation_time)
功能描述：
更新卫星轨道状态，计算新的位置和速度。计算时间间隔，根据模式更新卫星位置，计算速度向量，导出轨道数据(如果启用)。
参数：
simulation_time (float): 当前仿真时间。
返回值：
无返回值。该函数完成轨道状态的更新。



4. __setFlightState(simulation_time)
功能描述：
从预存数据设置轨道状态。从序列化器获取指定时间的轨道数据，更新卫星位置和速度信息，处理时间插值。
参数：
simulation_time (float): 仿真时间。
返回值：
无返回值。该函数完成轨道状态的设置。

#### 信息获取
5. getSatInfos()
功能描述：
获取所有卫星的位置和轨道信息。遍历所有星座、轨道壳层和轨道面，收集所有卫星的详细信息。
参数：
无参数。
返回值：
dict类型，格式为{sat_id: sat_info_dict}，包含卫星的纬度、经度、高度、速度向量和轨道类型。
```

## ViewManager 视野管理器

**文件位置**: `spacetasksim/manager/view_manager.py`

ViewManager负责管理节点间的可见性关系，包括卫星-地面站、卫星-卫星之间的视野计算。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_sat_positions` | dict | 卫星位置字典 {sat_id: (lat, lon, alt)} |
| `_sat_kdtree` | KDTree | 卫星位置的KD树索引 |
| `_relay_sat_kdtree` | KDTree | 中继卫星的KD树索引 |
| `_comp_sat_kdtree` | KDTree | 计算卫星的KD树索引 |
| `_sensing_sat_kdtree` | KDTree | 感知卫星的KD树索引 |
| `_china_transit_sat_ids` | list | 过境中国的卫星ID列表 |
| `_view_serializer` | ViewDataSerializer | 视野数据序列化器 |

### 核心方法

#### 构造函数
1. __init__(start_simulation_time, time_accuracy_digit, epoch, config_view, config_topology, satellites)
功能描述：
初始化视野管理器。初始化视野参数，设置运行模式，构建空间索引，计算初始可见性。
参数：
start_simulation_time (float): 仿真开始时间。
time_accuracy_digit (int): 时间精度位数。
epoch (str): 仿真起始时间戳。
config_view (dict): 视野配置字典。
config_topology (dict): 拓扑配置字典。
satellites (dict): 卫星节点字典。
返回值：
无返回值。该函数完成视野管理器的初始化。



2. _updateSatellitePositions(satellites)
功能描述：
更新卫星位置信息。提取所有卫星的位置信息，按功能类型分类卫星位置，转换坐标系用于距离计算。
参数：
satellites (dict): 卫星节点字典。
返回值：
无返回值。该函数完成卫星位置信息的更新。

3. _buildSpatialIndex()
功能描述：
构建空间索引以加速邻近查询。将地理坐标转换为笛卡尔坐标，构建KD树索引，为不同功能类型分别建立索引。
参数：
无参数。
返回值：
无返回值。该函数完成空间索引的构建。





#### 可见性计算
4. getNearestVisibleSatInfos(target_position, K=None, D=None, function_type=None)
功能描述：
获取目标位置附近可见的卫星信息。根据功能类型选择对应的KD树，执行邻近搜索，计算仰角和距离，过滤不满足条件的卫星。
参数：
target_position (tuple): 目标位置 (lat, lon, alt)。
K (int, 可选): 返回最近的K颗卫星。
D (float, 可选): 搜索半径(km)。
function_type (FunctionEnum, 可选): 卫星功能类型过滤。
返回值：
dict类型，格式为{sat_id: sat_info}的可见卫星信息字典。




#### 中国过境计算
5. _calculateChinaTransit()
功能描述：
计算过境中国的卫星。遍历所有卫星位置，检查是否在中国领土范围内，更新过境卫星列表。
参数：
无参数。
返回值：
无返回值。该函数完成过境卫星的计算。

#### 更新方法
6. update(simulation_time, satellites, ground_stations)
功能描述：
更新视野管理器状态。更新卫星位置，重建空间索引，重新计算可见性，导出视野数据。
参数：
simulation_time (float): 当前仿真时间。
satellites (dict): 卫星节点字典。
ground_stations (dict): 地面站节点字典。
返回值：
无返回值。该函数完成视野管理器的更新。

#### 信息获取方法
7. getSatIdsTransitChina()
功能描述：
获取过境中国的卫星ID列表。返回当前过境中国的所有卫星ID。
参数：
无参数。
返回值：
list类型，卫星ID列表。

8. getTransitSatInfos()
功能描述：
获取过境卫星的详细信息。返回过境卫星的位置和相关信息。
参数：
无参数。
返回值：
dict类型，格式为{sat_id: sat_info}的过境卫星信息字典。

## GridManager 网格管理器

**文件位置**: `spacetasksim/manager/grid_manager.py`

GridManager负责管理地理网格系统，用于空间划分和区域管理。

### 核心方法

1. __init__(config_grid)
功能描述：
初始化网格管理器。加载网格数据，建立网格索引，设置网格参数。
参数：
config_grid (dict): 网格配置字典。
返回值：
无返回值。该函数完成网格管理器的初始化。

2. getAllGrids()
功能描述：
获取所有网格信息。返回系统中所有网格的详细信息。
参数：
无参数。
返回值：
dict类型，网格信息字典。

3. getGridByPosition(lat, lon)
功能描述：
根据位置获取对应的网格。根据纬经度坐标查找对应的网格信息。
参数：
lat (float): 纬度。
lon (float): 经度。
返回值：
dict类型，网格信息。

## 资源管理器子模块

### CommunicationManager 通信资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/communication_manager.py`

#### 主要功能
- 管理卫星和地面站的通信资源
- 分配和释放通信波束
- 计算通信链路质量
- 管理带宽和功率资源

#### 核心方法

1. allocateResource(step_id, tx_node_id, rx_node_id, required_bandwidth)
功能描述：分配通信资源。

2. releaseResource(step_id)
功能描述：释放通信资源。

3. getResourceUsage(node_id)
功能描述：获取节点通信资源使用情况。

4. checkResourceAvailable(tx_node_id, rx_node_id, required_bandwidth)
功能描述：检查通信资源可用性。

### ComputationManager 计算资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/computation_manager.py`

#### 主要功能
- 管理节点的计算资源
- 分配和释放CPU资源
- 监控计算负载
- 计算任务执行时间

#### 核心方法

1. allocateResource(step_id, node_id, required_compute_size)
功能描述：分配计算资源。

2. releaseResource(step_id)
功能描述：释放计算资源。

3. getResourceUsage(node_id)
功能描述：获取节点计算资源使用情况。

### SensingManager 感知资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/sensing_manager.py`

#### 主要功能
- 管理卫星的感知资源
- 分配和释放相机资源
- 计算感知精度和时间
- 管理感知任务调度

### StorageManager 存储资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/storage_manager.py`

#### 主要功能
- 管理节点的存储资源
- 分配和释放存储空间
- 监控存储使用情况
- 处理数据存储和传输

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 实体类的完整实现
- 任务类和步骤类的详细API
- 枚举类的完整定义

请查看《SpaceTaskSim_详细代码文档_05_实体类与任务类.md》获取更多详细信息。

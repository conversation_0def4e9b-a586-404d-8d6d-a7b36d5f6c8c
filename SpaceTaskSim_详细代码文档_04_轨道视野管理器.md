# SpaceTaskSim 详细代码文档 - 第4部分：轨道视野管理器

## 目录
- [FlightManager 轨道管理器](#flightmanager-轨道管理器)
- [ViewManager 视野管理器](#viewmanager-视野管理器)
- [GridManager 网格管理器](#gridmanager-网格管理器)
- [资源管理器子模块](#资源管理器子模块)

## FlightManager 轨道管理器

**文件位置**: `spacetasksim/manager/flight_manager.py`

FlightManager负责管理卫星的轨道运动，包括轨道计算、位置更新、速度计算等功能。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_satellites` | dict | 卫星轨道信息的嵌套字典结构 |
| `_observer` | ephem.Observer | ephem库的观测者对象 |
| `_epoch` | ephem.Date | 仿真起始时间 |
| `_satellite_id_counter` | int | 卫星ID计数器 |
| `_mode` | str | 运行模式：'real'(实时计算) 或 'stored'(预存数据) |
| `_flight_serializer` | FlightDataSerializer | 轨道数据序列化器 |

### 卫星数据结构

FlightManager中的卫星数据采用以下嵌套结构：

```python
_satellites = {
    'constellation_name': {  # 星座名称
        'shell_name': {      # 轨道壳层名称
            'orbit_type_enum': OrbitEnum,  # 轨道类型枚举
            'shell_satellites': {
                'plane_name': {  # 轨道面名称
                    'sat_id': {  # 卫星ID
                        'sat_obj': ephem.EarthSatellite,  # ephem卫星对象
                        'sat_lat': float,    # 纬度
                        'sat_lon': float,    # 经度
                        'sat_alt': float,    # 高度(km)
                        'v_vector': tuple,   # 速度向量(ECEF坐标系)
                        'orbit_type_enum': OrbitEnum  # 轨道类型
                    }
                }
            }
        }
    }
}
```

### 核心方法

#### 构造函数
```
__init__(start_simulation_time, time_accuracy_digit, epoch, config_flight, config_topology)
功能描述：初始化轨道管理器，设置卫星轨道计算参数
参数：
  - start_simulation_time (float): 仿真开始时间
  - time_accuracy_digit (int): 时间精度位数
  - epoch (str): 仿真起始时间戳
  - config_flight (dict): 轨道配置字典
  - config_topology (dict): 拓扑配置字典
返回值：无
```
```

#### 卫星初始化
```python
def _initSatellites(self):
    """
    初始化卫星轨道

    功能:
        1. 解析星座配置文件
        2. 创建ephem卫星对象
        3. 计算初始位置和速度
        4. 构建卫星数据结构

    支持的星座类型:
        - starlink_v1: Starlink第一代星座
        - starlink_v2: Starlink第二代星座
        - geo_sensing: 地球静止轨道感知卫星
        - sso_sensing: 太阳同步轨道感知卫星
    """
    constellation_paths = self._config_flight.get('constellation_paths', [])

    for constellation_path in constellation_paths:
        with open(constellation_path, 'r') as f:
            constellation_config = yaml.safe_load(f)

        constellation_name = constellation_config['constellation_name']
        shells = constellation_config['shells']

        self._satellites[constellation_name] = {}

        for shell_config in shells:
            shell_name = shell_config['shell_name']
            orbit_type = OrbitEnum.getEnumByName(shell_config['orbit_type'])

            # 创建轨道壳层
            shell_dict = {
                'orbit_type_enum': orbit_type,
                'shell_satellites': {}
            }

            # 创建轨道面和卫星
            self._createShellSatellites(shell_config, shell_dict)

            self._satellites[constellation_name][shell_name] = shell_dict
```

#### 轨道更新
```
update(simulation_time)
功能描述：更新卫星轨道状态，计算新的位置和速度
参数：
  - simulation_time (float): 当前仿真时间
返回值：无
```

    if self._mode == 'real':
        # 实时计算模式
        self._observer.date = ephem.Date(self._epoch + simulation_time / 86400.0)

        for cons_name, cons_satellites in self._satellites.items():
            for shell_name, shell_dict in cons_satellites.items():
                for plane_name, plane_satellites in shell_dict['shell_satellites'].items():
                    for sat_name, sat_dict in plane_satellites.items():
                        # 保存旧位置用于速度计算
                        old_position = (
                            sat_dict["sat_lat"],
                            sat_dict["sat_lon"],
                            sat_dict["sat_alt"]
                        )

                        # 计算新位置
                        sat_dict["sat_obj"].compute(self._observer)
                        sat_dict["sat_lat"] = float(math.degrees(sat_dict['sat_obj'].sublat))
                        sat_dict["sat_lon"] = float(math.degrees(sat_dict['sat_obj'].sublong))

                        new_position = (
                            sat_dict["sat_lat"],
                            sat_dict["sat_lon"],
                            sat_dict["sat_alt"]
                        )

                        # 计算速度向量
                        sat_dict["v_vector"] = geo_util.calculate_velocity_vector(
                            old_position, new_position, update_interval, self._time_accuracy_digit
                        )

    elif self._mode == 'stored':
        # 预存数据模式
        self.__setFlightState(simulation_time)
    else:
        raise ValueError('Invalid mode')

    # 导出数据
    if self._export:
        self._flight_serializer.add(simulation_time, self._satellites)

    self._last_update_time = simulation_time
```

#### 状态设置
```python
def __setFlightState(self, simulation_time):
    """
    从预存数据设置轨道状态

    Args:
        simulation_time (float): 仿真时间

    功能:
        1. 从序列化器获取指定时间的轨道数据
        2. 更新卫星位置和速度信息
        3. 处理时间插值(如果需要)
    """
    flight_data = self._flight_serializer.get(simulation_time)
    if flight_data:
        self._satellites = flight_data
```

#### 信息获取
```
getSatInfos()
功能描述：获取所有卫星的位置和轨道信息
参数：无
返回值：dict - {sat_id: sat_info_dict} 卫星信息字典
```
```

## ViewManager 视野管理器

**文件位置**: `spacetasksim/manager/view_manager.py`

ViewManager负责管理节点间的可见性关系，包括卫星-地面站、卫星-卫星之间的视野计算。

### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_sat_positions` | dict | 卫星位置字典 {sat_id: (lat, lon, alt)} |
| `_sat_kdtree` | KDTree | 卫星位置的KD树索引 |
| `_relay_sat_kdtree` | KDTree | 中继卫星的KD树索引 |
| `_comp_sat_kdtree` | KDTree | 计算卫星的KD树索引 |
| `_sensing_sat_kdtree` | KDTree | 感知卫星的KD树索引 |
| `_china_transit_sat_ids` | list | 过境中国的卫星ID列表 |
| `_view_serializer` | ViewDataSerializer | 视野数据序列化器 |

### 核心方法

#### 构造函数
```python
def __init__(self, start_simulation_time, time_accuracy_digit, epoch, config_view, config_topology, satellites):
    """
    初始化视野管理器

    Args:
        start_simulation_time (float): 仿真开始时间
        time_accuracy_digit (int): 时间精度位数
        epoch (str): 仿真起始时间戳
        config_view (dict): 视野配置字典
        config_topology (dict): 拓扑配置字典
        satellites (dict): 卫星节点字典

    功能:
        1. 初始化视野参数
        2. 设置运行模式
        3. 构建空间索引
        4. 计算初始可见性
    """
    self._start_simulation_time = start_simulation_time
    self._time_accuracy_digit = time_accuracy_digit
    self._config_view = config_view
    self._epoch = ephem.Date(epoch)

    # 视野参数
    self._elevation_mask_angle = config_view.get('elevation_mask_angle', 10)  # 最小仰角
    self._max_communication_distance = config_view.get('max_communication_distance', 5000)  # 最大通信距离(km)

    # 初始化序列化器
    self._view_serializer = ViewDataSerializer(
        config_topology['view_path'],
        self._time_accuracy_digit
    )
    self._export = config_topology['export']
    self._mode = config_topology['mode']

    # 初始化数据结构
    self._sat_positions = {}
    self._relay_sat_positions = {}
    self._comp_sat_positions = {}
    self._sensing_sat_positions = {}
    self._china_transit_sat_ids = []

    # 根据模式初始化
    if self._mode == 'stored':
        self._view_serializer.load()
        self.__setViewState(self._start_simulation_time)
    else:
        self._updateSatellitePositions(satellites)
        self._buildSpatialIndex()
        self._calculateChinaTransit()
```

#### 卫星位置更新
```python
def _updateSatellitePositions(self, satellites):
    """
    更新卫星位置信息

    Args:
        satellites (dict): 卫星节点字典

    功能:
        1. 提取所有卫星的位置信息
        2. 按功能类型分类卫星位置
        3. 转换坐标系用于距离计算
    """
    self._sat_positions.clear()
    self._relay_sat_positions.clear()
    self._comp_sat_positions.clear()
    self._sensing_sat_positions.clear()

    for sat_id, satellite in satellites.items():
        position = satellite.getPosition()  # (lat, lon, alt)
        self._sat_positions[sat_id] = position

        # 根据功能分类
        functions = satellite.getFunctionEnumList()
        if FunctionEnum.RELAY in functions:
            self._relay_sat_positions[sat_id] = position
        if FunctionEnum.COMPUTATION in functions:
            self._comp_sat_positions[sat_id] = position
        if FunctionEnum.SENSING in functions:
            self._sensing_sat_positions[sat_id] = position
```

#### 空间索引构建
```python
def _buildSpatialIndex(self):
    """
    构建空间索引以加速邻近查询

    功能:
        1. 将地理坐标转换为笛卡尔坐标
        2. 构建KD树索引
        3. 为不同功能类型分别建立索引
    """
    from scipy.spatial import KDTree

    # 构建全体卫星索引
    if self._sat_positions:
        positions_cartesian = []
        sat_ids = []
        for sat_id, (lat, lon, alt) in self._sat_positions.items():
            x, y, z = geo_util.lla_to_ecef(lat, lon, alt)
            positions_cartesian.append([x, y, z])
            sat_ids.append(sat_id)

        self._sat_kdtree = KDTree(positions_cartesian)
        self._sat_ids_list = sat_ids

    # 构建中继卫星索引
    if self._relay_sat_positions:
        relay_positions_cartesian = []
        relay_sat_ids = []
        for sat_id, (lat, lon, alt) in self._relay_sat_positions.items():
            x, y, z = geo_util.lla_to_ecef(lat, lon, alt)
            relay_positions_cartesian.append([x, y, z])
            relay_sat_ids.append(sat_id)

        self._relay_sat_kdtree = KDTree(relay_positions_cartesian)
        self._relay_sat_ids_list = relay_sat_ids

    # 类似地构建计算和感知卫星索引...
```

#### 可见性计算
```python
def getNearestVisibleSatInfos(self, target_position, K=None, D=None, function_type=None):
    """
    获取目标位置附近可见的卫星信息

    Args:
        target_position (tuple): 目标位置 (lat, lon, alt)
        K (int): 返回最近的K颗卫星
        D (float): 搜索半径(km)
        function_type (FunctionEnum): 卫星功能类型过滤

    Returns:
        dict: {sat_id: sat_info} 可见卫星信息字典

    功能:
        1. 根据功能类型选择对应的KD树
        2. 执行邻近搜索
        3. 计算仰角和距离
        4. 过滤不满足条件的卫星
    """
    # 选择对应的KD树和位置字典
    if function_type == FunctionEnum.RELAY:
        kdtree = self._relay_sat_kdtree
        positions = self._relay_sat_positions
        sat_ids_list = self._relay_sat_ids_list
    elif function_type == FunctionEnum.COMPUTATION:
        kdtree = self._comp_sat_kdtree
        positions = self._comp_sat_positions
        sat_ids_list = self._comp_sat_ids_list
    elif function_type == FunctionEnum.SENSING:
        kdtree = self._sensing_sat_kdtree
        positions = self._sensing_sat_positions
        sat_ids_list = self._sensing_sat_ids_list
    else:
        kdtree = self._sat_kdtree
        positions = self._sat_positions
        sat_ids_list = self._sat_ids_list

    if kdtree is None:
        return {}

    # 转换目标位置到笛卡尔坐标
    target_lat, target_lon, target_alt = target_position
    target_x, target_y, target_z = geo_util.lla_to_ecef(target_lat, target_lon, target_alt)
    target_cartesian = [target_x, target_y, target_z]

    # 执行邻近搜索
    if D is not None:
        # 半径搜索
        D_meters = D * 1000  # 转换为米
        indices = kdtree.query_ball_point(target_cartesian, D_meters)
        if K is not None:
            # 限制返回数量
            distances = kdtree.query(target_cartesian, k=len(indices))[0]
            sorted_indices = sorted(zip(distances, indices))[:K]
            indices = [idx for _, idx in sorted_indices]
    else:
        # K近邻搜索
        if K is None:
            K = len(sat_ids_list)
        distances, indices = kdtree.query(target_cartesian, k=min(K, len(sat_ids_list)))
        if not isinstance(indices, (list, np.ndarray)):
            indices = [indices]

    # 构建结果字典并进行可见性检查
    visible_sats = {}
    for idx in indices:
        sat_id = sat_ids_list[idx]
        sat_position = positions[sat_id]

        # 计算仰角
        elevation = geo_util.calculate_elevation_angle(target_position, sat_position)

        # 检查是否满足最小仰角要求
        if elevation >= self._elevation_mask_angle:
            # 计算距离
            distance = geo_util.calculate_distance(target_position, sat_position)

            visible_sats[sat_id] = {
                'position': sat_position,
                'elevation': elevation,
                'distance': distance
            }

    return visible_sats
```

#### 中国过境计算
```python
def _calculateChinaTransit(self):
    """
    计算过境中国的卫星

    功能:
        1. 遍历所有卫星位置
        2. 检查是否在中国领土范围内
        3. 更新过境卫星列表
    """
    self._china_transit_sat_ids.clear()

    for sat_id, (lat, lon, alt) in self._sat_positions.items():
        # 使用地理工具检查是否在中国境内
        if geo_util.is_in_china(lat, lon):
            self._china_transit_sat_ids.append(sat_id)
```

#### 更新方法
```python
def update(self, simulation_time, satellites, ground_stations):
    """
    更新视野管理器状态

    Args:
        simulation_time (float): 当前仿真时间
        satellites (dict): 卫星节点字典
        ground_stations (dict): 地面站节点字典

    功能:
        1. 更新卫星位置
        2. 重建空间索引
        3. 重新计算可见性
        4. 导出视野数据(如果启用)
    """
    if self._mode == 'real':
        self._updateSatellitePositions(satellites)
        self._buildSpatialIndex()
        self._calculateChinaTransit()

        # 计算地面站-卫星可见性
        self._calculateGroundStationVisibility(ground_stations)

    elif self._mode == 'stored':
        self.__setViewState(simulation_time)

    # 导出数据
    if self._export:
        self._view_serializer.add(simulation_time, {
            'sat_positions': self._sat_positions,
            'china_transit_sat_ids': self._china_transit_sat_ids
        })
```

#### 信息获取方法
```python
def getSatIdsTransitChina(self):
    """
    获取过境中国的卫星ID列表

    Returns:
        list: 卫星ID列表
    """
    return self._china_transit_sat_ids.copy()

def getTransitSatInfos(self):
    """
    获取过境卫星的详细信息

    Returns:
        dict: {sat_id: sat_info} 过境卫星信息字典
    """
    transit_infos = {}
    for sat_id in self._china_transit_sat_ids:
        if sat_id in self._sat_positions:
            transit_infos[sat_id] = {
                'position': self._sat_positions[sat_id],
                'is_transit_china': True
            }
    return transit_infos
```

## GridManager 网格管理器

**文件位置**: `spacetasksim/manager/grid_manager.py`

GridManager负责管理地理网格系统，用于空间划分和区域管理。

### 核心方法

```python
def __init__(self, config_grid):
    """
    初始化网格管理器

    Args:
        config_grid (dict): 网格配置字典

    功能:
        1. 加载网格数据
        2. 建立网格索引
        3. 设置网格参数
    """

def getAllGrids(self):
    """
    获取所有网格信息

    Returns:
        dict: 网格信息字典
    """

def getGridByPosition(self, lat, lon):
    """
    根据位置获取对应的网格

    Args:
        lat (float): 纬度
        lon (float): 经度

    Returns:
        dict: 网格信息
    """
```

## 资源管理器子模块

### CommunicationManager 通信资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/communication_manager.py`

#### 主要功能
- 管理卫星和地面站的通信资源
- 分配和释放通信波束
- 计算通信链路质量
- 管理带宽和功率资源

#### 核心方法
```python
def allocateResource(self, step_id, tx_node_id, rx_node_id, required_bandwidth):
    """分配通信资源"""

def releaseResource(self, step_id):
    """释放通信资源"""

def getResourceUsage(self, node_id):
    """获取节点通信资源使用情况"""

def checkResourceAvailable(self, tx_node_id, rx_node_id, required_bandwidth):
    """检查通信资源可用性"""
```

### ComputationManager 计算资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/computation_manager.py`

#### 主要功能
- 管理节点的计算资源
- 分配和释放CPU资源
- 监控计算负载
- 计算任务执行时间

#### 核心方法
```python
def allocateResource(self, step_id, node_id, required_compute_size):
    """分配计算资源"""

def releaseResource(self, step_id):
    """释放计算资源"""

def getResourceUsage(self, node_id):
    """获取节点计算资源使用情况"""
```

### SensingManager 感知资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/sensing_manager.py`

#### 主要功能
- 管理卫星的感知资源
- 分配和释放相机资源
- 计算感知精度和时间
- 管理感知任务调度

### StorageManager 存储资源管理器

**文件位置**: `spacetasksim/manager/resource_managers/storage_manager.py`

#### 主要功能
- 管理节点的存储资源
- 分配和释放存储空间
- 监控存储使用情况
- 处理数据存储和传输

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 实体类的完整实现
- 任务类和步骤类的详细API
- 枚举类的完整定义

请查看《SpaceTaskSim_详细代码文档_05_实体类与任务类.md》获取更多详细信息。

# SpaceTaskSim 详细代码文档 - 第5部分：实体类与任务类

## 目录
- [实体类详解](#实体类详解)
- [任务类详解](#任务类详解)
- [步骤类详解](#步骤类详解)
- [抽象基类](#抽象基类)

## 实体类详解

### Satellite 卫星类

**文件位置**: `spacetasksim/entity/satellite.py`

Satellite类继承自ComputeNode、RelayNode、SenseNode，是功能最全面的节点类型。

#### 类继承关系
```python
class Satellite(ComputeNode, RelayNode, SenseNode):
    """
    卫星类 - 多重继承实现多种功能

    继承关系:
    - ComputeNode: 提供计算功能
    - RelayNode: 提供中继通信功能
    - SenseNode: 提供遥感功能
    """
```

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_node_type` | NodeTypeEnum | 节点类型，固定为NodeTypeEnum.SAT |
| `_orbit_type` | OrbitEnum | 轨道类型(LEO/MEO/GEO/SSO) |
| `_generation_time` | float | 节点生成时间 |
| `_last_position_update_time` | float | 最后位置更新时间 |
| `_last_profile_update_time` | float | 最后配置更新时间 |
| `_function_enum_list` | list | 功能列表 |
| `_calculate_obj` | EarthSatellite | ephem计算对象 |
| `_name` | str | 卫星名称 |
| `_v_vector` | tuple | 速度向量(ECEF坐标系) |

#### 构造函数
```
__init__(simulation_time, id, orbit_type, latitude, longitude, altitude, v_vector, function_enum_list, calculate_obj, compute_profile=None, relay_profile=None, sense_profile=None, name=None)
功能描述：初始化卫星节点，设置轨道、功能和资源配置
参数：
  - simulation_time (float): 仿真时间
  - id (str): 节点唯一ID
  - orbit_type (OrbitEnum): 轨道类型枚举
  - latitude (float): 纬度
  - longitude (float): 经度
  - altitude (float): 高度(km)
  - v_vector (tuple): 速度向量(ECEF坐标系)
  - function_enum_list (list): 功能枚举列表
  - calculate_obj (EarthSatellite): ephem计算对象
  - compute_profile (dict): 计算配置，可选
  - relay_profile (dict): 中继配置，可选
  - sense_profile (dict): 感知配置，可选
  - name (str): 卫星名称，可选
返回值：无
```
```

#### 核心方法

##### 基本信息获取
```
getNodeId()
功能描述：获取节点的唯一标识符
参数：无
返回值：str - 节点ID

getNodeType()
功能描述：获取节点类型
参数：无
返回值：NodeTypeEnum - 节点类型枚举

getOrbitType()
功能描述：获取卫星轨道类型
参数：无
返回值：OrbitEnum - 轨道类型枚举

getFunctionEnumList()
功能描述：获取节点支持的功能列表
参数：无
返回值：list - 功能枚举列表

getName()
功能描述：获取卫星名称
参数：无
返回值：str - 卫星名称

getVelocityVector()
功能描述：获取卫星在ECEF坐标系下的速度向量
参数：无
返回值：tuple - 速度向量(vx, vy, vz)
```

##### 时间信息获取
```python
def getGenerationTime(self):
    """获取节点生成时间"""
    return self._generation_time

def getLastPositionUpdateTime(self):
    """获取最后位置更新时间"""
    return self._last_position_update_time

def getLastProfileUpdateTime(self, profile_type: FunctionEnum = None):
    """
    获取最后配置更新时间

    Args:
        profile_type (FunctionEnum): 配置类型
            - FunctionEnum.COMPUTATION: 计算配置
            - FunctionEnum.RELAY: 中继配置
            - FunctionEnum.SENSING: 感知配置
            - None: 返回通用更新时间

    Returns:
        float: 更新时间
    """
    assert profile_type is None or profile_type in self._function_enum_list

    if profile_type is None:
        return self._last_profile_update_time
    elif profile_type == FunctionEnum.COMPUTATION:
        return self._last_compute_profile_update_time
    elif profile_type == FunctionEnum.RELAY:
        return self._last_relay_profile_update_time
    elif profile_type == FunctionEnum.SENSING:
        return self._last_sense_profile_update_time
    else:
        return None
```

##### 位置和状态更新
```python
def getPosition(self):
    """
    获取卫星位置

    Returns:
        tuple: (latitude, longitude, altitude) 位置信息
    """
    return self._getPosition()

def updatePosition(self, simulation_time, latitude, longitude, altitude):
    """
    更新卫星位置

    Args:
        simulation_time (float): 仿真时间
        latitude (float): 纬度
        longitude (float): 经度
        altitude (float): 高度
    """
    self._setPosition(simulation_time, latitude, longitude, altitude)
    self._last_position_update_time = simulation_time

def updateVelocity(self, simulation_time, v_vector):
    """
    更新卫星速度

    Args:
        simulation_time (float): 仿真时间
        v_vector (tuple): 速度向量(ECEF坐标系)
    """
    self._v_vector = v_vector

def updateProfile(self, simulation_time, compute_profile=None, relay_profile=None, sense_profile=None):
    """
    更新卫星配置

    Args:
        simulation_time (float): 仿真时间
        compute_profile (dict): 计算配置
        relay_profile (dict): 中继配置
        sense_profile (dict): 感知配置
    """
    if compute_profile is not None:
        self._setComputeProfile(simulation_time, compute_profile)
        self._last_compute_profile_update_time = simulation_time
    if relay_profile is not None:
        self._setRelayProfile(simulation_time, relay_profile)
        self._last_relay_profile_update_time = simulation_time
    if sense_profile is not None:
        self._setSenseProfile(simulation_time, sense_profile)
        self._last_sense_profile_update_time = simulation_time

    self._last_profile_update_time = simulation_time
```

##### 功能检查
```python
def hasFunction(self, function_enum: FunctionEnum):
    """
    检查卫星是否具有指定功能

    Args:
        function_enum (FunctionEnum): 功能枚举

    Returns:
        bool: True表示具有该功能
    """
    return function_enum in self._function_enum_list
```

### GroundStation 地面站类

**文件位置**: `spacetasksim/entity/ground_station.py`

GroundStation类继承自TaskGenNode，主要负责任务生成。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_node_type` | NodeTypeEnum | 节点类型，固定为NodeTypeEnum.GS |
| `_province_zh` | str | 省份名称(中文) |
| `_province_en` | str | 省份名称(英文) |
| `_city_zh` | str | 城市名称(中文) |
| `_city_en` | str | 城市名称(英文) |
| `_name_zh` | str | 节点名称(中文) |
| `_name_en` | str | 节点名称(英文) |

#### 构造函数
```python
def __init__(self, simulation_time, id, latitude, longitude, altitude,
             province_zh, province_en, city_zh, city_en, function_enum_list,
             task_generation_profile=None):
    """
    地面站构造函数

    Args:
        simulation_time (float): 仿真时间
        id (str): 节点ID
        latitude (float): 纬度
        longitude (float): 经度
        altitude (float): 高度
        province_zh (str): 省份名称(中文)
        province_en (str): 省份名称(英文)
        city_zh (str): 城市名称(中文)
        city_en (str): 城市名称(英文)
        function_enum_list (list): 功能列表
        task_generation_profile (dict): 任务生成配置
    """
    TaskGenNode.__init__(self, simulation_time, id, latitude, longitude, altitude, task_generation_profile)

    self._node_type = NodeTypeEnum.GS
    self._generation_time = simulation_time
    self._last_position_update_time = simulation_time
    self._last_profile_update_time = simulation_time
    self._function_enum_list = function_enum_list
    self._province_zh = province_zh
    self._province_en = province_en
    self._city_zh = city_zh
    self._city_en = city_en
    self._name_zh = f"{city_zh}_{province_zh}"
    self._name_en = f"{city_en}_{province_en}"
```

#### 核心方法

##### 地理信息获取
```python
def getProvinceZh(self):
    """获取省份名称(中文)"""
    return self._province_zh

def getProvinceEn(self):
    """获取省份名称(英文)"""
    return self._province_en

def getCityZh(self):
    """获取城市名称(中文)"""
    return self._city_zh

def getCityEn(self):
    """获取城市名称(英文)"""
    return self._city_en

def getNameZh(self):
    """获取节点名称(中文)"""
    return self._name_zh

def getNameEn(self):
    """获取节点名称(英文)"""
    return self._name_en
```

### MobileUser 移动用户类

**文件位置**: `spacetasksim/entity/mobile_user.py`

MobileUser类也继承自TaskGenNode，代表移动终端用户。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_node_type` | NodeTypeEnum | 节点类型，固定为NodeTypeEnum.MU |
| `_name` | str | 用户名称 |

#### 构造函数
```python
def __init__(self, simulation_time, id, latitude, longitude, altitude,
             function_enum_list, task_generation_profile=None, name=None):
    """
    移动用户构造函数

    Args:
        simulation_time (float): 仿真时间
        id (str): 节点ID
        latitude (float): 纬度
        longitude (float): 经度
        altitude (float): 高度
        function_enum_list (list): 功能列表
        task_generation_profile (dict): 任务生成配置
        name (str): 用户名称
    """
    TaskGenNode.__init__(self, simulation_time, id, latitude, longitude, altitude, task_generation_profile)

    self._node_type = NodeTypeEnum.MU
    self._generation_time = simulation_time
    self._last_position_update_time = simulation_time
    self._last_profile_update_time = simulation_time
    self._function_enum_list = function_enum_list
    self._name = name
```

## 任务类详解

### SimpleTask 基础任务类

**文件位置**: `spacetasksim/task/simple_task.py`

SimpleTask是所有任务类的基类，定义了任务的基本属性和状态管理。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_task_id` | str | 任务唯一ID |
| `_init_node_id` | str | 初始节点ID |
| `_arrival_time` | float | 任务到达时间 |
| `_ttl` | float | 任务生存时间 |
| `_deadline` | float | 任务截止时间 |
| `_start_time` | float | 任务开始时间 |
| `_finish_time` | float | 任务完成时间 |
| `_fail_time` | float | 任务失败时间 |
| `_state` | StateEnum | 任务状态 |
| `_all_steps` | dict | 所有步骤字典 |
| `_todo_steps` | dict | 待执行步骤字典 |
| `_finished_steps` | dict | 已完成步骤字典 |
| `_failed_steps` | dict | 已失败步骤字典 |

#### 构造函数
```python
def __init__(self, task_id, init_node_id, arrival_time, ttl, deadline, steps):
    """
    基础任务构造函数

    Args:
        task_id (str): 任务ID
        init_node_id (str): 初始节点ID
        arrival_time (float): 到达时间
        ttl (float): 生存时间
        deadline (float): 截止时间
        steps (list): 步骤列表
    """
    self._task_id = task_id
    self._init_node_id = init_node_id
    self._arrival_time = arrival_time
    self._ttl = ttl
    self._deadline = deadline
    self._start_time = None
    self._last_execute_time = None
    self._finish_time = None
    self._fail_time = None
    self._state = StateEnum.INITIALIZED

    # 初始化步骤字典
    self._all_steps = {step.step_id: step for step in steps}
    self._all_step_ids = [step.step_id for step in steps]
    self._todo_steps = {step.step_id: step for step in steps}
    self._finished_steps = {}
    self._finished_step_ids = []
    self._failed_steps = {}
    self._failed_step_ids = []
```

#### 核心方法

##### 基本信息获取
```python
def getTaskId(self):
    """获取任务ID"""
    return self._task_id

def getInitNodeId(self):
    """获取初始节点ID"""
    return self._init_node_id

def getArrivalTime(self):
    """获取到达时间"""
    return self._arrival_time

def getTTL(self):
    """获取生存时间"""
    return self._ttl

def getDeadline(self):
    """获取截止时间"""
    return self._deadline

def getState(self):
    """获取任务状态"""
    return self._state
```

##### 时间信息获取
```python
def getStartTime(self):
    """获取开始时间"""
    return self._start_time

def getFinishTime(self):
    """获取完成时间"""
    return self._finish_time

def getFailTime(self):
    """获取失败时间"""
    return self._fail_time

def getLastExecuteTime(self):
    """获取最后执行时间"""
    return self._last_execute_time
```

##### 步骤管理
```python
def getAllSteps(self):
    """获取所有步骤"""
    return self._all_steps

def getTodoSteps(self):
    """获取待执行步骤"""
    return self._todo_steps

def getFinishedSteps(self):
    """获取已完成步骤"""
    return self._finished_steps

def getFailedSteps(self):
    """获取已失败步骤"""
    return self._failed_steps

def getAllStepIds(self):
    """获取所有步骤ID"""
    return self._all_step_ids

def getFinishedStepIds(self):
    """获取已完成步骤ID"""
    return self._finished_step_ids

def getFailedStepIds(self):
    """获取已失败步骤ID"""
    return self._failed_step_ids
```

##### 状态更新
```python
def _start(self, current_time):
    """
    启动任务

    Args:
        current_time (float): 当前时间
    """
    if self._state == StateEnum.INITIALIZED:
        self._state = StateEnum.EXECUTING
        self._start_time = current_time
        self._last_execute_time = current_time

def _finish(self, current_time):
    """
    完成任务

    Args:
        current_time (float): 当前时间
    """
    if self._state == StateEnum.EXECUTING:
        self._state = StateEnum.FINISHED
        self._finish_time = current_time

def _fail(self, current_time):
    """
    任务失败

    Args:
        current_time (float): 当前时间
    """
    if self._state in [StateEnum.INITIALIZED, StateEnum.EXECUTING]:
        self._state = StateEnum.FAILED
        self._fail_time = current_time
```

##### 步骤状态更新
```python
def _finishStep(self, step_id, current_time):
    """
    完成步骤

    Args:
        step_id (str): 步骤ID
        current_time (float): 当前时间
    """
    if step_id in self._todo_steps:
        step = self._todo_steps.pop(step_id)
        self._finished_steps[step_id] = step
        self._finished_step_ids.append(step_id)

        # 检查是否所有步骤都完成
        if len(self._todo_steps) == 0:
            self._finish(current_time)

def _failStep(self, step_id, current_time):
    """
    步骤失败

    Args:
        step_id (str): 步骤ID
        current_time (float): 当前时间
    """
    if step_id in self._todo_steps:
        step = self._todo_steps.pop(step_id)
        self._failed_steps[step_id] = step
        self._failed_step_ids.append(step_id)

        # 步骤失败导致任务失败
        self._fail(current_time)
```

##### 状态检查
```python
def isFinished(self):
    """检查任务是否完成"""
    return self._state == StateEnum.FINISHED

def isFailed(self):
    """检查任务是否失败"""
    return self._state == StateEnum.FAILED

def isExecuting(self):
    """检查任务是否正在执行"""
    return self._state == StateEnum.EXECUTING

def isExpired(self, current_time):
    """
    检查任务是否过期

    Args:
        current_time (float): 当前时间

    Returns:
        bool: True表示任务已过期
    """
    return current_time > self._deadline
```

### CommunicateTask 通信任务类

**文件位置**: `spacetasksim/task/communicate_task.py`

CommunicateTask继承自SimpleTask，专门处理通信任务。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_task_type` | TaskTypeEnum | 任务类型，固定为COMMUNICATION |
| `_tx_node_id` | str | 发送节点ID |
| `_rx_node_id` | str | 接收节点ID |
| `_data_size` | float | 数据大小(MB) |

#### 构造函数
```python
def __init__(self, task_id, init_node_id, arrival_time, ttl, deadline,
             tx_node_id, rx_node_id, data_size, step_list):
    """
    通信任务构造函数

    Args:
        task_id (str): 任务ID
        init_node_id (str): 初始节点ID
        arrival_time (float): 到达时间
        ttl (float): 生存时间
        deadline (float): 截止时间
        tx_node_id (str): 发送节点ID
        rx_node_id (str): 接收节点ID
        data_size (float): 数据大小(MB)
        step_list (list): 步骤列表
    """
    SimpleTask.__init__(self, task_id, init_node_id, arrival_time, ttl, deadline, step_list)

    self._task_type = TaskTypeEnum.COMMUNICATION
    self._tx_node_id = tx_node_id
    self._rx_node_id = rx_node_id
    self._data_size = data_size
```

#### 核心方法
```python
@property
def task_type(self):
    """获取任务类型"""
    return self._task_type

def getTxNodeId(self):
    """获取发送节点ID"""
    return self._tx_node_id

def getRxNodeId(self):
    """获取接收节点ID"""
    return self._rx_node_id

def getDataSize(self):
    """获取数据大小"""
    return self._data_size
```

### ComputeTask 计算任务类

**文件位置**: `spacetasksim/task/compute_task.py`

ComputeTask继承自SimpleTask，专门处理计算任务。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_task_type` | TaskTypeEnum | 任务类型，固定为COMPUTATION |
| `_compute_size` | float | 计算量大小(MB) |
| `_required_cpu_cycles` | float | 所需CPU周期数 |

### SenseTask 感知任务类

**文件位置**: `spacetasksim/task/sense_task.py`

SenseTask继承自SimpleTask，专门处理感知任务。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `_task_type` | TaskTypeEnum | 任务类型，固定为SENSING |
| `_target_position` | tuple | 目标位置(lat, lon, alt) |
| `_required_accuracy` | float | 所需精度 |
| `_sensing_time` | float | 感知时间 |
| `_data_size` | float | 生成数据大小(MB) |

## 步骤类详解

### Step 基础步骤类

**文件位置**: `spacetasksim/task/step.py`

Step类定义了任务执行的基本单元。

#### 类属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `step_id` | str | 步骤唯一ID |
| `task_id` | str | 所属任务ID |
| `step_type` | StepTypeEnum | 步骤类型 |
| `node_id` | str | 执行节点ID |
| `state` | StateEnum | 步骤状态 |
| `start_time` | float | 开始时间 |
| `finish_time` | float | 完成时间 |
| `fail_time` | float | 失败时间 |

#### 核心方法

```python
def _start(self, current_time):
    """启动步骤"""
    self.state = StateEnum.EXECUTING
    self.start_time = current_time

def _finish(self, current_time):
    """完成步骤"""
    self.state = StateEnum.FINISHED
    self.finish_time = current_time

def _fail(self, current_time):
    """步骤失败"""
    self.state = StateEnum.FAILED
    self.fail_time = current_time

def isFinished(self):
    """检查是否完成"""
    return self.state == StateEnum.FINISHED

def isFailed(self):
    """检查是否失败"""
    return self.state == StateEnum.FAILED

def isExecuting(self):
    """检查是否正在执行"""
    return self.state == StateEnum.EXECUTING
```

### CommunicateStep 通信步骤类

通信步骤的特有属性：
- `required_trans_size`: 所需传输大小
- `to_trans_route`: 传输路由
- `allocated_bandwidth`: 分配的带宽

### ComputeStep 计算步骤类

计算步骤的特有属性：
- `required_compute_size`: 所需计算量
- `allocated_cpu_frequency`: 分配的CPU频率
- `estimated_finish_time`: 预计完成时间

### SenseStep 感知步骤类

感知步骤的特有属性：
- `target_position`: 目标位置
- `required_accuracy`: 所需精度
- `required_sensing_time`: 所需感知时间
- `allocated_camera_id`: 分配的相机ID

## 抽象基类

### BaseNode 节点基类

**文件位置**: `spacetasksim/entity/abstract/base_node.py`

所有节点类的基类，定义了节点的基本属性和方法。

#### 核心方法
```python
def _getPosition(self):
    """获取节点位置"""
    return (self._latitude, self._longitude, self._altitude)

def _setPosition(self, simulation_time, latitude, longitude, altitude):
    """设置节点位置"""
    self._latitude = latitude
    self._longitude = longitude
    self._altitude = altitude
```

### ComputeNode 计算节点基类

**文件位置**: `spacetasksim/entity/abstract/compute_node.py`

提供计算功能的节点基类。

### RelayNode 中继节点基类

**文件位置**: `spacetasksim/entity/abstract/relay_node.py`

提供中继通信功能的节点基类。

### SenseNode 感知节点基类

**文件位置**: `spacetasksim/entity/abstract/sense_node.py`

提供遥感功能的节点基类。

### TaskGenNode 任务生成节点基类

**文件位置**: `spacetasksim/entity/abstract/task_gen_node.py`

提供任务生成功能的节点基类。

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 所有枚举类的完整定义
- 调度器模块的详细实现
- 工具类的完整API说明

请查看《SpaceTaskSim_详细代码文档_06_枚举类与调度器.md》获取更多详细信息。

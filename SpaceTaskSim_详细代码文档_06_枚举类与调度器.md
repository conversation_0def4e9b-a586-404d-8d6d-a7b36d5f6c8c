# SpaceTaskSim 详细代码文档 - 第6部分：枚举类与调度器

## 目录
- [枚举类详解](#枚举类详解)
- [调度器模块详解](#调度器模块详解)
- [SpaceTaskSimScheduler 调度器接口](#spacetasksimscheduler-调度器接口)

## 枚举类详解

### NodeTypeEnum 节点类型枚举

**文件位置**: `spacetasksim/enum/node_type_enum.py`

定义了系统中所有节点类型的枚举。

#### 枚举值定义
```python
class NodeTypeEnum(Enum):
    GS = 1   # ground station 地面站节点
    MU = 2   # mobile user 移动用户节点
    SAT = 3  # satellite 卫星节点
```

#### 工具方法
```
getNameByCode(code)
功能描述：根据枚举值获取对应的枚举名称
参数：
  - code (int): 枚举值
返回值：str - 枚举名称
异常：ValueError - 未知的枚举值
```

@staticmethod
def getNameByEnum(code: Enum):
    """
    根据枚举获取枚举名称

    Args:
        code (Enum): 枚举对象

    Returns:
        str: 枚举名称
    """
    if code == NodeTypeEnum.GS:
        return 'GroundStation'
    elif code == NodeTypeEnum.MU:
        return 'MobileUser'
    elif code == NodeTypeEnum.SAT:
        return 'Satellite'
    else:
        raise ValueError(f"Unknown code: {code}")
```

### OrbitEnum 轨道类型枚举

**文件位置**: `spacetasksim/enum/orbit_enum.py`

定义了卫星轨道类型的枚举。

#### 枚举值定义
```python
class OrbitEnum(Enum):
    LEO = 1  # Low Earth orbit 低地球轨道（400-2000km）
    MEO = 2  # Medium Earth Orbit 中地球轨道（2000-35786km）
    GEO = 3  # Geostationary Orbit 地球静止同步轨道（35786km，赤道面）
    SSO = 4  # Sun-synchronous Orbit 太阳同步轨道（常见600-800km，倾角约98°的逆行轨道）
```

#### 工具方法
```python
@staticmethod
def getNameByCode(code: int):
    """根据枚举值获取轨道名称"""
    mapping = {
        OrbitEnum.LEO.value: 'LEO',
        OrbitEnum.MEO.value: 'MEO',
        OrbitEnum.GEO.value: 'GEO',
        OrbitEnum.SSO.value: 'SSO'
    }
    if code in mapping:
        return mapping[code]
    else:
        raise ValueError(f"Unknown code: {code}")

@staticmethod
def getEnumByName(name: str):
    """
    根据轨道名称获取枚举

    Args:
        name (str): 轨道名称 ('LEO', 'MEO', 'GEO', 'SSO')

    Returns:
        OrbitEnum: 对应的轨道枚举
    """
    mapping = {
        'LEO': OrbitEnum.LEO,
        'MEO': OrbitEnum.MEO,
        'GEO': OrbitEnum.GEO,
        'SSO': OrbitEnum.SSO
    }
    if name in mapping:
        return mapping[name]
    else:
        raise ValueError(f"Unknown enum name: {name}")
```

### TaskTypeEnum 任务类型枚举

**文件位置**: `spacetasksim/enum/task_type_enum.py`

定义了系统中所有任务类型的枚举。

#### 枚举值定义
```python
class TaskTypeEnum(Enum):
    COMPUTATION = 1    # 计算任务
    COMMUNICATION = 2  # 通信任务
    SENSING = 3        # 遥感任务
```

#### 工具方法
```python
@staticmethod
def getNameByCode(code: int):
    """根据枚举值获取任务类型名称"""
    mapping = {
        TaskTypeEnum.COMPUTATION.value: 'Computation',
        TaskTypeEnum.COMMUNICATION.value: 'Communication',
        TaskTypeEnum.SENSING.value: 'Sensing'
    }
    if code in mapping:
        return mapping[code]
    else:
        raise ValueError(f"Unknown code: {code}")
```

### StepTypeEnum 步骤类型枚举

**文件位置**: `spacetasksim/enum/step_type_enum.py`

定义了任务步骤类型的枚举。

#### 枚举值定义
```python
class StepTypeEnum(Enum):
    COMPUTATION = 1    # 计算步骤
    COMMUNICATION = 2  # 通信步骤
    SENSING = 3        # 遥感步骤
```

### FunctionEnum 功能类型枚举

**文件位置**: `spacetasksim/enum/function_enum.py`

定义了节点功能类型的枚举。

#### 枚举值定义
```python
class FunctionEnum(Enum):
    COMPUTATION = 1  # 计算功能
    RELAY = 2        # 中继功能
    SENSING = 3      # 遥感功能
    TASK_GEN = 4     # 任务生成功能
```

#### 工具方法
```python
@staticmethod
def getEnumByNameList(name_list: list):
    """
    根据功能名称列表获取枚举列表

    Args:
        name_list (list): 功能名称列表

    Returns:
        list: 功能枚举列表
    """
    enum_list = []
    mapping = {
        'Computation': FunctionEnum.COMPUTATION,
        'Relay': FunctionEnum.RELAY,
        'Sensing': FunctionEnum.SENSING,
        'Task_Gen': FunctionEnum.TASK_GEN
    }

    for name in name_list:
        if name in mapping:
            enum_list.append(mapping[name])
        else:
            raise ValueError(f"Unknown function name: {name}")

    return enum_list
```

### StateEnum 状态枚举

**文件位置**: `spacetasksim/enum/state_enum.py`

定义了任务和步骤的状态枚举。

#### 枚举值定义
```python
class StateEnum(Enum):
    NOT_INIT = 0     # 未初始化
    INITIALIZED = 1  # 已初始化
    EXECUTING = 2    # 正在执行
    FINISHED = 3     # 已完成
    FAILED = -1      # 已失败
```

### LinkEnum 链路类型枚举

**文件位置**: `spacetasksim/enum/link_enum.py`

定义了通信链路类型的枚举。

#### 枚举值定义
```python
class LinkEnum(Enum):
    G2S = 1  # 信关站-卫星信道
    S2G = 2  # 卫星-信关站信道
    S2S = 3  # 星间信道
```

### FailResultEnum 失败结果枚举

**文件位置**: `spacetasksim/enum/fail_result_enum.py`

定义了不同类型任务的失败原因枚举。

#### 通信任务失败原因
```python
class CommResult(Enum):
    NOT_ALLOCATE = 0      # 未分配资源
    FIRST_TRANS_FAIL = 1  # 第一跳传输失败
    SECOND_TRANS_FAIL = 2 # 第二跳传输失败
```

#### 计算任务失败原因
```python
class CompResult(Enum):
    NOT_ALLOCATE = 0  # 未分配资源
    TRANS_FAIL = 1    # 传输失败
    COMP_FAIL = 2     # 计算失败
```

#### 感知任务失败原因
```python
class SenseResult(Enum):
    NOT_ALLOCATE = 0  # 未分配资源
    TRANS_FAIL = 1    # 传输失败
    SENSE_FAIL = 2    # 感知失败
```

## 调度器模块详解

### BaseScheduler 调度器基类

**文件位置**: `spacetasksim/scheduler/base_scheduler.py`

所有调度器的基类，定义了调度器的基本接口。

```python
class BaseScheduler:
    """
    调度器基类

    提供调度器的基本接口和通用方法
    """

    def __init__(self):
        """初始化调度器"""
        pass

    @staticmethod
    def schedule():
        """
        调度方法的基本接口

        子类需要重写此方法实现具体的调度逻辑
        """
        raise NotImplementedError("Subclasses must implement schedule method")
```

### ResourceScheduler 资源调度器

**文件位置**: `spacetasksim/scheduler/resource_scheduler.py`

负责资源分配和检查的调度器。

#### 核心方法

##### 通信资源检查
```
checkCommunicationResourcesAvailable(env, tx_node_id, rx_node_id, required_trans_size, beam_usage_cache)
功能描述：检查通信资源是否可用于指定的传输任务
参数：
  - env (SpaceTaskSimEnv): 仿真环境对象
  - tx_node_id (str): 发送节点ID
  - rx_node_id (str): 接收节点ID
  - required_trans_size (float): 所需传输大小
  - beam_usage_cache (dict): 波束使用缓存
返回值：bool - True表示资源可用
```

    # 检查发送节点资源
    tx_beam_usage = beam_usage_cache.get(tx_node_id, {})
    tx_available = env.resource_manager.communication_manager.checkTransmitResourceAvailable(
        tx_node_id, tx_node_type, rx_node_type, tx_beam_usage
    )

    # 检查接收节点资源
    rx_beam_usage = beam_usage_cache.get(rx_node_id, {})
    rx_available = env.resource_manager.communication_manager.checkReceiveResourceAvailable(
        rx_node_id, rx_node_type, tx_node_type, rx_beam_usage
    )

    return tx_available and rx_available
```

##### 计算资源检查
```python
@staticmethod
def checkComputationResourcesAvailable(env: SpaceTaskSimEnv, node_id, required_compute_size,
                                     comp_usage_cache):
    """
    检查计算资源可用性

    Args:
        env (SpaceTaskSimEnv): 环境对象
        node_id (str): 节点ID
        required_compute_size (float): 所需计算量
        comp_usage_cache (dict): 计算资源使用缓存

    Returns:
        bool: True表示资源可用
    """
    comp_usage = comp_usage_cache.get(node_id, {})
    return env.resource_manager.computation_manager.checkResourceAvailable(
        node_id, required_compute_size, comp_usage
    )
```

##### 感知资源检查
```python
@staticmethod
def checkSensingResourcesAvailable(env: SpaceTaskSimEnv, node_id, required_accuracy,
                                 required_sensing_time, sensing_usage_cache):
    """
    检查感知资源可用性

    Args:
        env (SpaceTaskSimEnv): 环境对象
        node_id (str): 节点ID
        required_accuracy (float): 所需精度
        required_sensing_time (float): 所需感知时间
        sensing_usage_cache (dict): 感知资源使用缓存

    Returns:
        bool: True表示资源可用
    """
    sensing_usage = sensing_usage_cache.get(node_id, {})
    return env.resource_manager.sensing_manager.checkResourceAvailable(
        node_id, required_accuracy, required_sensing_time, sensing_usage
    )
```

##### 存储资源检查
```python
@staticmethod
def checkStorageResourcesAvailable(env: SpaceTaskSimEnv, node_id, required_storage_size,
                                 storage_usage_cache):
    """
    检查存储资源可用性

    Args:
        env (SpaceTaskSimEnv): 环境对象
        node_id (str): 节点ID
        required_storage_size (float): 所需存储大小
        storage_usage_cache (dict): 存储资源使用缓存

    Returns:
        bool: True表示资源可用
    """
    storage_usage = storage_usage_cache.get(node_id, {})
    return env.resource_manager.storage_manager.checkResourceAvailable(
        node_id, required_storage_size, storage_usage
    )
```

### TaskScheduler 任务调度器

**文件位置**: `spacetasksim/scheduler/task_scheduler.py`

负责任务和步骤调度的调度器。

#### 核心方法

##### 获取待调度步骤
```python
@staticmethod
def getToScheduledSteps(env: SpaceTaskSimEnv, step_type: StepTypeEnum, task_type: TaskTypeEnum):
    """
    获取待调度的步骤

    Args:
        env (SpaceTaskSimEnv): 环境对象
        step_type (StepTypeEnum): 步骤类型
        task_type (TaskTypeEnum): 任务类型

    Returns:
        dict: 待调度步骤字典
    """
    selected_steps = {}
    to_scheduled_steps = env.task_manager.getToScheduledSteps()

    for step_type_enum, steps in to_scheduled_steps.items():
        if step_type_enum == step_type:
            # 过滤等待资源分配的步骤
            if step_type_enum == StepTypeEnum.COMMUNICATION:
                allocate_waiting_steps = list(env.resource_waiting_communication_steps.keys())
            elif step_type_enum == StepTypeEnum.COMPUTATION:
                allocate_waiting_steps = list(env.resource_waiting_computation_steps.keys())
            elif step_type_enum == StepTypeEnum.SENSING:
                allocate_waiting_steps = list(env.resource_waiting_sensing_steps.keys())
            else:
                allocate_waiting_steps = []

            for step_id, step in steps.items():
                if step_id not in allocate_waiting_steps:
                    if task_type is None or step.task_type == task_type:
                        selected_steps[step_id] = step

    return selected_steps
```

##### 设置调度信息
```python
@staticmethod
def setCommStepScheInfo(env: SpaceTaskSimEnv, step_id, required_trans_size, to_trans_route):
    """
    设置通信步骤调度信息

    Args:
        env (SpaceTaskSimEnv): 环境对象
        step_id (str): 步骤ID
        required_trans_size (float): 所需传输大小
        to_trans_route (list): 传输路由
    """
    env.scheduled_communication_steps[step_id] = {
        'required_trans_size': required_trans_size,
        'to_trans_route': to_trans_route
    }

@staticmethod
def setCompStepScheInfo(env: SpaceTaskSimEnv, step_id, node_id, cpu_id, required_compute_size):
    """
    设置计算步骤调度信息

    Args:
        env (SpaceTaskSimEnv): 环境对象
        step_id (str): 步骤ID
        node_id (str): 执行节点ID
        cpu_id (str): CPU ID
        required_compute_size (float): 所需计算量
    """
    env.scheduled_computation_steps[step_id] = {
        'node_id': node_id,
        'cpu_id': cpu_id,
        'required_compute_size': required_compute_size
    }

@staticmethod
def setSensingScheInfo(env: SpaceTaskSimEnv, step_id, node_id, camera_id, accuracy,
                      required_sensing_accuracy, required_sensing_time, data_size, target_position):
    """
    设置感知步骤调度信息

    Args:
        env (SpaceTaskSimEnv): 环境对象
        step_id (str): 步骤ID
        node_id (str): 执行节点ID
        camera_id (str): 相机ID
        accuracy (float): 实际精度
        required_sensing_accuracy (float): 所需精度
        required_sensing_time (float): 所需感知时间
        data_size (float): 数据大小
        target_position (tuple): 目标位置
    """
    env.scheduled_sensing_steps[step_id] = {
        'node_id': node_id,
        'camera_id': camera_id,
        'accuracy': accuracy,
        'required_sensing_accuracy': required_sensing_accuracy,
        'required_sensing_time': required_sensing_time,
        'data_size': data_size,
        'target_position': target_position
    }
```

### ViewScheduler 视野调度器

**文件位置**: `spacetasksim/scheduler/view_scheduler.py`

负责视野相关查询和调度的调度器。

#### 核心方法

```python
@staticmethod
def getVisibleSatIds(env: SpaceTaskSimEnv, target_position, K=None, D=None, function_type=None):
    """
    获取目标位置可见的卫星ID列表

    Args:
        env (SpaceTaskSimEnv): 环境对象
        target_position (tuple): 目标位置 (lat, lon, alt)
        K (int): 返回最近的K颗卫星
        D (float): 搜索半径(km)
        function_type (FunctionEnum): 卫星功能类型过滤

    Returns:
        list: 可见卫星ID列表
    """
    sat_infos = env.view_manager.getNearestVisibleSatInfos(target_position, K, D, function_type)
    return list(sat_infos.keys())
```

### NodeScheduler 节点调度器

**文件位置**: `spacetasksim/scheduler/node_scheduler.py`

负责节点相关查询和调度的调度器。

#### 核心方法

```python
@staticmethod
def getNodeTypeById(env: SpaceTaskSimEnv, node_id):
    """
    根据节点ID获取节点类型

    Args:
        env (SpaceTaskSimEnv): 环境对象
        node_id (str): 节点ID

    Returns:
        NodeTypeEnum: 节点类型枚举
    """
    node = env.node_manager.getNodeById(node_id)
    if node:
        return node.getNodeType()
    return None

@staticmethod
def getNodePositionById(env: SpaceTaskSimEnv, node_id):
    """
    根据节点ID获取节点位置

    Args:
        env (SpaceTaskSimEnv): 环境对象
        node_id (str): 节点ID

    Returns:
        tuple: 节点位置 (lat, lon, alt)
    """
    node = env.node_manager.getNodeById(node_id)
    if node:
        return node.getPosition()
    return None
```

## SpaceTaskSimScheduler 调度器接口

**文件位置**: `spacetasksim/spacetasksim_scheduler.py`

SpaceTaskSimScheduler提供了统一的调度器接口，用于获取各种调度器实例。

### 类定义

```python
class SpaceTaskSimScheduler:
    """
    SpaceTaskSimEnv的调度器接口类

    提供通信、计算、感知、存储、视野调度器的统一接口
    智能体通过此类与调度器交互，获取状态、奖励和完成信号
    """
```

### 核心方法

```python
@staticmethod
def getResourceScheduler():
    """
    获取资源调度器实例

    Returns:
        ResourceScheduler: 资源调度器实例
    """
    return ResourceScheduler()

@staticmethod
def getTaskScheduler():
    """
    获取任务调度器实例

    Returns:
        TaskScheduler: 任务调度器实例
    """
    return TaskScheduler()

@staticmethod
def getViewScheduler():
    """
    获取视野调度器实例

    Returns:
        ViewScheduler: 视野调度器实例
    """
    return ViewScheduler()

@staticmethod
def getNodeScheduler():
    """
    获取节点调度器实例

    Returns:
        NodeScheduler: 节点调度器实例
    """
    return NodeScheduler()

@staticmethod
def getAlgorithmScheduler():
    """
    获取算法调度器实例

    Returns:
        AlgorithmScheduler: 算法调度器实例
    """
    return AlgorithmScheduler()
```

### 使用示例

```python
# 在策略模块中使用调度器
class MyStrategyModule(BaseStrategyModule):
    def __init__(self):
        super().__init__()
        # 获取各种调度器实例
        self.resourceScheduler = SpaceTaskSimScheduler.getResourceScheduler()
        self.taskScheduler = SpaceTaskSimScheduler.getTaskScheduler()
        self.viewScheduler = SpaceTaskSimScheduler.getViewScheduler()
        self.nodeScheduler = SpaceTaskSimScheduler.getNodeScheduler()
        self.algorithmScheduler = SpaceTaskSimScheduler.getAlgorithmScheduler()

    def scheduleStep(self):
        # 使用调度器进行调度决策
        to_scheduled_steps = self.taskScheduler.getToScheduledSteps(
            self.env, StepTypeEnum.COMPUTATION, TaskTypeEnum.COMPUTATION
        )

        for step_id, step in to_scheduled_steps.items():
            # 获取可见卫星
            visible_sats = self.viewScheduler.getVisibleSatIds(
                self.env, step.current_position, K=10, function_type=FunctionEnum.COMPUTATION
            )

            # 检查资源可用性
            for sat_id in visible_sats:
                available = self.resourceScheduler.checkComputationResourcesAvailable(
                    self.env, sat_id, step.required_compute_size, self.comp_resources_usage_cache
                )
                if available:
                    # 设置调度信息
                    self.taskScheduler.setCompStepScheInfo(
                        self.env, step_id, sat_id, 'cpu_0', step.required_compute_size
                    )
                    break
```

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 策略模块的完整实现
- 强化学习算法的详细API
- 世界模型的实现细节

请查看《SpaceTaskSim_详细代码文档_07_策略模块详解.md》获取更多详细信息。

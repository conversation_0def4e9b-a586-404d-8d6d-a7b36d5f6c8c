# SpaceTaskSim 详细代码文档 - 第7部分：策略模块详解

## 目录
- [BaseStrategyModule 基础策略模块](#basestrategymodule-基础策略模块)
- [传统策略模块](#传统策略模块)
- [深度强化学习策略模块](#深度强化学习策略模块)
- [世界模型策略模块](#世界模型策略模块)

## BaseStrategyModule 基础策略模块

**文件位置**: `train/base_strategy.py`

BaseStrategyModule是所有策略模块的基类，定义了策略模块的标准接口。

### 类定义

```python
class BaseStrategyModule:
    """
    策略模块基类

    定义了所有策略模块必须实现的接口方法
    提供了策略模块的基本框架和通用功能
    """
```

### 核心接口方法

#### 初始化方法
```
initialize(env, last_episode=0, final=False, eval=False)
功能描述：初始化策略模块，设置环境和参数
参数：
  - env (SpaceTaskSimEnv): 仿真环境对象
  - last_episode (int): 上次训练的轮次，用于模型加载
  - final (bool): 是否为最终评估模式
  - eval (bool): 是否为评估模式
返回值：无
注意：子类必须重写此方法实现具体的初始化逻辑
```
```

#### 调度方法
```
scheduleStep()
功能描述：执行一步调度决策，根据策略算法做出调度决策
参数：无
返回值：无
注意：这是策略模块的核心方法，子类必须重写
```
```

#### 经验更新方法
```python
def updateExperience(self):
    """
    更新策略经验

    功能:
        1. 收集环境反馈信息
        2. 计算奖励和状态转移
        3. 更新经验缓冲区
        4. 触发模型训练(如果需要)

    注意:
        对于强化学习策略，此方法用于经验收集和模型更新
        对于传统策略，此方法可能为空实现
    """
    pass
```

#### 重置方法
```python
def reset(self):
    """
    重置策略状态

    功能:
        1. 清空内部状态缓存
        2. 重置决策历史
        3. 准备新一轮仿真

    注意:
        在每轮仿真开始前调用
    """
    pass
```

#### 保存和加载方法
```python
def saveModel(self, episode, final, completion_ratio):
    """
    保存模型

    Args:
        episode (int): 当前训练轮次
        final (bool): 是否为最终保存
        completion_ratio (float): 任务完成率

    功能:
        1. 保存模型参数
        2. 保存训练状态
        3. 记录性能指标
    """
    pass

def loadModel(self, episode):
    """
    加载模型

    Args:
        episode (int): 要加载的模型轮次

    功能:
        1. 加载模型参数
        2. 恢复训练状态
    """
    pass
```

## 传统策略模块

### Random_StrategyModule 随机策略

**文件位置**: `train/random/strategy.py`

随机策略模块实现完全随机的调度决策，主要用于基线对比。

#### 类定义
```python
class Random_StrategyModule(BaseStrategyModule):
    """
    随机策略模块

    对所有调度决策采用随机选择策略
    用作性能基线和算法对比
    """
```

#### 初始化方法
```
initialize(env, last_episode=0, final=False, eval=False)
功能描述：初始化随机策略模块，设置调度器和缓存
参数：
  - env (SpaceTaskSimEnv): 仿真环境对象
  - last_episode (int): 未使用
  - final (bool): 未使用
  - eval (bool): 未使用
返回值：无
```
```

#### 调度方法
```python
def scheduleStep(self):
    """
    执行随机调度决策

    功能:
        1. 获取待调度的通信、计算、感知步骤
        2. 对每个步骤随机选择执行节点
        3. 检查资源可用性
        4. 设置调度信息
    """
    # 调度通信步骤
    self._scheduleCommSteps()

    # 调度计算步骤
    self._scheduleCompSteps()

    # 调度感知步骤
    self._scheduleSensingSteps()

    # 清空资源使用缓存
    self._clearResourceCache()

def _scheduleCommSteps(self):
    """调度通信步骤"""
    to_scheduled_comm_steps = self.taskScheduler.getToScheduledSteps(
        self.env, StepTypeEnum.COMMUNICATION, TaskTypeEnum.COMMUNICATION
    )

    for step_id, step in to_scheduled_comm_steps.items():
        # 获取发送和接收节点位置
        tx_position = self.nodeScheduler.getNodePositionById(self.env, step.tx_node_id)
        rx_position = self.nodeScheduler.getNodePositionById(self.env, step.rx_node_id)

        # 获取可见的中继卫星
        visible_relay_sats = self.viewScheduler.getVisibleSatIds(
            self.env, tx_position, K=10, function_type=FunctionEnum.RELAY
        )

        if visible_relay_sats:
            # 随机选择中继卫星
            relay_sat_id = random.choice(visible_relay_sats)

            # 构建传输路由
            to_trans_route = [step.tx_node_id, relay_sat_id, step.rx_node_id]

            # 检查资源可用性
            resource_available = True
            for i in range(len(to_trans_route) - 1):
                tx_id = to_trans_route[i]
                rx_id = to_trans_route[i + 1]
                if not self.resourceScheduler.checkCommunicationResourcesAvailable(
                    self.env, tx_id, rx_id, step.required_trans_size, self.beam_resources_usage_cache
                ):
                    resource_available = False
                    break

            if resource_available:
                # 设置调度信息
                self.taskScheduler.setCommStepScheInfo(
                    self.env, step_id, step.required_trans_size, to_trans_route
                )

                # 更新资源使用缓存
                self._updateBeamResourceCache(to_trans_route)

def _scheduleCompSteps(self):
    """调度计算步骤"""
    to_scheduled_comp_steps = self.taskScheduler.getToScheduledSteps(
        self.env, StepTypeEnum.COMPUTATION, TaskTypeEnum.COMPUTATION
    )

    for step_id, step in to_scheduled_comp_steps.items():
        # 获取当前位置
        current_position = self.nodeScheduler.getNodePositionById(self.env, step.current_node_id)

        # 获取可见的计算卫星
        visible_comp_sats = self.viewScheduler.getVisibleSatIds(
            self.env, current_position, K=10, function_type=FunctionEnum.COMPUTATION
        )

        if visible_comp_sats:
            # 随机选择计算卫星
            comp_sat_id = random.choice(visible_comp_sats)

            # 检查计算资源可用性
            if self.resourceScheduler.checkComputationResourcesAvailable(
                self.env, comp_sat_id, step.required_compute_size, self.comp_resources_usage_cache
            ):
                # 设置调度信息
                self.taskScheduler.setCompStepScheInfo(
                    self.env, step_id, comp_sat_id, 'cpu_0', step.required_compute_size
                )

                # 更新资源使用缓存
                self._updateCompResourceCache(comp_sat_id, step.required_compute_size)

def _scheduleSensingSteps(self):
    """调度感知步骤"""
    to_scheduled_sensing_steps = self.taskScheduler.getToScheduledSteps(
        self.env, StepTypeEnum.SENSING, TaskTypeEnum.SENSING
    )

    for step_id, step in to_scheduled_sensing_steps.items():
        # 获取可见的感知卫星
        visible_sensing_sats = self.viewScheduler.getVisibleSatIds(
            self.env, step.target_position, K=10, function_type=FunctionEnum.SENSING
        )

        if visible_sensing_sats:
            # 随机选择感知卫星
            sensing_sat_id = random.choice(visible_sensing_sats)

            # 检查感知资源可用性
            if self.resourceScheduler.checkSensingResourcesAvailable(
                self.env, sensing_sat_id, step.required_accuracy,
                step.required_sensing_time, self.sensing_resources_usage_cache
            ):
                # 设置调度信息
                self.taskScheduler.setSensingScheInfo(
                    self.env, step_id, sensing_sat_id, 'camera_0',
                    step.required_accuracy, step.required_accuracy,
                    step.required_sensing_time, step.data_size, step.target_position
                )

                # 更新资源使用缓存
                self._updateSensingResourceCache(sensing_sat_id, step.required_sensing_time)
```

### Greedy_StrategyModule 贪心策略

**文件位置**: `train/greedy/strategy.py`

贪心策略模块实现基于启发式规则的贪心调度决策。

#### 核心特点
- **最短距离优先**: 优先选择距离最近的节点
- **资源利用率优先**: 优先选择资源利用率低的节点
- **任务紧急度优先**: 优先调度截止时间紧急的任务

#### 调度方法
```python
def _scheduleCommSteps(self):
    """贪心通信调度"""
    to_scheduled_comm_steps = self.taskScheduler.getToScheduledSteps(
        self.env, StepTypeEnum.COMMUNICATION, TaskTypeEnum.COMMUNICATION
    )

    # 按任务紧急度排序
    sorted_steps = sorted(to_scheduled_comm_steps.items(),
                         key=lambda x: x[1].deadline - self.env.simulation_time)

    for step_id, step in sorted_steps:
        tx_position = self.nodeScheduler.getNodePositionById(self.env, step.tx_node_id)

        # 获取可见中继卫星并按距离排序
        visible_relay_sats = self.viewScheduler.getVisibleSatIds(
            self.env, tx_position, function_type=FunctionEnum.RELAY
        )

        # 计算距离并排序
        sat_distances = []
        for sat_id in visible_relay_sats:
            sat_position = self.nodeScheduler.getNodePositionById(self.env, sat_id)
            distance = geo_util.calculate_distance(tx_position, sat_position)
            sat_distances.append((sat_id, distance))

        # 按距离排序，优先选择最近的卫星
        sat_distances.sort(key=lambda x: x[1])

        for sat_id, _ in sat_distances:
            to_trans_route = [step.tx_node_id, sat_id, step.rx_node_id]

            # 检查资源可用性
            if self._checkRouteResourceAvailable(to_trans_route, step.required_trans_size):
                self.taskScheduler.setCommStepScheInfo(
                    self.env, step_id, step.required_trans_size, to_trans_route
                )
                self._updateBeamResourceCache(to_trans_route)
                break
```

### Heuristic_StrategyModule 启发式策略

**文件位置**: `train/heuristic/strategy.py`

启发式策略模块实现基于多种启发式规则的综合调度决策。

#### 核心特点
- **多目标优化**: 综合考虑距离、资源利用率、任务优先级
- **动态权重**: 根据系统状态动态调整各因素权重
- **负载均衡**: 考虑节点负载均衡

### Predict_StrategyModule 预测策略

**文件位置**: `train/predict/strategy.py`

预测策略模块基于历史数据和趋势预测进行调度决策。

#### 核心特点
- **历史数据分析**: 分析历史调度效果
- **趋势预测**: 预测未来资源需求和可用性
- **自适应调整**: 根据预测结果调整调度策略

## 深度强化学习策略模块

### D3QN_StrategyModule D3QN策略

**文件位置**: `train/D3QN/strategy.py`

D3QN策略模块实现Dueling Double Deep Q-Network算法。

#### 类定义
```python
class D3QN_StrategyModule(BaseStrategyModule):
    """
    D3QN策略模块

    实现Dueling Double Deep Q-Network算法
    用于离散动作空间的任务调度优化
    """
```

#### 初始化方法
```python
def initialize(self, env: SpaceTaskSimEnv, last_episode=0, final=False, eval=False):
    """
    D3QN策略初始化

    Args:
        env (SpaceTaskSimEnv): 仿真环境
        last_episode (int): 上次训练轮次
        final (bool): 是否最终评估
        eval (bool): 是否评估模式
    """
    self.env = env
    self.last_episode = last_episode
    self.final = final
    self.eval = eval

    # 初始化D3QN环境包装器
    self.D3QN_env = D3QN_Env(env)

    # 加载模型(如果存在)
    if last_episode > 0:
        self.loadModel(last_episode)

    # 设置探索参数
    if eval:
        self.epsilon = 0.0  # 评估时不探索
    else:
        self.epsilon = max(0.1, 1.0 - last_episode * 0.001)  # 线性衰减
```

#### 调度方法
```python
def scheduleStep(self):
    """
    D3QN调度决策

    功能:
        1. 获取当前状态
        2. 使用神经网络预测Q值
        3. 选择动作(ε-贪心策略)
        4. 执行动作并获取奖励
    """
    # 获取当前状态
    state = self.D3QN_env.getState()

    # 选择动作
    if random.random() < self.epsilon and not self.eval:
        # 随机探索
        action = self.D3QN_env.getRandomAction()
    else:
        # 利用策略
        action = self.D3QN_env.getBestAction(state)

    # 执行动作
    reward, done = self.D3QN_env.step(action)

    # 存储经验
    if not self.eval:
        next_state = self.D3QN_env.getState()
        self.D3QN_env.storeExperience(state, action, reward, next_state, done)
```

#### 训练方法
```python
def train(self):
    """
    训练D3QN模型

    Returns:
        float: 训练损失
    """
    if not self.eval and self.D3QN_env.canTrain():
        loss = self.D3QN_env.train()
        return loss
    return 0.0
```

### COMA_AC_StrategyModule COMA-AC策略

**文件位置**: `train/COMA_AC/strategy.py`

COMA-AC策略模块实现Counterfactual Multi-Agent Actor-Critic算法。

#### 核心特点
- **多智能体**: 支持多个智能体协同决策
- **反事实基线**: 使用反事实基线减少方差
- **集中训练分散执行**: 训练时集中，执行时分散

### PTMAPPO_Reptile_StrategyModule PTMAPPO策略

**文件位置**: `train/PTMAPPO_Reptile/strategy.py`

PTMAPPO策略模块实现基于Reptile元学习的MAPPO算法。

#### 核心特点
- **元学习**: 使用Reptile算法进行元学习
- **快速适应**: 能够快速适应新的任务分布
- **多任务学习**: 支持多任务场景下的学习

## 世界模型策略模块

### WMAPPO_StrategyModule 世界模型MAPPO策略

**文件位置**: `train/WMAPPO/strategy.py`

WMAPPO策略模块实现基于世界模型的MAPPO算法。

#### 类定义
```python
class MAPPOStrategyModule(BaseStrategyModule):
    """
    基于世界模型的MAPPO策略模块

    结合Transformer世界模型和MAPPO算法
    实现基于模型的强化学习
    """
```

#### 初始化方法
```python
def initialize(self, env: SpaceTaskSimEnv, last_episode=0, final=False, eval=False):
    """
    WMAPPO策略初始化

    Args:
        env (SpaceTaskSimEnv): 仿真环境
        last_episode (int): 上次训练轮次
        final (bool): 是否最终评估
        eval (bool): 是否评估模式
    """
    self.env = env
    self.last_episode = last_episode
    self.final = final
    self.eval = eval

    # 初始化MAPPO环境包装器
    self.mappo_env = MAPPO_Env(env)

    # 初始化世界模型
    self.world_model = None
    self.use_world_model = False

    # 加载模型
    if last_episode > 0:
        self.loadModel(last_episode)
```

#### 世界模型集成
```python
def initWorldModel(self, world_model_path=None):
    """
    初始化世界模型

    Args:
        world_model_path (str): 世界模型路径
    """
    from .model.transformer_world_model import TransformerWorldModel

    # 获取状态和动作维度
    state_dim = self.mappo_env.getStateDim()
    action_dim = self.mappo_env.getActionDim()

    # 创建世界模型
    self.world_model = TransformerWorldModel(
        state_dim=state_dim,
        action_dim=action_dim,
        d_model=256,
        nhead=8,
        num_layers=6
    )

    # 加载预训练模型
    if world_model_path and os.path.exists(world_model_path):
        self.world_model.load_state_dict(torch.load(world_model_path))
        self.use_world_model = True
        print(f"Loaded world model from {world_model_path}")

def predictNextState(self, state, action):
    """
    使用世界模型预测下一状态

    Args:
        state (torch.Tensor): 当前状态
        action (torch.Tensor): 动作

    Returns:
        torch.Tensor: 预测的下一状态
    """
    if self.world_model and self.use_world_model:
        with torch.no_grad():
            next_state = self.world_model(state.unsqueeze(0), action.unsqueeze(0))
            return next_state.squeeze(0)
    return None
```

#### 基于模型的规划
```python
def modelBasedPlanning(self, current_state, planning_horizon=5):
    """
    基于世界模型的规划

    Args:
        current_state (torch.Tensor): 当前状态
        planning_horizon (int): 规划时间步长

    Returns:
        list: 规划的动作序列
    """
    if not self.use_world_model:
        return []

    best_actions = []
    best_value = float('-inf')

    # 蒙特卡洛树搜索或随机射击
    for _ in range(100):  # 采样100个动作序列
        actions = []
        state = current_state.clone()
        total_reward = 0

        for step in range(planning_horizon):
            # 随机采样动作
            action = self.mappo_env.sampleRandomAction()
            actions.append(action)

            # 预测下一状态
            next_state = self.predictNextState(state, action)
            if next_state is None:
                break

            # 估计奖励
            reward = self.estimateReward(state, action, next_state)
            total_reward += reward * (0.99 ** step)  # 折扣因子

            state = next_state

        if total_reward > best_value:
            best_value = total_reward
            best_actions = actions

    return best_actions

def estimateReward(self, state, action, next_state):
    """
    估计奖励函数

    Args:
        state (torch.Tensor): 当前状态
        action (torch.Tensor): 动作
        next_state (torch.Tensor): 下一状态

    Returns:
        float: 估计奖励
    """
    # 基于状态变化估计奖励
    # 这里可以使用学习的奖励模型或手工设计的奖励函数
    return self.mappo_env.calculateReward(state, action, next_state)
```

### MBRLStrategyModule MBRL策略

**文件位置**: `train/WMAPPO/mbrl_strategy.py`

MBRL策略模块实现完整的基于模型的强化学习流程。

#### 核心特点
- **数据收集**: 收集真实环境交互数据
- **世界模型训练**: 训练Transformer世界模型
- **策略优化**: 在世界模型中进行策略优化
- **真实环境验证**: 在真实环境中验证策略效果

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 工具类的完整实现
- 评估模块的详细API
- Web应用接口的完整说明

请查看《SpaceTaskSim_详细代码文档_08_工具类与评估模块.md》获取更多详细信息。

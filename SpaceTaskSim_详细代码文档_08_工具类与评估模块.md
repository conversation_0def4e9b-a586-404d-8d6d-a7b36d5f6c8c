# SpaceTaskSim 详细代码文档 - 第8部分：工具类与评估模块

## 目录
- [工具类详解](#工具类详解)
- [评估模块详解](#评估模块详解)
- [文件工具类](#文件工具类)

## 工具类详解

### geo_util 地理工具类

**文件位置**: `spacetasksim/utils/geo_util.py`

geo_util提供了地理计算相关的工具函数，包括坐标转换、距离计算、角度计算等。

#### 坐标系转换函数

##### LLA到ECEF转换
```python
def lla_to_ecef(lat, lon, alt):
    """
    将地理坐标(LLA)转换为地心地固坐标(ECEF)
    
    Args:
        lat (float): 纬度(度)
        lon (float): 经度(度)
        alt (float): 高度(米)
    
    Returns:
        tuple: (x, y, z) ECEF坐标(米)
    
    算法:
        使用WGS84椭球体参数进行精确转换
        考虑地球扁率和偏心率
    """
    # WGS84椭球体参数
    a = 6378137.0  # 长半轴(米)
    f = 1 / 298.257223563  # 扁率
    e2 = 2 * f - f * f  # 第一偏心率平方
    
    # 转换为弧度
    lat_rad = math.radians(lat)
    lon_rad = math.radians(lon)
    
    # 计算卯酉圈曲率半径
    N = a / math.sqrt(1 - e2 * math.sin(lat_rad) ** 2)
    
    # ECEF坐标计算
    x = (N + alt) * math.cos(lat_rad) * math.cos(lon_rad)
    y = (N + alt) * math.cos(lat_rad) * math.sin(lon_rad)
    z = (N * (1 - e2) + alt) * math.sin(lat_rad)
    
    return x, y, z
```

##### ECEF到LLA转换
```python
def ecef_to_lla(x, y, z):
    """
    将地心地固坐标(ECEF)转换为地理坐标(LLA)
    
    Args:
        x (float): ECEF X坐标(米)
        y (float): ECEF Y坐标(米)
        z (float): ECEF Z坐标(米)
    
    Returns:
        tuple: (lat, lon, alt) 地理坐标(度, 度, 米)
    
    算法:
        使用迭代算法进行精确转换
        Bowring方法的改进版本
    """
    # WGS84椭球体参数
    a = 6378137.0
    f = 1 / 298.257223563
    e2 = 2 * f - f * f
    
    # 计算经度
    lon = math.atan2(y, x)
    
    # 计算纬度和高度(迭代方法)
    p = math.sqrt(x * x + y * y)
    lat = math.atan2(z, p * (1 - e2))
    
    for _ in range(10):  # 迭代10次通常足够精确
        N = a / math.sqrt(1 - e2 * math.sin(lat) ** 2)
        alt = p / math.cos(lat) - N
        lat_new = math.atan2(z, p * (1 - e2 * N / (N + alt)))
        
        if abs(lat_new - lat) < 1e-12:
            break
        lat = lat_new
    
    # 转换为度
    lat_deg = math.degrees(lat)
    lon_deg = math.degrees(lon)
    
    return lat_deg, lon_deg, alt
```

#### 距离和角度计算

##### 大圆距离计算
```python
def calculate_distance(pos1, pos2):
    """
    计算两点间的大圆距离
    
    Args:
        pos1 (tuple): 位置1 (lat, lon, alt)
        pos2 (tuple): 位置2 (lat, lon, alt)
    
    Returns:
        float: 距离(公里)
    
    算法:
        使用Haversine公式计算球面距离
        考虑高度差的三维距离
    """
    lat1, lon1, alt1 = pos1
    lat2, lon2, alt2 = pos2
    
    # 转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # Haversine公式
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    a = (math.sin(dlat / 2) ** 2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    # 地球半径(公里)
    R = 6371.0
    
    # 球面距离
    surface_distance = R * c
    
    # 考虑高度差
    height_diff = abs(alt2 - alt1) / 1000.0  # 转换为公里
    distance = math.sqrt(surface_distance ** 2 + height_diff ** 2)
    
    return distance
```

##### 仰角计算
```python
def calculate_elevation_angle(observer_pos, target_pos):
    """
    计算观测者到目标的仰角
    
    Args:
        observer_pos (tuple): 观测者位置 (lat, lon, alt)
        target_pos (tuple): 目标位置 (lat, lon, alt)
    
    Returns:
        float: 仰角(度)
    
    算法:
        1. 转换为ECEF坐标
        2. 计算观测者的本地坐标系
        3. 将目标向量投影到本地坐标系
        4. 计算仰角
    """
    # 转换为ECEF坐标
    obs_x, obs_y, obs_z = lla_to_ecef(*observer_pos)
    tar_x, tar_y, tar_z = lla_to_ecef(*target_pos)
    
    # 目标向量
    dx = tar_x - obs_x
    dy = tar_y - obs_y
    dz = tar_z - obs_z
    
    # 观测者位置的本地坐标系
    obs_lat_rad = math.radians(observer_pos[0])
    obs_lon_rad = math.radians(observer_pos[1])
    
    # 本地坐标系的单位向量
    # 东向单位向量
    east_x = -math.sin(obs_lon_rad)
    east_y = math.cos(obs_lon_rad)
    east_z = 0
    
    # 北向单位向量
    north_x = -math.sin(obs_lat_rad) * math.cos(obs_lon_rad)
    north_y = -math.sin(obs_lat_rad) * math.sin(obs_lon_rad)
    north_z = math.cos(obs_lat_rad)
    
    # 天顶向单位向量
    up_x = math.cos(obs_lat_rad) * math.cos(obs_lon_rad)
    up_y = math.cos(obs_lat_rad) * math.sin(obs_lon_rad)
    up_z = math.sin(obs_lat_rad)
    
    # 投影到本地坐标系
    local_east = dx * east_x + dy * east_y + dz * east_z
    local_north = dx * north_x + dy * north_y + dz * north_z
    local_up = dx * up_x + dy * up_y + dz * up_z
    
    # 计算仰角
    horizontal_distance = math.sqrt(local_east ** 2 + local_north ** 2)
    elevation_rad = math.atan2(local_up, horizontal_distance)
    elevation_deg = math.degrees(elevation_rad)
    
    return elevation_deg
```

#### 速度计算
```python
def calculate_velocity_vector(old_pos, new_pos, time_interval, time_accuracy_digit):
    """
    计算速度向量
    
    Args:
        old_pos (tuple): 旧位置 (lat, lon, alt)
        new_pos (tuple): 新位置 (lat, lon, alt)
        time_interval (float): 时间间隔(秒)
        time_accuracy_digit (int): 时间精度位数
    
    Returns:
        tuple: 速度向量 (vx, vy, vz) ECEF坐标系(米/秒)
    """
    if time_interval <= 0:
        return (0.0, 0.0, 0.0)
    
    # 转换为ECEF坐标
    old_x, old_y, old_z = lla_to_ecef(*old_pos)
    new_x, new_y, new_z = lla_to_ecef(*new_pos)
    
    # 计算位移
    dx = new_x - old_x
    dy = new_y - old_y
    dz = new_z - old_z
    
    # 计算速度
    vx = dx / time_interval
    vy = dy / time_interval
    vz = dz / time_interval
    
    # 精度处理
    vx = round(vx, time_accuracy_digit)
    vy = round(vy, time_accuracy_digit)
    vz = round(vz, time_accuracy_digit)
    
    return (vx, vy, vz)
```

#### 地理区域判断
```python
def is_in_china(lat, lon):
    """
    判断坐标是否在中国境内
    
    Args:
        lat (float): 纬度
        lon (float): 经度
    
    Returns:
        bool: True表示在中国境内
    
    算法:
        使用简化的边界框判断
        实际应用中可以使用更精确的多边形判断
    """
    # 中国大陆边界框(简化)
    china_bounds = {
        'lat_min': 18.0,   # 南海诸岛
        'lat_max': 53.5,   # 黑龙江北部
        'lon_min': 73.5,   # 新疆西部
        'lon_max': 135.0   # 黑龙江东部
    }
    
    return (china_bounds['lat_min'] <= lat <= china_bounds['lat_max'] and
            china_bounds['lon_min'] <= lon <= china_bounds['lon_max'])
```

### math_util 数学工具类

**文件位置**: `spacetasksim/utils/math_util.py`

math_util提供了数学计算相关的工具函数。

#### 浮点数运算
```python
def float_mod(dividend, divisor, precision_digits):
    """
    精确的浮点数取模运算
    
    Args:
        dividend (float): 被除数
        divisor (float): 除数
        precision_digits (int): 精度位数
    
    Returns:
        float: 取模结果
    
    功能:
        解决浮点数精度问题的取模运算
        确保结果的数值稳定性
    """
    # 转换为整数进行精确计算
    scale = 10 ** precision_digits
    dividend_int = int(round(dividend * scale))
    divisor_int = int(round(divisor * scale))
    
    if divisor_int == 0:
        return 0.0
    
    result_int = dividend_int % divisor_int
    result = result_int / scale
    
    return round(result, precision_digits)
```

#### 统计函数
```python
def calculate_statistics(data_list):
    """
    计算数据的统计信息
    
    Args:
        data_list (list): 数据列表
    
    Returns:
        dict: 统计信息字典
    """
    if not data_list:
        return {
            'count': 0,
            'mean': 0.0,
            'std': 0.0,
            'min': 0.0,
            'max': 0.0,
            'median': 0.0
        }
    
    import numpy as np
    
    data_array = np.array(data_list)
    
    return {
        'count': len(data_list),
        'mean': float(np.mean(data_array)),
        'std': float(np.std(data_array)),
        'min': float(np.min(data_array)),
        'max': float(np.max(data_array)),
        'median': float(np.median(data_array))
    }
```

### node_util 节点工具类

**文件位置**: `spacetasksim/utils/node_util.py`

node_util提供了节点相关的工具函数。

#### 节点信息提取
```python
def extract_node_info(node):
    """
    提取节点的基本信息
    
    Args:
        node: 节点对象
    
    Returns:
        dict: 节点信息字典
    """
    info = {
        'node_id': node.getNodeId(),
        'node_type': node.getNodeType().name,
        'position': node.getPosition(),
        'functions': [func.name for func in node.getFunctionEnumList()]
    }
    
    # 如果是卫星，添加轨道信息
    if hasattr(node, 'getOrbitType'):
        info['orbit_type'] = node.getOrbitType().name
        info['velocity_vector'] = node.getVelocityVector()
    
    # 如果是地面站，添加地理信息
    if hasattr(node, 'getProvinceZh'):
        info['province_zh'] = node.getProvinceZh()
        info['city_zh'] = node.getCityZh()
    
    return info
```

## 评估模块详解

### SpaceTaskSimEvaluation 评估类

**文件位置**: `spacetasksim/spacetasksim_evaluation.py`

SpaceTaskSimEvaluation提供了详细的性能评估指标和可视化功能。

#### 类定义
```python
class SpaceTaskSimEvaluation:
    """
    SpaceTaskSim仿真评估类
    
    提供任务完成率、资源利用率、系统性能等多维度评估
    支持实时评估和最终评估报告生成
    """
```

#### 初始化方法
```python
def __init__(self, env: SpaceTaskSimEnv, sche_strategy_module, base_path="./evaluation/"):
    """
    评估模块初始化
    
    Args:
        env (SpaceTaskSimEnv): 仿真环境对象
        sche_strategy_module: 调度策略模块
        base_path (str): 评估结果保存路径
    """
    self.env = env
    self.sche_strategy_module = sche_strategy_module
    self.base_path = base_path
    
    # 创建评估目录
    os.makedirs(base_path, exist_ok=True)
    
    # 初始化评估指标
    self._initEvaluationMetrics()
    
    # 初始化数据收集器
    self._initDataCollectors()
```

#### 评估指标初始化
```python
def _initEvaluationMetrics(self):
    """初始化评估指标"""
    # 任务完成相关指标
    self.total_tasks = 0
    self.finished_tasks = 0
    self.failed_tasks = 0
    self.completion_ratio = 0.0
    self.failure_ratio = 0.0
    
    # 任务类型统计
    self.task_type_stats = {
        TaskTypeEnum.COMMUNICATION: {'total': 0, 'finished': 0, 'failed': 0},
        TaskTypeEnum.COMPUTATION: {'total': 0, 'finished': 0, 'failed': 0},
        TaskTypeEnum.SENSING: {'total': 0, 'finished': 0, 'failed': 0}
    }
    
    # 时间相关指标
    self.avg_completion_time = 0.0
    self.avg_waiting_time = 0.0
    self.completion_times = []
    self.waiting_times = []
    
    # 资源利用率指标
    self.resource_utilization = {
        'communication': [],
        'computation': [],
        'sensing': [],
        'storage': []
    }
    
    # 系统性能指标
    self.throughput = 0.0
    self.latency_stats = {}
    self.energy_consumption = 0.0
```

#### 单步评估方法
```python
def evaluateStep(self):
    """
    执行单步评估
    
    功能:
        1. 收集当前步骤的任务完成情况
        2. 更新评估指标
        3. 记录资源使用情况
        4. 计算实时性能指标
    """
    current_time = self.env.simulation_time
    
    # 获取完成和失败的任务
    finished_tasks = self.env.task_manager.getLastSimIntervalFinishedTasks()
    failed_tasks = self.env.task_manager.getLastSimIntervalFailedTasks()
    
    # 更新任务统计
    self._updateTaskStatistics(finished_tasks, failed_tasks)
    
    # 更新时间统计
    self._updateTimeStatistics(finished_tasks, current_time)
    
    # 更新资源利用率
    self._updateResourceUtilization(current_time)
    
    # 计算实时指标
    self._calculateRealTimeMetrics()
```

#### 任务统计更新
```python
def _updateTaskStatistics(self, finished_tasks, failed_tasks):
    """
    更新任务统计信息
    
    Args:
        finished_tasks (dict): 完成任务字典
        failed_tasks (dict): 失败任务字典
    """
    # 统计完成任务
    for task_type, tasks in finished_tasks.items():
        count = len(tasks)
        self.finished_tasks += count
        self.task_type_stats[task_type]['finished'] += count
        
        # 记录完成时间
        for task in tasks:
            completion_time = task.getFinishTime() - task.getArrivalTime()
            self.completion_times.append(completion_time)
            
            waiting_time = task.getStartTime() - task.getArrivalTime()
            self.waiting_times.append(waiting_time)
    
    # 统计失败任务
    for task_type, tasks in failed_tasks.items():
        count = len(tasks)
        self.failed_tasks += count
        self.task_type_stats[task_type]['failed'] += count
    
    # 更新总任务数
    self.total_tasks = self.finished_tasks + self.failed_tasks
    
    # 计算完成率和失败率
    if self.total_tasks > 0:
        self.completion_ratio = self.finished_tasks / self.total_tasks
        self.failure_ratio = self.failed_tasks / self.total_tasks
```

#### 资源利用率更新
```python
def _updateResourceUtilization(self, current_time):
    """
    更新资源利用率统计
    
    Args:
        current_time (float): 当前时间
    """
    # 获取所有节点的资源使用情况
    all_nodes = self.env.node_manager.getAllNodes()
    
    comm_utilization = []
    comp_utilization = []
    sensing_utilization = []
    storage_utilization = []
    
    for node_type, nodes in all_nodes.items():
        for node_id, node in nodes.items():
            # 通信资源利用率
            if FunctionEnum.RELAY in node.getFunctionEnumList():
                comm_usage = self.env.resource_manager.communication_manager.getResourceUsage(node_id)
                utilization = self._calculateCommUtilization(comm_usage)
                comm_utilization.append(utilization)
            
            # 计算资源利用率
            if FunctionEnum.COMPUTATION in node.getFunctionEnumList():
                comp_usage = self.env.resource_manager.computation_manager.getResourceUsage(node_id)
                utilization = self._calculateCompUtilization(comp_usage)
                comp_utilization.append(utilization)
            
            # 感知资源利用率
            if FunctionEnum.SENSING in node.getFunctionEnumList():
                sensing_usage = self.env.resource_manager.sensing_manager.getResourceUsage(node_id)
                utilization = self._calculateSensingUtilization(sensing_usage)
                sensing_utilization.append(utilization)
            
            # 存储资源利用率
            storage_usage = self.env.resource_manager.storage_manager.getResourceUsage(node_id)
            utilization = self._calculateStorageUtilization(storage_usage)
            storage_utilization.append(utilization)
    
    # 记录平均利用率
    if comm_utilization:
        self.resource_utilization['communication'].append(np.mean(comm_utilization))
    if comp_utilization:
        self.resource_utilization['computation'].append(np.mean(comp_utilization))
    if sensing_utilization:
        self.resource_utilization['sensing'].append(np.mean(sensing_utilization))
    if storage_utilization:
        self.resource_utilization['storage'].append(np.mean(storage_utilization))
```

#### 最终评估报告
```python
def generateFinalReport(self):
    """
    生成最终评估报告
    
    Returns:
        dict: 完整的评估报告
    """
    report = {
        'simulation_info': {
            'total_time': self.env.max_simulation_time,
            'strategy': self.sche_strategy_module.__class__.__name__,
            'timestamp': datetime.now().isoformat()
        },
        'task_performance': {
            'total_tasks': self.total_tasks,
            'finished_tasks': self.finished_tasks,
            'failed_tasks': self.failed_tasks,
            'completion_ratio': self.completion_ratio,
            'failure_ratio': self.failure_ratio,
            'task_type_breakdown': self.task_type_stats
        },
        'time_performance': {
            'avg_completion_time': np.mean(self.completion_times) if self.completion_times else 0,
            'avg_waiting_time': np.mean(self.waiting_times) if self.waiting_times else 0,
            'completion_time_std': np.std(self.completion_times) if self.completion_times else 0,
            'waiting_time_std': np.std(self.waiting_times) if self.waiting_times else 0
        },
        'resource_performance': {
            'avg_comm_utilization': np.mean(self.resource_utilization['communication']) if self.resource_utilization['communication'] else 0,
            'avg_comp_utilization': np.mean(self.resource_utilization['computation']) if self.resource_utilization['computation'] else 0,
            'avg_sensing_utilization': np.mean(self.resource_utilization['sensing']) if self.resource_utilization['sensing'] else 0,
            'avg_storage_utilization': np.mean(self.resource_utilization['storage']) if self.resource_utilization['storage'] else 0
        },
        'system_performance': {
            'throughput': self.throughput,
            'energy_consumption': self.energy_consumption
        }
    }
    
    # 保存报告
    report_path = os.path.join(self.base_path, 'final_report.json')
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    return report
```

## 文件工具类

### file_util 文件工具类

**文件位置**: `spacetasksim/utils/file_util.py`

file_util提供了数据序列化和文件操作功能。

#### TaskInfoSerializer 任务信息序列化器
```python
class TaskInfoSerializer:
    """
    任务信息序列化器
    
    用于记录和保存任务执行过程中的详细信息
    支持CSV格式的数据导出
    """
    
    def __init__(self, file_path, time_accuracy_digit):
        """
        初始化序列化器
        
        Args:
            file_path (str): 文件保存路径
            time_accuracy_digit (int): 时间精度位数
        """
        self.file_path = file_path
        self.time_accuracy_digit = time_accuracy_digit
        self.data_buffer = []
        self.headers = [
            'arrival_time', 'task_id', 'task_type', 'init_node_id',
            'start_time', 'finish_time', 'completion_time', 'status'
        ]
    
    def add(self, arrival_time, task_id, task_type, init_node_id, 
            start_time=None, finish_time=None, status='executing'):
        """
        添加任务记录
        
        Args:
            arrival_time (float): 到达时间
            task_id (str): 任务ID
            task_type (TaskTypeEnum): 任务类型
            init_node_id (str): 初始节点ID
            start_time (float): 开始时间
            finish_time (float): 完成时间
            status (str): 任务状态
        """
        completion_time = None
        if start_time and finish_time:
            completion_time = finish_time - start_time
        
        record = {
            'arrival_time': round(arrival_time, self.time_accuracy_digit),
            'task_id': task_id,
            'task_type': task_type.name,
            'init_node_id': init_node_id,
            'start_time': round(start_time, self.time_accuracy_digit) if start_time else None,
            'finish_time': round(finish_time, self.time_accuracy_digit) if finish_time else None,
            'completion_time': round(completion_time, self.time_accuracy_digit) if completion_time else None,
            'status': status
        }
        
        self.data_buffer.append(record)
    
    def save(self):
        """保存数据到CSV文件"""
        import pandas as pd
        
        if self.data_buffer:
            df = pd.DataFrame(self.data_buffer)
            df.to_csv(self.file_path, index=False)
            print(f"Task info saved to {self.file_path}")
```

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- Web应用接口的完整实现
- Flask API的详细说明
- 前端界面的功能介绍

请查看《SpaceTaskSim_详细代码文档_09_Web应用接口.md》获取更多详细信息。

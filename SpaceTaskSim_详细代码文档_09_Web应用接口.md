# SpaceTaskSim 详细代码文档 - 第9部分：Web应用接口

## 目录
- [Flask Web应用](#flask-web应用)
- [API接口详解](#api接口详解)
- [前端功能模块](#前端功能模块)
- [数据可视化](#数据可视化)

## Flask Web应用

**文件位置**: `examples/app.py`

Flask Web应用提供了SpaceTaskSim的Web界面，支持仿真控制、实时监控和结果可视化。

### 应用初始化

```python
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import threading
import json
import os
import yaml
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量
simulation_thread = None
simulation_status = {
    'running': False,
    'progress': 0.0,
    'current_time': 0.0,
    'total_time': 0.0,
    'completion_ratio': 0.0,
    'total_tasks': 0,
    'finished_tasks': 0,
    'failed_tasks': 0
}
simulation_data = {
    'task_stats': [],
    'resource_usage': [],
    'node_positions': [],
    'performance_metrics': []
}
```

## API接口详解

### 仿真控制接口

#### 启动仿真
```python
@app.route('/api/simulation/start', methods=['POST'])
def start_simulation():
    """
    启动仿真

    Request Body:
        {
            "config_path": "path/to/config.yaml",
            "strategy": "Random|Greedy|D3QN|WMAPPO",
            "max_time": 3600,
            "save_results": true
        }

    Response:
        {
            "success": true,
            "message": "Simulation started successfully",
            "simulation_id": "sim_20231201_143022"
        }

    功能:
        1. 验证请求参数
        2. 加载配置文件
        3. 初始化仿真环境
        4. 启动仿真线程
        5. 返回仿真ID
    """
    global simulation_thread, simulation_status

    try:
        # 检查是否已有仿真在运行
        if simulation_status['running']:
            return jsonify({
                'success': False,
                'message': 'Simulation is already running'
            }), 400

        # 获取请求参数
        data = request.get_json()
        config_path = data.get('config_path', 'examples/config.yaml')
        strategy_name = data.get('strategy', 'Random')
        max_time = data.get('max_time', 3600)
        save_results = data.get('save_results', True)

        # 验证配置文件
        if not os.path.exists(config_path):
            return jsonify({
                'success': False,
                'message': f'Config file not found: {config_path}'
            }), 400

        # 生成仿真ID
        simulation_id = f"sim_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 启动仿真线程
        simulation_thread = threading.Thread(
            target=run_simulation_thread,
            args=(config_path, strategy_name, max_time, save_results, simulation_id)
        )
        simulation_thread.daemon = True
        simulation_thread.start()

        return jsonify({
            'success': True,
            'message': 'Simulation started successfully',
            'simulation_id': simulation_id
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to start simulation: {str(e)}'
        }), 500
```

#### 停止仿真
```
/api/simulation/stop
方法：POST
功能描述：停止当前运行的仿真任务
输入参数：无
返回值：
  - success (bool): 操作是否成功
  - message (str): 操作结果信息
```

    try:
        if not simulation_status['running']:
            return jsonify({
                'success': False,
                'message': 'No simulation is running'
            }), 400

        # 设置停止标志
        simulation_status['stop_requested'] = True

        return jsonify({
            'success': True,
            'message': 'Simulation stop requested'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to stop simulation: {str(e)}'
        }), 500
```

#### 暂停/恢复仿真
```python
@app.route('/api/simulation/pause', methods=['POST'])
def pause_simulation():
    """
    暂停仿真

    Response:
        {
            "success": true,
            "message": "Simulation paused"
        }
    """
    global simulation_status

    try:
        if not simulation_status['running']:
            return jsonify({
                'success': False,
                'message': 'No simulation is running'
            }), 400

        simulation_status['paused'] = True

        return jsonify({
            'success': True,
            'message': 'Simulation paused'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to pause simulation: {str(e)}'
        }), 500

@app.route('/api/simulation/resume', methods=['POST'])
def resume_simulation():
    """
    恢复仿真

    Response:
        {
            "success": true,
            "message": "Simulation resumed"
        }
    """
    global simulation_status

    try:
        if not simulation_status['running']:
            return jsonify({
                'success': False,
                'message': 'No simulation is running'
            }), 400

        simulation_status['paused'] = False

        return jsonify({
            'success': True,
            'message': 'Simulation resumed'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to resume simulation: {str(e)}'
        }), 500
```

### 状态查询接口

#### 获取仿真状态
```
/api/simulation/status
方法：GET
功能描述：获取当前仿真的运行状态和性能指标
输入参数：无
返回值：
  - running (bool): 仿真是否正在运行
  - progress (float): 仿真进度（0-1）
  - current_time (float): 当前仿真时间
  - total_time (float): 总仿真时间
  - completion_ratio (float): 任务完成率
  - total_tasks (int): 总任务数
  - finished_tasks (int): 已完成任务数
  - failed_tasks (int): 失败任务数
  - resource_utilization (dict): 资源利用率信息
```

    try:
        # 计算资源利用率
        resource_utilization = calculate_current_resource_utilization()

        status = {
            'running': simulation_status.get('running', False),
            'paused': simulation_status.get('paused', False),
            'progress': simulation_status.get('progress', 0.0),
            'current_time': simulation_status.get('current_time', 0.0),
            'total_time': simulation_status.get('total_time', 0.0),
            'completion_ratio': simulation_status.get('completion_ratio', 0.0),
            'total_tasks': simulation_status.get('total_tasks', 0),
            'finished_tasks': simulation_status.get('finished_tasks', 0),
            'failed_tasks': simulation_status.get('failed_tasks', 0),
            'resource_utilization': resource_utilization
        }

        return jsonify(status)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get simulation status: {str(e)}'
        }), 500
```

#### 获取节点信息
```python
@app.route('/api/nodes', methods=['GET'])
def get_nodes():
    """
    获取所有节点信息

    Query Parameters:
        - node_type: 节点类型过滤 (SAT|GS|MU)
        - function_type: 功能类型过滤 (COMPUTATION|RELAY|SENSING)

    Response:
        {
            "nodes": [
                {
                    "node_id": "sat_001",
                    "node_type": "SAT",
                    "position": [39.9042, 116.4074, 550000],
                    "functions": ["COMPUTATION", "RELAY"],
                    "orbit_type": "LEO",
                    "velocity": [7500.0, 0.0, 0.0],
                    "resource_usage": {
                        "computation": 0.65,
                        "communication": 0.45
                    }
                }
            ]
        }

    功能:
        1. 返回节点基本信息
        2. 支持类型过滤
        3. 包含实时位置和资源使用情况
    """
    try:
        node_type_filter = request.args.get('node_type')
        function_type_filter = request.args.get('function_type')

        nodes = get_filtered_nodes(node_type_filter, function_type_filter)

        return jsonify({
            'nodes': nodes
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get nodes: {str(e)}'
        }), 500
```

#### 获取任务信息
```python
@app.route('/api/tasks', methods=['GET'])
def get_tasks():
    """
    获取任务信息

    Query Parameters:
        - task_type: 任务类型过滤 (COMMUNICATION|COMPUTATION|SENSING)
        - status: 任务状态过滤 (EXECUTING|FINISHED|FAILED)
        - limit: 返回数量限制
        - offset: 偏移量

    Response:
        {
            "tasks": [
                {
                    "task_id": "task_001",
                    "task_type": "COMPUTATION",
                    "status": "EXECUTING",
                    "arrival_time": 1234.5,
                    "start_time": 1240.2,
                    "deadline": 1300.0,
                    "progress": 0.65,
                    "assigned_nodes": ["sat_001"],
                    "steps": [
                        {
                            "step_id": "step_001",
                            "step_type": "COMPUTATION",
                            "status": "EXECUTING",
                            "node_id": "sat_001"
                        }
                    ]
                }
            ],
            "total_count": 1250,
            "filtered_count": 125
        }

    功能:
        1. 返回任务详细信息
        2. 支持多种过滤条件
        3. 支持分页查询
        4. 包含任务执行进度
    """
    try:
        task_type_filter = request.args.get('task_type')
        status_filter = request.args.get('status')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        tasks, total_count, filtered_count = get_filtered_tasks(
            task_type_filter, status_filter, limit, offset
        )

        return jsonify({
            'tasks': tasks,
            'total_count': total_count,
            'filtered_count': filtered_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get tasks: {str(e)}'
        }), 500
```

### 数据导出接口

#### 导出仿真结果
```python
@app.route('/api/export/results', methods=['GET'])
def export_results():
    """
    导出仿真结果

    Query Parameters:
        - format: 导出格式 (json|csv|excel)
        - include: 包含的数据类型 (tasks|nodes|metrics|all)

    Response:
        文件下载或JSON数据

    功能:
        1. 支持多种导出格式
        2. 可选择导出内容
        3. 生成压缩包下载
    """
    try:
        export_format = request.args.get('format', 'json')
        include_data = request.args.get('include', 'all')

        # 收集导出数据
        export_data = collect_export_data(include_data)

        if export_format == 'json':
            return jsonify(export_data)
        elif export_format == 'csv':
            return generate_csv_export(export_data)
        elif export_format == 'excel':
            return generate_excel_export(export_data)
        else:
            return jsonify({
                'success': False,
                'message': f'Unsupported export format: {export_format}'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to export results: {str(e)}'
        }), 500
```

### 配置管理接口

#### 获取配置模板
```python
@app.route('/api/config/template', methods=['GET'])
def get_config_template():
    """
    获取配置文件模板

    Response:
        {
            "template": {
                "config_simulation": {
                    "simulation_time": 3600.0,
                    "simulation_interval": 1.0,
                    "schedule_interval": 10.0
                },
                "config_node": {
                    "config_constellation": {
                        "constellation_name": "starlink",
                        "orbit_altitude": 550
                    }
                }
            }
        }

    功能:
        1. 提供标准配置模板
        2. 包含参数说明
        3. 支持不同场景模板
    """
    try:
        template = load_config_template()

        return jsonify({
            'template': template
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get config template: {str(e)}'
        }), 500
```

#### 验证配置文件
```python
@app.route('/api/config/validate', methods=['POST'])
def validate_config():
    """
    验证配置文件

    Request Body:
        {
            "config": {配置对象}
        }

    Response:
        {
            "valid": true,
            "errors": [],
            "warnings": []
        }

    功能:
        1. 验证配置文件格式
        2. 检查参数合理性
        3. 提供错误和警告信息
    """
    try:
        data = request.get_json()
        config = data.get('config', {})

        validation_result = validate_simulation_config(config)

        return jsonify(validation_result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to validate config: {str(e)}'
        }), 500
```

## 前端功能模块

### 仿真控制面板

前端提供了直观的仿真控制界面，包括：

#### 控制按钮
- **启动仿真**: 配置参数并启动新的仿真
- **暂停/恢复**: 暂停或恢复当前仿真
- **停止仿真**: 停止当前仿真并保存结果
- **重置环境**: 重置仿真环境到初始状态

#### 参数配置
- **策略选择**: 选择调度策略(Random/Greedy/D3QN/WMAPPO等)
- **仿真时长**: 设置仿真总时长
- **时间间隔**: 设置仿真和调度时间间隔
- **节点配置**: 配置卫星、地面站、移动用户参数
- **任务配置**: 配置任务生成参数

### 实时监控面板

#### 状态指示器
```javascript
// 实时更新仿真状态
function updateSimulationStatus() {
    fetch('/api/simulation/status')
        .then(response => response.json())
        .then(data => {
            // 更新进度条
            document.getElementById('progress-bar').style.width =
                (data.progress * 100) + '%';

            // 更新状态指示器
            document.getElementById('status-indicator').className =
                data.running ? 'status-running' : 'status-stopped';

            // 更新性能指标
            updatePerformanceMetrics(data);
        })
        .catch(error => console.error('Error:', error));
}

// 每秒更新一次状态
setInterval(updateSimulationStatus, 1000);
```

#### 性能指标显示
```javascript
function updatePerformanceMetrics(data) {
    // 任务完成率
    document.getElementById('completion-ratio').textContent =
        (data.completion_ratio * 100).toFixed(2) + '%';

    // 任务统计
    document.getElementById('total-tasks').textContent = data.total_tasks;
    document.getElementById('finished-tasks').textContent = data.finished_tasks;
    document.getElementById('failed-tasks').textContent = data.failed_tasks;

    // 资源利用率
    updateResourceUtilization(data.resource_utilization);
}

function updateResourceUtilization(utilization) {
    const resources = ['communication', 'computation', 'sensing', 'storage'];

    resources.forEach(resource => {
        const value = (utilization[resource] * 100).toFixed(1);
        document.getElementById(`${resource}-utilization`).textContent = value + '%';

        // 更新进度条
        const progressBar = document.getElementById(`${resource}-progress`);
        progressBar.style.width = value + '%';

        // 根据利用率设置颜色
        if (utilization[resource] > 0.8) {
            progressBar.className = 'progress-bar bg-danger';
        } else if (utilization[resource] > 0.6) {
            progressBar.className = 'progress-bar bg-warning';
        } else {
            progressBar.className = 'progress-bar bg-success';
        }
    });
}
```

## 数据可视化

### 实时图表更新

#### 任务完成率趋势图
```javascript
// 使用Chart.js创建实时图表
const completionChart = new Chart(document.getElementById('completion-chart'), {
    type: 'line',
    data: {
        labels: [],
        datasets: [{
            label: 'Task Completion Ratio',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 1.0
            }
        },
        plugins: {
            title: {
                display: true,
                text: 'Task Completion Ratio Over Time'
            }
        }
    }
});

// 更新图表数据
function updateCompletionChart(time, ratio) {
    completionChart.data.labels.push(time);
    completionChart.data.datasets[0].data.push(ratio);

    // 保持最近100个数据点
    if (completionChart.data.labels.length > 100) {
        completionChart.data.labels.shift();
        completionChart.data.datasets[0].data.shift();
    }

    completionChart.update('none');
}
```

#### 资源利用率热力图
```javascript
// 使用D3.js创建热力图
function createResourceHeatmap() {
    const margin = {top: 30, right: 30, bottom: 30, left: 30};
    const width = 600 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    const svg = d3.select("#resource-heatmap")
        .append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // 颜色比例尺
    const colorScale = d3.scaleSequential()
        .interpolator(d3.interpolateYlOrRd)
        .domain([0, 1]);

    // 更新热力图数据
    function updateHeatmap(data) {
        const cells = svg.selectAll(".cell")
            .data(data);

        cells.enter()
            .append("rect")
            .attr("class", "cell")
            .merge(cells)
            .attr("x", d => d.x)
            .attr("y", d => d.y)
            .attr("width", d => d.width)
            .attr("height", d => d.height)
            .attr("fill", d => colorScale(d.value))
            .on("mouseover", function(event, d) {
                // 显示工具提示
                showTooltip(event, d);
            })
            .on("mouseout", hideTooltip);
    }

    return updateHeatmap;
}
```

### 3D卫星轨道可视化

```javascript
// 使用Three.js创建3D场景
function create3DVisualization() {
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();

    renderer.setSize(800, 600);
    document.getElementById('3d-visualization').appendChild(renderer.domElement);

    // 创建地球
    const earthGeometry = new THREE.SphereGeometry(6.371, 32, 32);
    const earthMaterial = new THREE.MeshBasicMaterial({
        map: new THREE.TextureLoader().load('earth_texture.jpg')
    });
    const earth = new THREE.Mesh(earthGeometry, earthMaterial);
    scene.add(earth);

    // 卫星对象数组
    const satellites = [];

    // 更新卫星位置
    function updateSatellitePositions(satelliteData) {
        satelliteData.forEach((satData, index) => {
            if (!satellites[index]) {
                // 创建新卫星
                const satGeometry = new THREE.SphereGeometry(0.1, 8, 8);
                const satMaterial = new THREE.MeshBasicMaterial({color: 0xff0000});
                const satellite = new THREE.Mesh(satGeometry, satMaterial);
                satellites[index] = satellite;
                scene.add(satellite);
            }

            // 更新位置
            const [lat, lon, alt] = satData.position;
            const position = llaToCartesian(lat, lon, alt);
            satellites[index].position.set(position.x, position.y, position.z);
        });
    }

    // 渲染循环
    function animate() {
        requestAnimationFrame(animate);

        // 旋转地球
        earth.rotation.y += 0.01;

        renderer.render(scene, camera);
    }

    animate();

    return updateSatellitePositions;
}
```

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 世界模型的完整实现
- 数据收集和处理流程
- 模型训练和评估方法

请查看《SpaceTaskSim_详细代码文档_10_世界模型与数据处理.md》获取更多详细信息。

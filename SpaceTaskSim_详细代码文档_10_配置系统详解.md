# SpaceTaskSim 详细代码文档 - 第10部分：配置系统详解

## 目录
- [配置系统概述](#配置系统概述)
- [config_simulation 仿真配置](#config_simulation-仿真配置)
- [config_topology 拓扑配置](#config_topology-拓扑配置)
- [config_grid 网格配置](#config_grid-网格配置)
- [config_node 节点配置](#config_node-节点配置)
- [config_task 任务配置](#config_task-任务配置)
- [config_resource 资源配置](#config_resource-资源配置)
- [config_view 视野配置](#config_view-视野配置)

## 配置系统概述

SpaceTaskSim使用YAML格式的配置文件来管理仿真参数。主要配置文件位于 `examples/config.yaml`，包含了仿真、节点、任务、资源等各个方面的配置参数。

### 配置文件结构

配置文件采用分层结构，主要包含以下几个部分：
- `config_simulation`: 仿真基本参数
- `config_topology`: 拓扑数据配置
- `config_grid`: 网格配置
- `config_node`: 节点配置
- `config_task`: 任务配置
- `config_resource`: 资源配置
- `config_view`: 视野配置

## config_simulation 仿真配置

```yaml
config_simulation:
  bbox:
    longitude_range: [-180.0, 180.0] # 经度范围
    latitude_range: [-90.0,  90.0] # 纬度范围
  epoch: "2025-01-01 00:00:00"
  max_simulation_time: 200
  start_simulation_time: 0
  flight_interval: 1.0
  simulation_interval: 0.1
  schedule_interval: 0.1
  state_sync_interval: 1.0
  time_accuracy_digit: 3 # 0.001s
```

### 参数详解

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `bbox.longitude_range` | list | [-180.0, 180.0] | 仿真区域经度范围(度) |
| `bbox.latitude_range` | list | [-90.0, 90.0] | 仿真区域纬度范围(度) |
| `epoch` | str | "2025-01-01 00:00:00" | 仿真起始时间(UTC) |
| `max_simulation_time` | int | 200 | 最大仿真时间(秒) |
| `start_simulation_time` | int | 0 | 仿真开始时间(秒) |
| `flight_interval` | float | 1.0 | 轨道更新间隔(秒) |
| `simulation_interval` | float | 0.1 | 仿真时间步长(秒) |
| `schedule_interval` | float | 0.1 | 调度时间间隔(秒) |
| `state_sync_interval` | float | 1.0 | 状态同步间隔(秒) |
| `time_accuracy_digit` | int | 3 | 时间精度位数(小数点后位数) |

## config_topology 拓扑配置

```yaml
config_topology:
  mode: "stored" # "real" or "stored"
  export: False # True or False
  flight_path: "../../files/topology_files/500s/flight.pkl"
  view_path: "../../files/topology_files/500s/view.pkl"
```

### 参数详解

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `mode` | str | 运行模式："real"(实时计算) 或 "stored"(使用预存数据) |
| `export` | bool | 是否导出拓扑数据 |
| `flight_path` | str | 轨道数据文件路径 |
| `view_path` | str | 视野数据文件路径 |

## config_grid 网格配置

```yaml
config_grid:
  grid_path: "../../files/grid_files/grid.pkl"
```

### 参数详解

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `grid_path` | str | 网格数据文件路径 |

## config_node 节点配置

```yaml
config_node:
  # 是否导出时隙节点记录
  record: False # True or False
  export: False # True or False
  export_path: "../../files/other_files/60s/node_position.csv"
  # supported constellation:['starlink_v1','starlink_v2','gw','GEO_sensing','SSO_sensing']
  constellation_paths: [
    "../../spacetasksim/node_config/constellation/starlink_v1.yaml",
    "../../spacetasksim/node_config/constellation/geo_sensing.yaml",
    "../../spacetasksim/node_config/constellation/sso_sensing.yaml"
  ]
  # supported station:['China']
  station_paths: ["../../spacetasksim/node_config/station/China.yaml"]
```

### 参数详解

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `record` | bool | 是否记录节点信息 |
| `export` | bool | 是否导出节点数据 |
| `export_path` | str | 节点数据导出路径 |
| `constellation_paths` | list | 星座配置文件路径列表 |
| `station_paths` | list | 地面站配置文件路径列表 |

### 支持的星座类型
- `starlink_v1`: Starlink第一代星座
- `starlink_v2`: Starlink第二代星座
- `gw`: 网关星座
- `GEO_sensing`: 地球静止轨道感知卫星
- `SSO_sensing`: 太阳同步轨道感知卫星

### 支持的地面站配置
- `China`: 中国地面站网络

## config_task 任务配置

```yaml
config_task:
  # 是否导出时隙新任务记录
  record: False # True or False
  export: False # True or False
  export_path: "../../files/other_files/60s/task.csv"

  MTPD_threshold: 0.3 # The threshold of Maximum Tolerable Period Disruption for task exe
  communication:
    task_size: [10000,50000] # MB
    task_requirement_unit: 0.0010 # MB s/person
    task_TTL_range: [ 10,20 ] # s
  computation:
    task_size: [1000,5000] # MB
    task_requirement_unit: 0.00012 # MB s/person
    task_TTL_range: [10,20] # s
  sensing:
    sensing_time: [11,22] # s
    sensing_interval: [120,60,60,60,45] # minutes/time
    task_TTL_range: [10,20] # s
    sensing_accuracy_range: [0.2,0.8]
```

### 参数详解

#### 通用任务参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `record` | bool | 是否记录任务信息 |
| `export` | bool | 是否导出任务数据 |
| `export_path` | str | 任务数据导出路径 |
| `MTPD_threshold` | float | 最大可容忍周期中断阈值 |

#### 通信任务参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `task_size` | list | 任务大小范围(MB) |
| `task_requirement_unit` | float | 任务需求单位(MB s/person) |
| `task_TTL_range` | list | 任务生存时间范围(秒) |

#### 计算任务参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `task_size` | list | 任务大小范围(MB) |
| `task_requirement_unit` | float | 任务需求单位(MB s/person) |
| `task_TTL_range` | list | 任务生存时间范围(秒) |

#### 感知任务参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `sensing_time` | list | 感知时间范围(秒) |
| `sensing_interval` | list | 感知间隔(分钟/次) |
| `task_TTL_range` | list | 任务生存时间范围(秒) |
| `sensing_accuracy_range` | list | 感知精度范围 |

## config_resource 资源配置

```yaml
config_resource:
  # 是否导出时隙资源记录
  record: False # True or False
  export: False # True or False
  export_path: "../../files/other_files/60s/resource.csv"
  
  # storage
  config_storage:
    capacity: 3.0e+7 # MB 30TB
    
  # computation
  config_computation:
    cycle_per_byte: 17 # B
    user:
      cpu: 1  # CPU capacity of mobile users
      frequency: [0.5,1.0] # Gc/s
    station:
      cpu: 2  # CPU capacity of ground stations
      frequency: [ 1.0,2.0 ] # Gc/s
    satellite:
      cpu: 4  # CPU capacity of LEO satellites
      frequency: [2.0,8.0] # Gc/s
      
  # communication
  config_communication:
    G2S:
      # Each ground station use fixed frequency
      beam_receive: 8 # Receive beam num on satellite
      beam_transmit: 1  # Transmit beam num on station
      bandwidth: [0.25,0.50] # GHz
      frequency_start: 30.0 # GHz
      power_dbw: [10,10] # 10W
    S2G:
      # Each ground station use fixed frequency
      beam_receive: 4 # Receive beam num on station
      beam_transmit: 16  # Transmit beam num on satellite
      bandwidth: [0.50,1.00] # GHz
      frequency_start: 40.0 # GHz
      power_dbw: [16.99,16.99] # 50W
    S2S:
      # ISL,Inter-SatelliteLink
      beam_receive: 10 # Receive beam num on satellite
      beam_transmit: 10  # Transmit beam num on satellite
      bandwidth: [1,4] # GHz
      frequency_start: 193500 # GHz 193.5THz 1550nm
      power_dbw: [-3.01,6.99] # 单位：dbw  0.5w=-3.01dbw 1w=0dbw 1.5w=1.76dbw 5W=6.99dbw
      
  # sensing
  config_sensing:
    camera: 6 # Camera num on each sensing node
    accuracy_range: [0.1,1.0] # Accuracy of camera
    data_increase_rate: 50 # MB/s accuracy=1.0
    elevation_mask_angle: 25 # Minimum elevation angle for sensing
```

### 存储资源配置
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `capacity` | float | 存储容量(MB) |

### 计算资源配置
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `cycle_per_byte` | int | 每字节所需CPU周期数 |
| `user.cpu` | int | 移动用户CPU核心数 |
| `user.frequency` | list | 移动用户CPU频率范围(GHz) |
| `station.cpu` | int | 地面站CPU核心数 |
| `station.frequency` | list | 地面站CPU频率范围(GHz) |
| `satellite.cpu` | int | 卫星CPU核心数 |
| `satellite.frequency` | list | 卫星CPU频率范围(GHz) |

### 通信资源配置

#### G2S (地面到卫星)链路
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `beam_receive` | int | 卫星接收波束数 |
| `beam_transmit` | int | 地面站发射波束数 |
| `bandwidth` | list | 带宽范围(GHz) |
| `frequency_start` | float | 起始频率(GHz) |
| `power_dbw` | list | 发射功率范围(dBW) |

#### S2G (卫星到地面)链路
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `beam_receive` | int | 地面站接收波束数 |
| `beam_transmit` | int | 卫星发射波束数 |
| `bandwidth` | list | 带宽范围(GHz) |
| `frequency_start` | float | 起始频率(GHz) |
| `power_dbw` | list | 发射功率范围(dBW) |

#### S2S (星间)链路
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `beam_receive` | int | 卫星接收波束数 |
| `beam_transmit` | int | 卫星发射波束数 |
| `bandwidth` | list | 带宽范围(GHz) |
| `frequency_start` | float | 起始频率(GHz，193.5THz对应1550nm光通信) |
| `power_dbw` | list | 发射功率范围(dBW) |

### 感知资源配置
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `camera` | int | 每个感知节点的相机数量 |
| `accuracy_range` | list | 相机精度范围 |
| `data_increase_rate` | int | 数据增长率(MB/s，精度为1.0时) |
| `elevation_mask_angle` | int | 感知最小仰角(度) |

## config_view 视野配置

```yaml
config_view:
  clearance: 150 # Clearance for safe ISL transmitting, km
  elevation_mask_angle: 25 # Minimum elevation angle for satellite-station view
```

### 参数详解
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `clearance` | int | 星间链路安全间隙(km) |
| `elevation_mask_angle` | int | 卫星-地面站视野最小仰角(度) |

## 配置文件使用示例

### 加载配置文件
```python
import yaml

def load_config(config_path="examples/config.yaml"):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

# 使用示例
config = load_config()
simulation_time = config['config_simulation']['max_simulation_time']
```

### 修改配置参数
```python
# 修改仿真时间
config['config_simulation']['max_simulation_time'] = 3600

# 修改调度间隔
config['config_simulation']['schedule_interval'] = 1.0

# 启用数据导出
config['config_task']['export'] = True
config['config_resource']['export'] = True
```

### 保存配置文件
```python
def save_config(config, config_path="modified_config.yaml"):
    """保存配置文件"""
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

# 保存修改后的配置
save_config(config, "my_config.yaml")
```

## 配置参数调优建议

### 性能优化
- 增大 `simulation_interval` 和 `schedule_interval` 可以提高仿真速度
- 设置 `mode: "stored"` 使用预计算数据可以显著提升性能
- 关闭不必要的数据记录和导出功能

### 精度优化
- 减小 `simulation_interval` 可以提高仿真精度
- 增加 `time_accuracy_digit` 可以提高时间计算精度
- 设置 `mode: "real"` 进行实时轨道计算

### 内存优化
- 合理设置仿真时间范围
- 适当调整星座规模和地面站数量
- 控制任务生成频率和数据导出频率

## 下一部分预告

在下一部分文档中，我们将详细介绍：
- 数据处理与预测模块的实现
- 测试框架和部署指南
- 性能优化和故障排除方法

请查看《SpaceTaskSim_详细代码文档_11_数据处理与测试部署.md》获取更多详细信息。

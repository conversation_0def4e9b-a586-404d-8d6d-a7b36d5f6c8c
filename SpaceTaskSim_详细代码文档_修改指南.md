# SpaceTaskSim 详细代码文档修改指南

## 修改原则

根据您提供的格式要求，所有API函数介绍应遵循以下统一格式：

```
函数名(参数列表)
功能描述：简要说明函数的主要功能
参数：
  - 参数名 (类型): 参数说明
返回值：返回值类型和说明
```

## 需要修改的文档列表

### 1. SpaceTaskSim_详细代码文档_01_项目概述与架构.md
**状态**: ✅ 已修改
- 添加了模块层次结构说明
- 统一了API函数说明格式

### 2. SpaceTaskSim_详细代码文档_02_核心环境类.md
**状态**: ✅ 部分修改
- 已修改主要API函数格式
- 需要继续修改剩余函数

**待修改示例**:
```
# 原格式
def _updateFlight(self):
    """
    更新卫星轨道状态
    
    功能:
        1. 调用FlightManager更新所有卫星位置
        2. 计算卫星速度向量
        3. 更新轨道参数
    """

# 修改为
_updateFlight()
功能描述：更新卫星轨道状态，计算位置和速度
参数：无
返回值：无
```

### 3. SpaceTaskSim_详细代码文档_03_管理器类详解.md
**状态**: ✅ 部分修改
- 已修改NodeManager部分
- 需要修改TaskManager和ResourceManager部分

**待修改示例**:
```
# TaskManager构造函数
__init__(time_accuracy_digit, config_task, config_resource, grids, stations)
功能描述：初始化任务管理器，设置任务生成和调度参数
参数：
  - time_accuracy_digit (int): 时间精度位数
  - config_task (dict): 任务配置字典
  - config_resource (dict): 资源配置字典
  - grids (dict): 网格信息
  - stations (dict): 地面站信息
返回值：无
```

### 4. SpaceTaskSim_详细代码文档_04_轨道视野管理器.md
**需要修改的主要函数**:

```
# FlightManager
__init__(start_simulation_time, time_accuracy_digit, epoch, config_flight, config_topology)
功能描述：初始化轨道管理器，设置卫星轨道计算参数
参数：
  - start_simulation_time (float): 仿真开始时间
  - time_accuracy_digit (int): 时间精度位数
  - epoch (str): 仿真起始时间戳
  - config_flight (dict): 轨道配置字典
  - config_topology (dict): 拓扑配置字典
返回值：无

update(simulation_time)
功能描述：更新卫星轨道状态，计算新的位置和速度
参数：
  - simulation_time (float): 当前仿真时间
返回值：无

getSatInfos()
功能描述：获取所有卫星的位置和轨道信息
参数：无
返回值：dict - {sat_id: sat_info_dict} 卫星信息字典
```

### 5. SpaceTaskSim_详细代码文档_05_实体类与任务类.md
**需要修改的主要函数**:

```
# Satellite类
__init__(simulation_time, id, orbit_type, latitude, longitude, altitude, v_vector, function_enum_list, calculate_obj, compute_profile, relay_profile, sense_profile, name)
功能描述：初始化卫星节点，设置轨道、功能和资源配置
参数：
  - simulation_time (float): 仿真时间
  - id (str): 节点唯一ID
  - orbit_type (OrbitEnum): 轨道类型枚举
  - latitude (float): 纬度
  - longitude (float): 经度
  - altitude (float): 高度(km)
  - v_vector (tuple): 速度向量(ECEF坐标系)
  - function_enum_list (list): 功能枚举列表
  - calculate_obj (EarthSatellite): ephem计算对象
  - compute_profile (dict): 计算配置，可选
  - relay_profile (dict): 中继配置，可选
  - sense_profile (dict): 感知配置，可选
  - name (str): 卫星名称，可选
返回值：无
```

### 6. SpaceTaskSim_详细代码文档_06_枚举类与调度器.md
**需要修改的主要函数**:

```
# ResourceScheduler
checkCommunicationResourcesAvailable(env, tx_node_id, rx_node_id, required_trans_size, beam_usage_cache)
功能描述：检查通信资源是否可用于指定的传输任务
参数：
  - env (SpaceTaskSimEnv): 环境对象
  - tx_node_id (str): 发送节点ID
  - rx_node_id (str): 接收节点ID
  - required_trans_size (float): 所需传输大小
  - beam_usage_cache (dict): 波束使用缓存
返回值：bool - True表示资源可用
```

### 7. SpaceTaskSim_详细代码文档_07_策略模块详解.md
**需要修改的主要函数**:

```
# BaseStrategyModule
initialize(env, last_episode, final, eval)
功能描述：初始化策略模块，设置环境和参数
参数：
  - env (SpaceTaskSimEnv): 仿真环境对象
  - last_episode (int): 上次训练的轮次，用于模型加载
  - final (bool): 是否为最终评估模式
  - eval (bool): 是否为评估模式
返回值：无

scheduleStep()
功能描述：执行一步调度决策，根据策略算法做出调度决策
参数：无
返回值：无
```

### 8. SpaceTaskSim_详细代码文档_08_工具类与评估模块.md
**需要修改的主要函数**:

```
# geo_util
lla_to_ecef(lat, lon, alt)
功能描述：将地理坐标(LLA)转换为地心地固坐标(ECEF)
参数：
  - lat (float): 纬度(度)
  - lon (float): 经度(度)
  - alt (float): 高度(米)
返回值：tuple - (x, y, z) ECEF坐标(米)

calculate_distance(pos1, pos2)
功能描述：计算两点间的大圆距离
参数：
  - pos1 (tuple): 位置1 (lat, lon, alt)
  - pos2 (tuple): 位置2 (lat, lon, alt)
返回值：float - 距离(公里)
```

### 9. SpaceTaskSim_详细代码文档_09_Web应用接口.md
**状态**: ✅ 部分修改
- 已修改部分API接口格式

**需要继续修改的API**:

```
/api/nodes
方法：GET
功能描述：获取所有节点信息，支持类型和功能过滤
输入参数：
  - node_type (str): 节点类型过滤，可选SAT/GS/MU
  - function_type (str): 功能类型过滤，可选COMPUTATION/RELAY/SENSING
返回值：
  - nodes (list): 节点信息列表

/api/tasks
方法：GET
功能描述：获取任务信息，支持多种过滤条件和分页
输入参数：
  - task_type (str): 任务类型过滤，可选COMMUNICATION/COMPUTATION/SENSING
  - status (str): 任务状态过滤，可选EXECUTING/FINISHED/FAILED
  - limit (int): 返回数量限制
  - offset (int): 偏移量
返回值：
  - tasks (list): 任务信息列表
  - total_count (int): 总数量
  - filtered_count (int): 过滤后数量
```

### 10. SpaceTaskSim_详细代码文档_10_配置系统详解.md
**状态**: ✅ 已完成
- 基于实际config.yaml文件内容生成
- 格式符合要求

## 修改建议

1. **统一格式**: 所有函数都使用相同的格式，不包含具体代码实现
2. **简洁描述**: 功能描述要简洁明了，突出主要功能
3. **参数详细**: 每个参数都要说明类型和用途
4. **返回值明确**: 清楚说明返回值的类型和含义
5. **删除冗余**: 删除详细的代码实现，只保留API说明

## 批量修改脚本建议

可以使用以下Python脚本批量修改文档格式：

```python
import re

def convert_function_format(text):
    # 匹配Python函数定义格式
    pattern = r'```python\ndef (\w+)\((.*?)\):\s*"""(.*?)"""'
    
    def replace_func(match):
        func_name = match.group(1)
        params = match.group(2)
        docstring = match.group(3)
        
        # 解析docstring提取功能描述、参数、返回值
        # 转换为新格式
        return f"```\n{func_name}({params})\n功能描述：...\n参数：...\n返回值：...\n```"
    
    return re.sub(pattern, replace_func, text, flags=re.DOTALL)
```

## 完成状态总结

- ✅ 文档1: 项目概述与架构 (已完成)
- 🔄 文档2: 核心环境类 (部分完成)
- 🔄 文档3: 管理器类详解 (部分完成)
- ❌ 文档4: 轨道视野管理器 (待修改)
- ❌ 文档5: 实体类与任务类 (待修改)
- ❌ 文档6: 枚举类与调度器 (待修改)
- ❌ 文档7: 策略模块详解 (待修改)
- ❌ 文档8: 工具类与评估模块 (待修改)
- 🔄 文档9: Web应用接口 (部分完成)
- ✅ 文档10: 配置系统详解 (已完成)

建议按照上述格式继续修改剩余文档，确保所有API函数都遵循统一的格式标准。

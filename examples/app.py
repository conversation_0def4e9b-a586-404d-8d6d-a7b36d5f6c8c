

import os
import sys

project_root = "/mnt/data1/chenjiarui/project/spacetasksim"
sys.path.insert(0, project_root)
sys.path.append(os.path.join(os.path.dirname(__file__), '../spacetasksim'))
print("sys.path =", sys.path)

from spacetasksim.enum.task_type_enum import TaskTypeEnum

import yaml
import colorama
from flask import Flask, jsonify, request
from flask_cors import CORS
from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from spacetasksim.sche_strategy.base_strategy import BaseStrategyModule
from train import Random_StrategyModule,Greedy_StrategyModule,Heuristic_StrategyModule,Predict_StrategyModule,D3QN_StrategyModule,COMA_AC_StrategyModule,PTRMAPPO_StrategyModule,WMAPPO_StrategyModule
from train import parseD3QNArgs,parseCOMA_ACArgs,parsePTRMAPPOArgs,parseWMAPPOArgs
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from spacetasksim.utils import file_util



# Initialize colorama and Flask
colorama.init(autoreset=True)
app = Flask(__name__)
CORS(app) # 全局允许跨域请求

# Load configuration
config_path = sys.argv[1] if len(sys.argv) > 1 else os.path.join(os.path.dirname(__file__), 'config.yaml')
config = file_util.load_config(config_path)

# Initialize environment and modules
env = None
sche_strategy_module = None
evaluation_module=None
strategy_module_dict={
    'base':BaseStrategyModule(),
    'random':Random_StrategyModule(),
    'greedy':Greedy_StrategyModule(),
    'heuristic':Heuristic_StrategyModule(),
    'predict':Predict_StrategyModule(),
    'D3QN':D3QN_StrategyModule(),
    'COMA':COMA_AC_StrategyModule(),
    'TRMAPPO':PTRMAPPO_StrategyModule(),
    'WMAPPO':WMAPPO_StrategyModule()
}
arg_parser_dict={
    'D3QN':parseD3QNArgs,
    'COMA':parseCOMA_ACArgs,
    'TRMAPPO':parsePTRMAPPOArgs,
    'WMAPPO':parseWMAPPOArgs
}
learning_models=['D3QN','COMA','TRMAPPO','WMAPPO']
collaboration_models=['base','TRMAPPO']
strategy_module_name=None


def initialize_simulation():
    global env, sche_strategy_module, evaluation_module,strategy_module_name
    env = SpaceTaskSimEnv(config)
    strategy_module_name='base'
    sche_strategy_module = strategy_module_dict[strategy_module_name]
    sche_strategy_module.initialize(env)
    evaluation_module = SpaceTaskSimEvaluation(env, sche_strategy_module)
    return "Simulation initialized"


@app.route('/init', methods=['GET'])
@app.route('/setAlgorithm', methods=['GET'])
def init():
    global env,strategy_module_name,sche_strategy_module
    strategy_module_name = request.args.get('algorithm_name', type=str)  # 从 ?algorithm_name=xxx 中获取参数
    if not strategy_module_name:
        strategy_module_name='base'
    elif strategy_module_name not in strategy_module_dict:
        return jsonify({"code": 404,"status": "error", "message": f"Algorithm <{strategy_module_name}> not found"})
    sche_strategy_module = strategy_module_dict[strategy_module_name]
    if strategy_module_name in learning_models:
        args=arg_parser_dict[strategy_module_name]()
        model_base_dir=args.model_base_dir
        last_dir = os.path.basename(model_base_dir)  # "COMA_AC"
        new_path = os.path.join("../models/",  last_dir)  # "../COMA_AC"
        sys.argv.append(f"--model_base_dir={new_path}")  # 模拟命令行参数
        sche_strategy_module.initialize(env,last_episode=0,final= True,eval=True)
    else:
        sche_strategy_module.initialize(env)

    env.reset()
    sche_strategy_module.reset()
    evaluation_module.reset_sche_strategy_module(sche_strategy_module)

    result=f"Algorithm <{strategy_module_name}> init successfully"

    return jsonify({"code": 200,"status": "success", "message": result})


@app.route('/step', methods=['GET'])
def step():
    if env is None:
        return jsonify({"code": 404,"status": "error", "message": "Simulation not initialized"})

    if env.isDone():
        return jsonify({"code": 204,"status": "done", "message": "Simulation completed"})

    sche_strategy_module.scheduleStep()
    env.step()
    evaluation_module.evaluateStep()
    simulation_time=env.simulation_time
    message={
        'simulation_time':simulation_time
    }

    return jsonify({
        "code": 200,
        "status": "success",
        "message": message,
    })


@app.route('/reset', methods=['GET'])
def reset():
    if env is None:
        return jsonify({"code": 404,"status": "error", "message": "Simulation not initialized"})

    env.reset()
    sche_strategy_module.reset()
    evaluation_module.reset()

    return jsonify({"code": 200,"status": "success", "message": "Simulation reset"})

@app.route('/getSatPosition', methods=['GET'])
def getSatPosition():
    if env is None:
        return jsonify({"code": 404,"status": "error", "message": "Simulation not initialized"})
    sats_info=env.node_manager.getSatPositions()
    return jsonify({
        "code": 200,
        "status": "success",
        "message": sats_info,
    })

@app.route('/getWorkingSatResourceStates', methods=['GET'])
def getResourceState():
    if env is None:
        return jsonify({"code": 404,"status": "error", "message": "Simulation not initialized"})
    id = request.args.get('id', type=str)  # 从 ?id=xxx 中获取参数
    init_cn_transit_sat_ids=env.init_cn_transit_sat_ids
    if not id:
        working_sat_ids=init_cn_transit_sat_ids
    else:
        if id not in init_cn_transit_sat_ids:
            return jsonify({"code": 404,"status": "error", "message": "Satellite not found"})
        working_sat_ids=[id]

    state_info={}
    for sat_id in working_sat_ids:
        comm_resource_states=env.resource_manager.communication_manager.getResourceUsage(sat_id)
        comp_resource_states=env.resource_manager.computation_manager.getResourceUsage(sat_id)
        sensing_resource_states=env.resource_manager.sensing_manager.getResourceUsage(sat_id)
        storage_resource_states=env.resource_manager.storage_manager.getResourceUsage(sat_id)
        state_info[sat_id]={
            'communication':comm_resource_states,
            'computation':comp_resource_states,
            'sensing':sensing_resource_states,
            'storage':storage_resource_states,
        }
    return jsonify({
        "code": 200,
        "status": "success",
        "message": state_info,
    })

@app.route('/getGlobalTaskExeStates', methods=['GET'])
def getGlobalTaskExeStates():
    if env is None:
        return jsonify({"code": 404,"status": "error", "message": "Simulation not initialized"})
    executing_tasks=env.task_manager._executing_tasks
    executing_num={
        'communication':len(executing_tasks[TaskTypeEnum.COMMUNICATION]),
        'computation':len(executing_tasks[TaskTypeEnum.COMPUTATION]),
        'sensing':len(executing_tasks[TaskTypeEnum.SENSING]),
        'sum':len(executing_tasks[TaskTypeEnum.COMMUNICATION])+len(executing_tasks[TaskTypeEnum.COMPUTATION])+len(executing_tasks[TaskTypeEnum.SENSING])
    }
    finish_num={
        'communication':evaluation_module.finish_num_comm,
        'computation':evaluation_module.finish_num_comp,
        'sensing':evaluation_module.finish_num_sensing,
        'sum':evaluation_module.finish_num
    }
    fail_num={
        'communication':evaluation_module.fail_num_comm,
        'computation':evaluation_module.fail_num_comp,
        'sensing':evaluation_module.fail_num_sensing,
        'sum':evaluation_module.fail_num
    }
    completion_ratio={
        'communication':evaluation_module.completion_ratio_comm,
        'computation':evaluation_module.completion_ratio_comp,
        'sensing':evaluation_module.completion_ratio_sensing,
        'sum':evaluation_module.completion_ratio
    }
    task_exe_states={
        'executing_num':executing_num,
        'finish_num':finish_num,
        'fail_num':fail_num,
        'completion_ratio':completion_ratio,
    }

    return jsonify({
        "code": 200,
        "status": "success",
        "message": task_exe_states,
    })

@app.route('/getOffloadingLogs', methods=['GET'])
def getOffloadingLogs():
    id = request.args.get('id', type=str)  # 从 ?id=xxx 中获取参数
    init_cn_transit_sat_ids=env.init_cn_transit_sat_ids
    if not id:
        sat_ids=init_cn_transit_sat_ids
    else:
        if id not in init_cn_transit_sat_ids:
            return jsonify({"code": 404,"status": "error", "message": "Satellite not found"})
        sat_ids=[id]
    logs={}
    for sat_id in sat_ids:
        logs[sat_id]=env.task_manager.log.get(sat_id,[])
    return jsonify({
        "code": 200,
        "status": "success",
        "message": logs,
    })

@app.route('/getIncrementalOffloadingLogs', methods=['GET'])
def getIncrementalOffloadingLogs():
    id = request.args.get('id', type=str)  # 从 ?id=xxx 中获取参数
    init_cn_transit_sat_ids=env.init_cn_transit_sat_ids
    if not id:
        sat_ids=init_cn_transit_sat_ids
    else:
        if id not in init_cn_transit_sat_ids:
            return jsonify({"code": 404,"status": "error", "message": "Satellite not found"})
        sat_ids=[id]
    logs={}
    for sat_id in sat_ids:
        logs[sat_id]=env.task_manager.incremental_log.get(sat_id,[])
        env.task_manager.incremental_log[sat_id]=[]
    return jsonify({
        "code": 200,
        "status": "success",
        "message": logs,
    })

@app.route('/getAlgorithmTag', methods=['GET'])
def getAlgorithmTag():
    return jsonify({
        "code": 200,
        "status": "success",
        "message": strategy_module_name
    })

@app.route('/getAvailableAlgorithms', methods=['GET'])
def getAvailableAlgorithms():
    return jsonify({
        "code": 200,
        "status": "success",
        "message": list(strategy_module_dict.keys())
    })

@app.route('/getCollaborationGroup', methods=['GET'])
def getCollaborationGroup():
    if env is None:
        return jsonify({"code": 404,"status": "error", "message": "Simulation not initialized"})
    if strategy_module_name not in collaboration_models:
        return jsonify({"code": 404,"status": "error", "message": "Collaboration group division is not supported"})
    collaboration_group=sche_strategy_module.collaboration_group
    return jsonify({
        "code": 200,
        "status": "success",
        "message": collaboration_group,
    })

if __name__ == '__main__':
    # Initialize simulation on startup
    initialize_simulation()
    # Start Flask server
    app.run(host='0.0.0.0', port=5051)
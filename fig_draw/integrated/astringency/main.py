import os
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

# 定义颜色（黄、绿、蓝）
colors = {
    'yellow': (248 / 255, 230 / 255, 32 / 255),
    'green': (53 / 255, 183 / 255, 119 / 255),
    'blue': (48 / 255, 104 / 255, 141 / 255)
}

# 定义算法名称和对应的日志文件夹路径
algorithms = {
    'COMA_AC': '../../../train/COMA_AC2/logs/',
    'D3QN': '../../../train/D3QN/logs/',
    'ST-PT': '../../../train/PTMAPPO_Reptile2/logs/'
}


def plot_metric(metric_name, ylabel, save_name):
    """绘制指定指标的曲线图"""
    plt.figure(figsize=(10, 6))
    for algo_name, color in zip(algorithms.keys(), colors.values()):
        log_dir = algorithms[algo_name]

        # 初始化 EventAccumulator
        event_acc = EventAccumulator(log_dir)
        event_acc.Reload()

        # 检查是否存在目标数据
        tags = event_acc.Tags().get('scalars', [])
        if metric_name not in tags:
            print(f"Warning: '{metric_name}' not found in {algo_name}'s logs.")
            continue

        # 提取数据
        metric_data = event_acc.Scalars(metric_name)
        steps = [event.step for event in metric_data]
        values = [event.value for event in metric_data]

        # 绘制曲线
        plt.plot(steps, values, label=algo_name, color=color, linewidth=2)

    # 添加图例和标签
    plt.title(f'Reward Curve of Different Algorithms', fontsize=14,y=1.1)
    plt.xlabel('Episodes', fontsize=12)
    plt.ylabel('Avg Reward', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(
        loc='lower center',
        bbox_to_anchor=(0.5, 1),
        fancybox=True,
        shadow=False,
        ncol=3,
        fontsize=12
    )

    # 保存图像
    plt.tight_layout()
    plt.savefig(f'{save_name}.png', bbox_inches='tight', dpi=300)
    plt.show()


# 绘制 Reward 曲线
plot_metric(metric_name='sum_reward', ylabel='Reward', save_name='reward_curve')

# 绘制 Completion 曲线（假设标签为 'completion'）
plot_metric(metric_name='succ_ratio', ylabel='Completion Ratio', save_name='completion_curve')
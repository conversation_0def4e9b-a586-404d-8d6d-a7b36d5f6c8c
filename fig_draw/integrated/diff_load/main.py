import json
import statistics

import numpy as np
from matplotlib import pyplot as plt


def ema_smoothing(data, alpha=0.8):
    smoothed = [data[0]]  # 初始值为第一个数据点
    for i in range(1, len(data)):
        smoothed.append(alpha * smoothed[i - 1] + (1 - alpha) * data[i])
    return smoothed


color = {
    'yellow': (248 / 255, 230 / 255, 32 / 255),
    'yellow_green': (145 / 255, 213 / 255, 66 / 255),
    'green': (53 / 255, 183 / 255, 119 / 255),
    'green_blue': (31 / 146, 146 / 255, 139 / 255),
    'blue': (48 / 255, 104 / 255, 141 / 255),
    'blue_purple': (65 / 255, 62 / 255, 133 / 255),
    'purple': (68 / 255, 4 / 255, 90 / 255),
    'pink': (255 / 255, 192 / 255, 203 / 255),
    'orange': (255 / 255, 102 / 255, 0 / 255)
}
marker = {
    'random': 'x',
    'greedy': 'o',
    'heuristic': 'D',
    'COMA_AC': 's',
    'D3QN': '^',
    'ST-PT': 'v',
}
method_color = {
    'random': color['yellow'],
    'greedy': color['yellow_green'],
    'heuristic': color['green'],
    'COMA_AC': color['green_blue'],
    'D3QN': color['blue'],
    'ST-PT': color['blue_purple'],
}

paths={
    'random':{
        'low': "../../../train_low/random/evaluation/final/evaluation.json",
        'mid_low': "../../../train_mid_low/random/evaluation/final/evaluation.json",
        'mid': "../../../train/random/evaluation/final/evaluation.json",
        'mid_high': "../../../train_mid_high/random/evaluation/final/evaluation.json",
        'high': "../../../train_high/random/evaluation/final/evaluation.json"
    },
    'greedy':{
        'low': "../../../train_low/greedy/evaluation/final/evaluation.json",
        'mid_low': "../../../train_mid_low/greedy/evaluation/final/evaluation.json",
        'mid': "../../../train/greedy/evaluation/final/evaluation.json",
        'mid_high': "../../../train_mid_high/greedy/evaluation/final/evaluation.json",
        'high': "../../../train_high/greedy/evaluation/final/evaluation.json"
    },
    'heuristic':{
        'low': "../../../train_low/heuristic/evaluation/final/evaluation.json",
        'mid_low': "../../../train_mid_low/heuristic/evaluation/final/evaluation.json",
        'mid': "../../../train/heuristic/evaluation/final/evaluation.json",
        'mid_high': "../../../train_mid_high/heuristic/evaluation/final/evaluation.json",
        'high': "../../../train_high/heuristic/evaluation/final/evaluation.json"
    },
    'COMA_AC':{
        'low': "../../../train_low/COMA_AC2/evaluation/final/evaluation.json",
        'mid_low': "../../../train_mid_low/COMA_AC2/evaluation/final/evaluation.json",
        'mid': "../../../train/COMA_AC2/evaluation/final/evaluation.json",
        'mid_high': "../../../train_mid_high/COMA_AC2/evaluation/final/evaluation.json",
        'high': "../../../train_high/COMA_AC2/evaluation/final/evaluation.json"
    },
    'D3QN': {
        'low': "../../../train_low/D3QN2/evaluation/final/evaluation.json",
        'mid_low': "../../../train_mid_low/D3QN2/evaluation/final/evaluation.json",
        'mid': "../../../train_mid/D3QN2/evaluation/final/evaluation.json",
        'mid_high': "../../../train_mid_high/D3QN2/evaluation/final/evaluation.json",
        'high': "../../../train_high/D3QN2/evaluation/final/evaluation.json"
    },
    'ST-PT':{
        'low': "../../../train_low/PTMAPPO_Reptile/evaluation/final/evaluation.json",
        'mid_low': "../../../train_mid_low/PTMAPPO_Reptile/evaluation/final/evaluation.json",
        'mid': "../../../train_mid/PTMAPPO_Reptile/evaluation/final/evaluation.json",
        'mid_high': "../../../train_mid_high/PTMAPPO_Reptile/evaluation/final/evaluation.json",
        'high': "../../../train_high/PTMAPPO_Reptile/evaluation/final/evaluation.json"
    }
}

save_dir = "./"
# groups = ["random", "greedy", "heuristic", "predict", "MAPPO","D3QN", "ST-PT"]
groups = ["random", "greedy", "heuristic","COMA_AC","D3QN", "ST-PT"]
# loads = ['low','mid_low',  'mid', 'mid_high', 'high']
# x_values = ['low','mid_low',  'mid', 'mid_high', 'high']
loads = ['low',  'mid', 'mid_high']
x_values = ['low',  'mid', 'high']
contents = ['all', 'comm', 'comp', 'sensing']

completion_data={
    'completion_data_all':{},
    'completion_data_comm':{},
    'completion_data_comp':{},
    'completion_data_sensing':{}
}

time_data={
    'time_data_all':{},
    'time_data_comm':{},
    'time_data_comp':{},
    'time_data_sensing':{}
}

# 原始数据收集（用于箱式图）
raw_completion_data = {f'raw_completion_{content}': {group: {load: [] for load in loads} for group in groups} for content in contents}
raw_time_data = {f'raw_time_{content}': {group: {load: [] for load in loads} for group in groups} for content in contents}
for group in groups:
    for key, value in completion_data.items():
        value[group]=[]
    for key, value in time_data.items():
        value[group]=[]
    for load in loads:
        with open(paths[group][load], 'r') as file:
            data = json.load(file)
            completion_ratio = [entry["episode_completion_ratio"] for entry in data]
            completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
            completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
            completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
            floating_time = [entry["episode_avg_terminated_time"] for entry in data]
            floating_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
            floating_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
            floating_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]

            # 收集原始数据（用于箱式图）
            raw_completion_data['raw_completion_all'][group][load] = [entry["episode_completion_ratio"] for entry in
                                                                      data]
            raw_completion_data['raw_completion_comm'][group][load] = [entry["episode_completion_ratio_comm"] for entry
                                                                       in data]
            raw_completion_data['raw_completion_comp'][group][load] = [entry["episode_completion_ratio_comp"] for entry
                                                                       in data]
            raw_completion_data['raw_completion_sensing'][group][load] = [entry["episode_completion_ratio_sensing"] for
                                                                          entry in data]

            raw_time_data['raw_time_all'][group][load] = [entry["episode_avg_terminated_time"] for entry in data]
            raw_time_data['raw_time_comm'][group][load] = [entry["episode_avg_terminated_time_comm"] for entry in data]
            raw_time_data['raw_time_comp'][group][load] = [entry["episode_avg_terminated_time_comp"] for entry in data]
            raw_time_data['raw_time_sensing'][group][load] = [entry["episode_avg_terminated_time_sensing"] for entry in
                                                              data]

            # 计算平均值（用于折线图）
            avg_completion_ratio = sum(completion_ratio) / len(completion_ratio)
            avg_completion_ratio_comm = sum(completion_ratio_comm) / len(completion_ratio_comm)
            avg_completion_ratio_comp = sum(completion_ratio_comp) / len(completion_ratio_comp)
            avg_completion_ratio_sensing = sum(completion_ratio_sensing) / len(completion_ratio_sensing)
            avg_floating_time = sum(floating_time) / len(floating_time)
            avg_floating_time_comm = sum(floating_time_comm) / len(floating_time_comm)
            avg_floating_time_comp = sum(floating_time_comp) / len(floating_time_comp)
            avg_floating_time_sensing = sum(floating_time_sensing) / len(floating_time_sensing)
            completion_data['completion_data_all'][group].append(avg_completion_ratio)
            completion_data['completion_data_comm'][group].append(avg_completion_ratio_comm)
            completion_data['completion_data_comp'][group].append(avg_completion_ratio_comp)
            completion_data['completion_data_sensing'][group].append(avg_completion_ratio_sensing)
            time_data['time_data_all'][group].append(avg_floating_time)
            time_data['time_data_comm'][group].append(avg_floating_time_comm)
            time_data['time_data_comp'][group].append(avg_floating_time_comp)
            time_data['time_data_sensing'][group].append(avg_floating_time_sensing)

            print(group, load, avg_completion_ratio, avg_completion_ratio_comm, avg_completion_ratio_comp, avg_completion_ratio_sensing)


for content in contents:
    # 绘制折线图
    plt.figure(clear=True)
    for group in groups:
        plt.plot(x_values, completion_data[f'completion_data_{content}'][group], marker=marker[group], label=group,color=method_color[group])
    # 添加标题和标签
    plt.title(f'Completion Ratio - {content}', y=1.2)
    plt.xlabel('load')
    plt.ylabel('Completion Ratio')
    plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
    plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=3)
    plt.tight_layout()
    plt.savefig(save_dir + f'load_CompletionRatio_{content}.png')
    plt.close()

    # 绘制折线图
    plt.figure(clear=True)
    for group in groups:
        plt.plot(x_values, time_data[f'time_data_{content}'][group], marker=marker[group], label=group,color=method_color[group])
    # 添加标题和标签
    plt.title(f'Time Delay Distribution - {content}', y=1.2)
    plt.xlabel('load')
    plt.ylabel('Delay(s)')
    plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
    plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=3)
    plt.tight_layout()
    plt.savefig(save_dir + f'load_Time_{content}.png')
    plt.close()

# 新增箱式图绘制（按load分组，组内显示不同方法）
for content in contents:
    # 完成率箱式图
    plt.figure(clear=True)
    # 设置每个load组的偏移量
    n_methods = len(groups)
    width = 0.6  # 每组总宽度
    offset = np.linspace(-width / 2, width / 2, n_methods)*1.05
    # 为每种方法创建代理艺术家（用于图例）
    proxy_artists = []
    x_centers = np.arange(len(x_values))  # Low/Medium/High的中心坐标 [0,1,2]
    for i, group in enumerate(groups):
        # 收集当前方法在所有load下的数据
        data = [raw_completion_data[f'raw_completion_{content}'][group][load] for load in
                loads]  # 修改：使用x_values而不是loads
        # 计算箱体位置（以x_centers为基准对称偏移）
        positions = x_centers + offset[i]
        # 绘制箱线图
        box = plt.boxplot(data,
                          positions=positions,
                          widths=width / n_methods * 0.6,
                          patch_artist=True,
                          showfliers=False,
                          boxprops={'edgecolor': 'black', 'linewidth': 0.5},
                          medianprops={'color': 'black', 'linewidth': 1.5})
        # 设置样式
        for patch in box['boxes']:
            patch.set_facecolor(method_color[group])
            patch.set_alpha(0.9)
        # 保存代理艺术家（用于图例）
        proxy_artists.append(plt.Rectangle((0, 0), 1, 1,
                                           fc=method_color[group],
                                           alpha=0.9,
                                           label=group))
    # 设置x轴
    plt.xticks(x_centers, x_values)  # 标签严格对准中心
    plt.xlim(x_centers[0] - 0.5, x_centers[-1] + 0.5)  # 控制边界
    plt.xlabel('Load Level')
    # 设置y轴
    plt.ylabel('Completion Ratio')
    plt.title(f'Completion Ratio Distribution - {content}', y=1.2)

    # 添加辅助线（增强分组视觉）
    for x in x_centers:
        plt.axvline(x + 0.5, color='gray', linestyle=':', alpha=0.3, linewidth=0.8)
    # 添加图例（使用代理艺术家）
    plt.legend(handles=proxy_artists,  # 关键修改：明确指定handles
               loc='lower center',
               bbox_to_anchor=(0.5, 1),
               fancybox=True,
               shadow=False,
               ncol=3)
    plt.grid(axis='y', linestyle='--', alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{save_dir}boxplot_CompletionRatio_{content}.png', dpi=300, bbox_inches='tight')
    plt.close()


    # 时间箱式图
    plt.figure(clear=True)
    proxy_artists = []

    # 参数设置
    n_methods = len(groups)
    width = 0.6  # 缩小总宽度以增加组间距
    offset = np.linspace(-width / 2, width / 2, n_methods) * 1.05  # 偏移系数1.2倍
    x_centers = np.arange(len(x_values))  # Low/Medium/High的中心坐标 [0,1,2]

    for i, group in enumerate(groups):
        # 收集数据
        data = [raw_time_data[f'raw_time_{content}'][group][load] for load in loads]

        # 计算箱体位置（以x_centers为基准对称偏移）
        positions = x_centers + offset[i]

        # 绘制箱线图
        box = plt.boxplot(data,
                          positions=positions,
                          widths=width / n_methods * 0.6,  # 缩窄单个箱体宽度
                          patch_artist=True,
                          showfliers=False,
                          boxprops={'edgecolor': 'black', 'linewidth': 0.5},
                          medianprops={'color': 'black', 'linewidth': 1.5})

        # 设置样式
        for patch in box['boxes']:
            patch.set_facecolor(method_color[group])
            patch.set_alpha(0.9)  # 提高透明度

        # 图例代理
        proxy_artists.append(plt.Rectangle((0, 0), 1, 1,
                                           fc=method_color[group],
                                           alpha=0.9,
                                           label=group))

    # 坐标轴设置
    plt.xticks(x_centers, x_values)  # 标签严格对准中心
    plt.xlim(x_centers[0] - 0.5, x_centers[-1] + 0.5)  # 控制边界
    plt.xlabel('Load Level')
    plt.ylabel('Delay (s)')
    plt.title(f'Time Delay Distribution - {content}', y=1.2)

    # 添加辅助线（增强分组视觉）
    for x in x_centers:
        plt.axvline(x + 0.5, color='gray', linestyle=':', alpha=0.3, linewidth=0.8)

    # 图例与网格
    plt.legend(handles=proxy_artists,
               loc='lower center',
               bbox_to_anchor=(0.5, 1),
               fancybox=True,
               shadow=False,
               ncol=3)
    plt.grid(axis='y', linestyle='--', alpha=0.3)

    # 输出
    plt.tight_layout()
    plt.savefig(f'{save_dir}boxplot_Time_{content}.png', dpi=300, bbox_inches='tight')
    plt.close()
import json
import statistics

import numpy as np
from matplotlib import pyplot as plt


def ema_smoothing(data, alpha=0.9):
    smoothed = [data[0]]  # 初始值为第一个数据点
    for i in range(1, len(data)):
        smoothed.append(alpha * smoothed[i - 1] + (1 - alpha) * data[i])
    return smoothed


color = {
    'yellow': (248 / 255, 230 / 255, 32 / 255),
    'yellow_green': (145 / 255, 213 / 255, 66 / 255),
    'green': (53 / 255, 183 / 255, 119 / 255),
    'green_blue': (31 / 146, 146 / 255, 139 / 255),
    'blue': (48 / 255, 104 / 255, 141 / 255),
    'blue_purple': (65 / 255, 62 / 255, 133 / 255),
    'purple': (68 / 255, 4 / 255, 90 / 255),
    'pink': (255 / 255, 192 / 255, 203 / 255),
    'orange': (255 / 255, 102 / 255, 0 / 255)
}
marker = {
    'random': 'x',
    'greedy': 'o',
    'heuristic': 'D',
    'COMA_AC': 's',
    'D3QN': '^',
    'WMAPPO': 'p',
    'TRMAPPO': 'v',
}
method_color = {
    'random': color['yellow'],
    'greedy': color['yellow_green'],
    'heuristic': color['green'],
    'COMA_AC': color['green_blue'],
    'D3QN': color['blue'],
    'WMAPPO': color['blue_purple'],
    'TRMAPPO': color['purple'],
}


file_paths={
    'random': "../../../train/random_real/evaluation/final/evaluation.json",
    'greedy': "../../../train/greedy_real/evaluation/final/evaluation.json",
    'heuristic': "../../../train/predict/evaluation/final/evaluation.json",
    'COMA_AC': "../../../train/COMA_AC2/evaluation/final/evaluation.json",
    'D3QN': "../../../train_mid/D3QN/evaluation/final/evaluation.json",
    'WMAPPO': "../../../train/WMAPPO/evaluation_lr1/final/evaluation.json",
    'TRMAPPO': "../../../train_mid/PTMAPPO_Reptile/evaluation/final/evaluation.json"
}
save_dir = "./"
max_episodes=100


categories = ['all', 'comm', 'comp', 'sensing']
# algorithms = ['random', 'greedy', 'heuristic','COMA_AC','D3QN','WMAPPO','TRMAPPO']
algorithms = ['random', 'greedy', 'heuristic','COMA_AC','D3QN','WMAPPO','TRMAPPO']

start_episode=0
end_episode=100

completion_data={}
time_data={}
for algorithm in algorithms:
    completion_data[algorithm]={}
    time_data[algorithm] = {}
for algorithm in algorithms:
    file_path=file_paths[algorithm]
    with open(file_path, 'r') as file:
        data = json.load(file)
        completion_ratio = [entry["episode_completion_ratio"] for entry in data][start_episode:end_episode]
        completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data][start_episode:end_episode]
        completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data][start_episode:end_episode]
        completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data][start_episode:end_episode]
        floating_time = [entry["episode_avg_terminated_time"] for entry in data][start_episode:end_episode]
        floating_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data][start_episode:end_episode]
        floating_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data][start_episode:end_episode]
        floating_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data][start_episode:end_episode]

        # 计算平均值（用于折线图）
        avg_completion_ratio = ema_smoothing(completion_ratio)[-1]
        avg_completion_ratio_comm = ema_smoothing(completion_ratio_comm)[-1]
        avg_completion_ratio_comp = ema_smoothing(completion_ratio_comp)[-1]
        avg_completion_ratio_sensing = ema_smoothing(completion_ratio_sensing)[-1]
        avg_floating_time = ema_smoothing(floating_time)[-1]
        avg_floating_time_comm = ema_smoothing(floating_time_comm)[-1]
        avg_floating_time_comp = ema_smoothing(floating_time_comp)[-1]
        avg_floating_time_sensing = ema_smoothing(floating_time_sensing)[-1]

        # # 计算平均值（用于折线图）
        # avg_completion_ratio = sum(completion_ratio) / len(completion_ratio)
        # avg_completion_ratio_comm = sum(completion_ratio_comm) / len(completion_ratio_comm)
        # avg_completion_ratio_comp = sum(completion_ratio_comp) / len(completion_ratio_comp)
        # avg_completion_ratio_sensing = sum(completion_ratio_sensing) / len(completion_ratio_sensing)
        # avg_floating_time = sum(floating_time) / len(floating_time)
        # avg_floating_time_comm = sum(floating_time_comm) / len(floating_time_comm)
        # avg_floating_time_comp = sum(floating_time_comp) / len(floating_time_comp)
        # avg_floating_time_sensing = sum(floating_time_sensing) / len(floating_time_sensing)

        completion_data[algorithm]['all'] = avg_completion_ratio
        completion_data[algorithm]['comm'] = avg_completion_ratio_comm
        completion_data[algorithm]['comp'] = avg_completion_ratio_comp
        completion_data[algorithm]['sensing'] = avg_completion_ratio_sensing
        time_data[algorithm]['all'] = avg_floating_time
        time_data[algorithm]['comm'] = avg_floating_time_comm
        time_data[algorithm]['comp'] = avg_floating_time_comp
        time_data[algorithm]['sensing'] = avg_floating_time_sensing

# 绘图设置
bar_width = 0.12
x = np.arange(len(categories))


# 完成率图
plt.figure(clear=True,figsize=(8, 6)) #figsize=(12, 6)
# 绘制柱状图
for i, algo in enumerate(algorithms):
    values = [completion_data[algo][cat] for cat in categories]
    plt.bar(x + i*bar_width, values, width=bar_width,
            color=method_color[algo], label=algo)
# 添加标签和标题
plt.xlabel('Task Categories')
plt.ylabel('Completion Ratio')
# plt.title('Task Completion Ratio by Algorithm and Category')
plt.xticks(x + bar_width*1.5, categories)
plt.yticks()
plt.ylim(0, 1)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1), fancybox=True, shadow=False,  ncol=4)
plt.grid(True, axis='y', linestyle='--', alpha=0.7)
# 显示数值标签
for i, algo in enumerate(algorithms):
    for j, cat in enumerate(categories):
        height = completion_data[algo][cat]
        plt.text(x[j] + i*bar_width, height + 0.01,
                 f'{height:.3f}',
                 ha='center', va='bottom',fontsize=5)
plt.tight_layout()
plt.savefig('completion_load3_all.png')
plt.show()

# 时延图
plt.figure(clear=True,figsize=(8, 6)) #figsize=(12, 6)
# 绘制柱状图
for i, algo in enumerate(algorithms):
    values = [time_data[algo][cat] for cat in categories]
    plt.bar(x + i*bar_width, values, width=bar_width,
            color=method_color[algo], label=algo)
# 添加标签和标题
plt.xlabel('Task Categories')
plt.ylabel('Delay (s)')  # 修改ylabel
plt.xticks(x + bar_width*1.5, categories)
plt.yticks()
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
plt.grid(True, axis='y', linestyle='--', alpha=0.7)
# 显示数值标签（使用time_data而非completion_data）
for i, algo in enumerate(algorithms):
    for j, cat in enumerate(categories):
        time_value = time_data[algo][cat]
        plt.text(x[j] + i*bar_width, time_value + 0.01,  # 动态偏移量
                 f'{time_value:.2f}s',  # 添加单位
                 ha='center', va='bottom',fontsize=5)
plt.tight_layout()
plt.savefig('time_load3_all.png')
plt.show()


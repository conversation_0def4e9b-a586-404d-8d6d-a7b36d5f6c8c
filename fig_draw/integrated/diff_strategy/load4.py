import json
import statistics

import numpy as np
from matplotlib import pyplot as plt


def ema_smoothing(data, alpha=0.8):
    smoothed = [data[0]]  # 初始值为第一个数据点
    for i in range(1, len(data)):
        smoothed.append(alpha * smoothed[i - 1] + (1 - alpha) * data[i])
    return smoothed


color = {
    'yellow': (248 / 255, 230 / 255, 32 / 255),
    'green': (53 / 255, 183 / 255, 119 / 255),
    'blue': (48 / 255, 104 / 255, 141 / 255),
    'purple': (68 / 255, 4 / 255, 90 / 255)
}
marker = {
    'random': 'x',
    'greedy': 'o',
    'heuristic': 'D',
    'predict': 's'
}
method_color = {
    'random': color['green'],
    'greedy': color['blue'],
    'heuristic': color['yellow'],
    'predict': color['purple']
}

random_file_path = "../../train_mid_high/random/evaluation/final/evaluation.json"
greedy_file_path = "../../train_mid_high/greedy/evaluation/final/evaluation.json"
heuristic_file_path = "../../train_mid_high/heuristic_local/evaluation/final/evaluation.json"
predict_file_path = "../../train_mid_high/predict/evaluation/final/evaluation.json"

save_dir = "./"

with open(random_file_path, 'r') as file:
    data = json.load(file)
    random_completion_ratio = [entry["episode_completion_ratio"] for entry in data]
    random_completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
    random_completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
    random_completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
    random_avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data]
    random_avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
    random_avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
    random_avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]
    random_completion_ratio = sum(random_completion_ratio) / len(random_completion_ratio)
    random_completion_ratio_comm = sum(random_completion_ratio_comm) / len(random_completion_ratio_comm)
    random_completion_ratio_comp = sum(random_completion_ratio_comp) / len(random_completion_ratio_comp)
    random_completion_ratio_sensing = sum(random_completion_ratio_sensing) / len(random_completion_ratio_sensing)
    random_avg_terminated_time = sum(random_avg_terminated_time) / len(random_avg_terminated_time)
    random_avg_terminated_time_comm = sum(random_avg_terminated_time_comm) / len(random_avg_terminated_time_comm)
    random_avg_terminated_time_comp = sum(random_avg_terminated_time_comp) / len(random_avg_terminated_time_comp)
    random_avg_terminated_time_sensing = sum(random_avg_terminated_time_sensing) / len(random_avg_terminated_time_sensing)

with open(greedy_file_path, 'r') as file:
    data = json.load(file)
    greedy_completion_ratio = [entry["episode_completion_ratio"] for entry in data]
    greedy_completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
    greedy_completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
    greedy_completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
    greedy_avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data]
    greedy_avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
    greedy_avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
    greedy_avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]
    greedy_completion_ratio = sum(greedy_completion_ratio) / len(greedy_completion_ratio)
    greedy_completion_ratio_comm = sum(greedy_completion_ratio_comm) / len(greedy_completion_ratio_comm)
    greedy_completion_ratio_comp = sum(greedy_completion_ratio_comp) / len(greedy_completion_ratio_comp)
    greedy_completion_ratio_sensing = sum(greedy_completion_ratio_sensing) / len(greedy_completion_ratio_sensing)
    greedy_avg_terminated_time = sum(greedy_avg_terminated_time) / len(greedy_avg_terminated_time)
    greedy_avg_terminated_time_comm = sum(greedy_avg_terminated_time_comm) / len(greedy_avg_terminated_time_comm)
    greedy_avg_terminated_time_comp = sum(greedy_avg_terminated_time_comp) / len(greedy_avg_terminated_time_comp)
    greedy_avg_terminated_time_sensing = sum(greedy_avg_terminated_time_sensing) / len(greedy_avg_terminated_time_sensing)


with open(heuristic_file_path, 'r') as file:
    data = json.load(file)
    heuristic_completion_ratio = [entry["episode_completion_ratio"] for entry in data]
    heuristic_completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
    heuristic_completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
    heuristic_completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
    heuristic_avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data]
    heuristic_avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
    heuristic_avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
    heuristic_avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]
    heuristic_completion_ratio = sum(heuristic_completion_ratio) / len(heuristic_completion_ratio)
    heuristic_completion_ratio_comm = sum(heuristic_completion_ratio_comm) / len(heuristic_completion_ratio_comm)
    heuristic_completion_ratio_comp = sum(heuristic_completion_ratio_comp) / len(heuristic_completion_ratio_comp)
    heuristic_completion_ratio_sensing = sum(heuristic_completion_ratio_sensing) / len(heuristic_completion_ratio_sensing)
    heuristic_avg_terminated_time = sum(heuristic_avg_terminated_time) / len(heuristic_avg_terminated_time)
    heuristic_avg_terminated_time_comm = sum(heuristic_avg_terminated_time_comm) / len(heuristic_avg_terminated_time_comm)
    heuristic_avg_terminated_time_comp = sum(heuristic_avg_terminated_time_comp) / len(heuristic_avg_terminated_time_comp)
    heuristic_avg_terminated_time_sensing = sum(heuristic_avg_terminated_time_sensing) / len(heuristic_avg_terminated_time_sensing)

with open(predict_file_path, 'r') as file:
    data = json.load(file)
    predict_completion_ratio = [entry["episode_completion_ratio"] for entry in data]
    predict_completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
    predict_completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
    predict_completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
    predict_avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data]
    predict_avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
    predict_avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
    predict_avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]
    predict_completion_ratio = sum(predict_completion_ratio) / len(predict_completion_ratio)
    predict_completion_ratio_comm = sum(predict_completion_ratio_comm) / len(predict_completion_ratio_comm)
    predict_completion_ratio_comp = sum(predict_completion_ratio_comp) / len(predict_completion_ratio_comp)
    predict_completion_ratio_sensing = sum(predict_completion_ratio_sensing) / len(predict_completion_ratio_sensing)
    predict_avg_terminated_time = sum(predict_avg_terminated_time) / len(predict_avg_terminated_time)
    predict_avg_terminated_time_comm = sum(predict_avg_terminated_time_comm) / len(predict_avg_terminated_time_comm)
    predict_avg_terminated_time_comp = sum(predict_avg_terminated_time_comp) / len(predict_avg_terminated_time_comp)
    predict_avg_terminated_time_sensing = sum(predict_avg_terminated_time_sensing) / len(predict_avg_terminated_time_sensing)

completion_data = {
    'random': {
        'all': random_completion_ratio,
        'comm': random_completion_ratio_comm,
        'comp': random_completion_ratio_comp,
        'sensing': random_completion_ratio_sensing
    },
    'greedy': {
        'all': greedy_completion_ratio,
        'comm': greedy_completion_ratio_comm,
        'comp': greedy_completion_ratio_comp,
        'sensing': greedy_completion_ratio_sensing
    },
    'heuristic': {
        'all': heuristic_completion_ratio,
        'comm': heuristic_completion_ratio_comm,
        'comp': heuristic_completion_ratio_comp,
        'sensing': heuristic_completion_ratio_sensing
    },
    'predict': {
        'all': predict_completion_ratio,
        'comm': predict_completion_ratio_comm,
        'comp': predict_completion_ratio_comp,
        'sensing': predict_completion_ratio_sensing
    }
}

time_data = {
    'random': {
        'all': random_avg_terminated_time,
        'comm': random_avg_terminated_time_comm,
        'comp': random_avg_terminated_time_comp,
        'sensing': random_avg_terminated_time_sensing
    },
    'greedy': {
        'all': greedy_avg_terminated_time,
        'comm': greedy_avg_terminated_time_comm,
        'comp': greedy_avg_terminated_time_comp,
        'sensing': greedy_avg_terminated_time_sensing
    },
    'heuristic': {
        'all': heuristic_avg_terminated_time,
        'comm': heuristic_avg_terminated_time_comm,
        'comp': heuristic_avg_terminated_time_comp,
        'sensing': heuristic_avg_terminated_time_sensing
    },
    'predict': {
        'all': predict_avg_terminated_time,
        'comm': predict_avg_terminated_time_comm,
        'comp': predict_avg_terminated_time_comp,
        'sensing': predict_avg_terminated_time_sensing
    }
}

# 颜色定义（与您原始代码一致）
color = {
    'random': (53/255, 183/255, 119/255),   # 绿色
    'greedy': (48/255, 104/255, 141/255),   # 蓝色
    'heuristic': (248/255, 230/255, 32/255), # 黄色
    'predict': (68/255, 4/255, 90/255)      # 紫色
}

# 绘图设置
categories = ['all', 'comm', 'comp', 'sensing']
algorithms = ['random', 'greedy', 'heuristic', 'predict']
bar_width = 0.2
x = np.arange(len(categories))


# 完成率图
plt.figure(clear=True,figsize=(8, 6)) #figsize=(12, 6)
# 绘制柱状图
for i, algo in enumerate(algorithms):
    values = [completion_data[algo][cat] for cat in categories]
    plt.bar(x + i*bar_width, values, width=bar_width,
            color=color[algo], label=algo)
# 添加标签和标题
plt.xlabel('Task Categories')
plt.ylabel('Completion Ratio')
# plt.title('Task Completion Ratio by Algorithm and Category')
plt.xticks(x + bar_width*1.5, categories)
plt.yticks()
plt.ylim(0, 1)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1), fancybox=True, shadow=False, ncol=4)
plt.grid(True, axis='y', linestyle='--', alpha=0.7)
# 显示数值标签
for i, algo in enumerate(algorithms):
    for j, cat in enumerate(categories):
        height = completion_data[algo][cat]
        plt.text(x[j] + i*bar_width, height + 0.01,
                 f'{height:.3f}',
                 ha='center', va='bottom',fontsize=6)
plt.tight_layout()
plt.savefig('completion_load4.png')
plt.show()

# 时延图
plt.figure(clear=True,figsize=(8, 6)) #figsize=(12, 6)
# 绘制柱状图
for i, algo in enumerate(algorithms):
    values = [time_data[algo][cat] for cat in categories]
    plt.bar(x + i*bar_width, values, width=bar_width,
            color=color[algo], label=algo)
# 添加标签和标题
plt.xlabel('Task Categories')
plt.ylabel('Time (s)')  # 修改ylabel
plt.xticks(x + bar_width*1.5, categories)
plt.yticks()
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),
           fancybox=True, shadow=False, ncol=4)
plt.grid(True, axis='y', linestyle='--', alpha=0.7)

# 显示数值标签（使用time_data而非completion_data）
for i, algo in enumerate(algorithms):
    for j, cat in enumerate(categories):
        time_value = time_data[algo][cat]
        plt.text(x[j] + i*bar_width, time_value + 0.01,  # 动态偏移量
                 f'{time_value:.2f}s',  # 添加单位
                 ha='center', va='bottom',fontsize=6)
plt.tight_layout()
plt.savefig('time_load4.png')
plt.show()


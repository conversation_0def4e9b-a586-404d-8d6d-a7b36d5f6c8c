import json

PTMAPPO_file_path = "../../train_mid_high/TransMAPPO_train5/evaluation/final/evaluation.json"
# PTMAPPO_file_path = "../../train3/heuristic_local/evaluation/final/evaluation.json"
max_episodes=100

completion_fluctuation_list_all=[]
completion_fluctuation_list_comm=[]
completion_fluctuation_list_comp=[]
completion_fluctuation_list_sensing=[]
time_fluctuation_list=[]
time_fluctuation_list_comm=[]
time_fluctuation_list_comp=[]
time_fluctuation_list_sensing=[]
with open(PTMAPPO_file_path, 'r') as file:
    data = json.load(file)
    completion_ratio = [entry["episode_completion_ratio"] for entry in data][:max_episodes]
    completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data][:max_episodes]
    completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data][:max_episodes]
    completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data][:max_episodes]
    avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data][:max_episodes]
    avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data][:max_episodes]
    avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data][:max_episodes]
    avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data][:max_episodes]

# 计算完成率波动（后减前）
for i in range(1, len(completion_ratio)):
    fluctuation = completion_ratio[i] - completion_ratio[i - 1]
    completion_fluctuation_list_all.append(fluctuation)
    fluctuation = completion_ratio_comm[i] - completion_ratio_comm[i - 1]
    completion_fluctuation_list_comm.append(fluctuation)
    fluctuation = completion_ratio_comp[i] - completion_ratio_comp[i - 1]
    completion_fluctuation_list_comp.append(fluctuation)
    fluctuation = completion_ratio_sensing[i] - completion_ratio_sensing[i - 1]
    completion_fluctuation_list_sensing.append(fluctuation)

# 计算时间波动（(后-前)/前）
for i in range(1, len(avg_terminated_time)):
    fluctuation = (avg_terminated_time[i] - avg_terminated_time[i - 1]) / avg_terminated_time[i - 1]
    time_fluctuation_list.append(fluctuation)
    fluctuation = (avg_terminated_time_comm[i] - avg_terminated_time_comm[i - 1]) / avg_terminated_time_comm[i - 1]
    time_fluctuation_list_comm.append(fluctuation)
    fluctuation = (avg_terminated_time_comp[i] - avg_terminated_time_comp[i - 1]) / avg_terminated_time_comp[i - 1]
    time_fluctuation_list_comp.append(fluctuation)
    fluctuation = (avg_terminated_time_sensing[i] - avg_terminated_time_sensing[i - 1]) / avg_terminated_time_sensing[i - 1]
    time_fluctuation_list_sensing.append(fluctuation)

print('全局完成率最大差异',max(completion_ratio)-min(completion_ratio))
print('全局完成率最大差异comm',max(completion_ratio_comm)-min(completion_ratio_comm))
print('全局完成率最大差异comp',max(completion_ratio_comp)-min(completion_ratio_comp))
print('全局完成率最大差异sensing',max(completion_ratio_sensing)-min(completion_ratio_sensing))
print()
print('单步完成率最大差异',max(completion_fluctuation_list_all))
print('单步完成率最大差异comm',max(completion_fluctuation_list_comm))
print('单步完成率最大差异comp',max(completion_fluctuation_list_comp))
print('单步完成率最大差异sensing',max(completion_fluctuation_list_sensing))

'''
PTMAPPO_low
全局完成率最大差异 0.07006859804837617
全局完成率最大差异comm 0.1206182912032624
全局完成率最大差异comp 0.07344905107076338
全局完成率最大差异sensing 0.11379314210131031

单步完成率最大差异 0.058663760436781676
单步完成率最大差异comm 0.09088117735953838
单步完成率最大差异comp 0.04575163398692805
单步完成率最大差异sensing 0.06386047229964764


PTMAPPO_mid
全局完成率最大差异 0.06908210401836734
全局完成率最大差异comm 0.10099706658748986
全局完成率最大差异comp 0.06037654596256792
全局完成率最大差异sensing 0.186483539046033

单步完成率最大差异 0.046815713703898476
单步完成率最大差异comm 0.08932340481501444
单步完成率最大差异comp 0.04641650582435197
单步完成率最大差异sensing 0.16675809786122464

PTMAPPO_mid_high
全局完成率最大差异 0.06203550785350176
全局完成率最大差异comm 0.10144985303500142
全局完成率最大差异comp 0.07774592766761568
全局完成率最大差异sensing 0.12031527714346307

单步完成率最大差异 0.05357419017722964
单步完成率最大差异comm 0.10144985303500142
单步完成率最大差异comp 0.05228598899277237
单步完成率最大差异sensing 0.0541243978834447

'''
# completion ratio
import json

from matplotlib import pyplot as plt

def ema_smoothing(data, alpha=0.8):
    smoothed = [data[0]]  # 初始值为第一个数据点
    for i in range(1, len(data)):
        smoothed.append(  alpha * smoothed[i-1] + (1-alpha) * data[i])
    return smoothed

color = {
    'yellow': (248 / 255, 230 / 255, 32 / 255),
    'green': (53 / 255, 183 / 255, 119 / 255),
    'blue': (48 / 255, 104 / 255, 141 / 255),
    'purple': (68 / 255, 4 / 255, 90 / 255)
}

# trans_file_path="../../train/TransMAPPO_train5_sens/evaluation/final/evaluation.json"
trans_file_path="../../train3/TransMAPPO_train5/evaluation/final/evaluation.json"
ntrans_file_path="../../train3/MAPPO_train/evaluation/final/evaluation.json"
save_dir="./"
max_episode=50
episode_nums=[i for i in range(max_episode)]
with open(trans_file_path, 'r') as file:
    data = json.load(file)
    trans_completion_ratio = [entry["episode_completion_ratio"] for entry in data]
    trans_completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
    trans_completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
    trans_completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
    trans_avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data]
    trans_avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
    trans_avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
    trans_avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]
    trans_completion_ratio = ema_smoothing(trans_completion_ratio)[:max_episode]
    trans_completion_ratio_comm = ema_smoothing(trans_completion_ratio_comm)[:max_episode]
    trans_completion_ratio_comp =ema_smoothing(trans_completion_ratio_comp)[:max_episode]
    trans_completion_ratio_sensing = ema_smoothing(trans_completion_ratio_sensing)[:max_episode]
    trans_avg_terminated_time = ema_smoothing(trans_avg_terminated_time)[:max_episode]
    trans_avg_terminated_time_comm = ema_smoothing(trans_avg_terminated_time_comm)[:max_episode]
    trans_avg_terminated_time_comp = ema_smoothing(trans_avg_terminated_time_comp)[:max_episode]
    trans_avg_terminated_time_sensing = ema_smoothing(trans_avg_terminated_time_sensing)[:max_episode]

with open(ntrans_file_path, 'r') as file:
    data = json.load(file)
    ntrans_completion_ratio = [entry["episode_completion_ratio"] for entry in data]
    ntrans_completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data]
    ntrans_completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data]
    ntrans_completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data]
    ntrans_avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data]
    ntrans_avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data]
    ntrans_avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data]
    ntrans_avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data]
    ntrans_completion_ratio = ema_smoothing(ntrans_completion_ratio)[:max_episode]
    ntrans_completion_ratio_comm = ema_smoothing(ntrans_completion_ratio_comm)[:max_episode]
    ntrans_completion_ratio_comp =ema_smoothing(ntrans_completion_ratio_comp)[:max_episode]
    ntrans_completion_ratio_sensing = ema_smoothing(ntrans_completion_ratio_sensing)[:max_episode]
    ntrans_avg_terminated_time = ema_smoothing(ntrans_avg_terminated_time)[:max_episode]
    ntrans_avg_terminated_time_comm = ema_smoothing(ntrans_avg_terminated_time_comm)[:max_episode]
    ntrans_avg_terminated_time_comp = ema_smoothing(ntrans_avg_terminated_time_comp)[:max_episode]
    ntrans_avg_terminated_time_sensing = ema_smoothing(ntrans_avg_terminated_time_sensing)[:max_episode]


plt.figure(clear=True)
plt.plot(episode_nums, ntrans_completion_ratio, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_completion_ratio, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('CompletionRatio')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'CompletionRatio_all.png')
plt.close()

plt.figure(clear=True)
plt.plot(episode_nums, ntrans_completion_ratio_comm, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_completion_ratio_comm, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('CompletionRatio')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'CompletionRatio_comm.png')
plt.close()

plt.figure(clear=True)
plt.plot(episode_nums, ntrans_completion_ratio_comp, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_completion_ratio_comp, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('CompletionRatio')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'CompletionRatio_comp.png')
plt.close()

plt.figure(clear=True)
plt.plot(episode_nums, ntrans_completion_ratio_sensing, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_completion_ratio_sensing, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('CompletionRatio')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'CompletionRatio_sens.png')
plt.close()




plt.figure(clear=True)
plt.plot(episode_nums, ntrans_avg_terminated_time, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_avg_terminated_time, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('Relay(s)')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'Relay_all.png')
plt.close()

plt.figure(clear=True)
plt.plot(episode_nums, ntrans_avg_terminated_time_comm, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_avg_terminated_time_comm, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('Relay(s)')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'Relay_comm.png')
plt.close()

plt.figure(clear=True)
plt.plot(episode_nums, ntrans_avg_terminated_time_comp, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_avg_terminated_time_comp, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('Relay(s)')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'Relay_comp.png')
plt.close()

plt.figure(clear=True)
plt.plot(episode_nums, ntrans_avg_terminated_time_sensing, label='No Trans', color=color['green'])
plt.plot(episode_nums, trans_avg_terminated_time_sensing, label='Trans', color=color['blue'])
plt.xlabel('Episode')
plt.ylabel('Relay(s)')
plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=2)
plt.savefig(save_dir + 'Relay_sens.png')
plt.close()


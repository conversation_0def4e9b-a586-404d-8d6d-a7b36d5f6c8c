import json

# PTMAPPO_file_path = "../../../train_low/D3QN/evaluation/final/evaluation.json"
PTMAPPO_file_path = "../../../train_mid_high/PTMAPPO_Reptile2/evaluation/final/evaluation.json"
max_episodes=100

completion_fluctuation_list_all=[]
completion_fluctuation_list_comm=[]
completion_fluctuation_list_comp=[]
completion_fluctuation_list_sensing=[]
time_fluctuation_list=[]
time_fluctuation_list_comm=[]
time_fluctuation_list_comp=[]
time_fluctuation_list_sensing=[]
with open(PTMAPPO_file_path, 'r') as file:
    data = json.load(file)
    completion_ratio = [entry["episode_completion_ratio"] for entry in data][:max_episodes]
    completion_ratio_comm = [entry["episode_completion_ratio_comm"] for entry in data][:max_episodes]
    completion_ratio_comp = [entry["episode_completion_ratio_comp"] for entry in data][:max_episodes]
    completion_ratio_sensing = [entry["episode_completion_ratio_sensing"] for entry in data][:max_episodes]
    avg_terminated_time = [entry["episode_avg_terminated_time"] for entry in data][:max_episodes]
    avg_terminated_time_comm = [entry["episode_avg_terminated_time_comm"] for entry in data][:max_episodes]
    avg_terminated_time_comp = [entry["episode_avg_terminated_time_comp"] for entry in data][:max_episodes]
    avg_terminated_time_sensing = [entry["episode_avg_terminated_time_sensing"] for entry in data][:max_episodes]

# 计算完成率波动（后减前）
for i in range(1, len(completion_ratio)):
    fluctuation = completion_ratio[i] - completion_ratio[i - 1]
    completion_fluctuation_list_all.append(fluctuation)
    fluctuation = completion_ratio_comm[i] - completion_ratio_comm[i - 1]
    completion_fluctuation_list_comm.append(fluctuation)
    fluctuation = completion_ratio_comp[i] - completion_ratio_comp[i - 1]
    completion_fluctuation_list_comp.append(fluctuation)
    fluctuation = completion_ratio_sensing[i] - completion_ratio_sensing[i - 1]
    completion_fluctuation_list_sensing.append(fluctuation)

# 计算时间波动（(后-前)/前）
for i in range(1, len(avg_terminated_time)):
    fluctuation = (avg_terminated_time[i] - avg_terminated_time[i - 1]) / avg_terminated_time[i - 1]
    time_fluctuation_list.append(fluctuation)
    fluctuation = (avg_terminated_time_comm[i] - avg_terminated_time_comm[i - 1]) / avg_terminated_time_comm[i - 1]
    time_fluctuation_list_comm.append(fluctuation)
    fluctuation = (avg_terminated_time_comp[i] - avg_terminated_time_comp[i - 1]) / avg_terminated_time_comp[i - 1]
    time_fluctuation_list_comp.append(fluctuation)
    fluctuation = (avg_terminated_time_sensing[i] - avg_terminated_time_sensing[i - 1]) / avg_terminated_time_sensing[i - 1]
    time_fluctuation_list_sensing.append(fluctuation)

print('全局完成率最大差异',max(completion_ratio)-min(completion_ratio))
print('全局完成率最大差异comm',max(completion_ratio_comm)-min(completion_ratio_comm))
print('全局完成率最大差异comp',max(completion_ratio_comp)-min(completion_ratio_comp))
print('全局完成率最大差异sensing',max(completion_ratio_sensing)-min(completion_ratio_sensing))
print()
print('单步完成率最大差异',max(completion_fluctuation_list_all))
print('单步完成率最大差异comm',max(completion_fluctuation_list_comm))
print('单步完成率最大差异comp',max(completion_fluctuation_list_comp))
print('单步完成率最大差异sensing',max(completion_fluctuation_list_sensing))

'''
PTMAPPO_low
全局完成率最大差异 0.0498751613354429
全局完成率最大差异comm 0.09650387401379679
全局完成率最大差异comp 0.07541319330489005
全局完成率最大差异sensing 0.08732473556594156

单步完成率最大差异 0.03555798687089706
单步完成率最大差异comm 0.06810180483546235
单步完成率最大差异comp 0.04704955694125368
单步完成率最大差异sensing 0.0755074765971665


PTMAPPO_mid
全局完成率最大差异 0.07808411609838717
全局完成率最大差异comm 0.13516828635786793
全局完成率最大差异comp 0.07733149981165566
全局完成率最大差异sensing 0.21253181412602495

单步完成率最大差异 0.05878321983056556
单步完成率最大差异comm 0.08913046279803505
单步完成率最大差异comp 0.05660704622349677
单步完成率最大差异sensing 0.12338023842991641

PTMAPPO_mid_high
全局完成率最大差异 0.05607260357220778
全局完成率最大差异comm 0.08406861191747272
全局完成率最大差异comp 0.05565724380025794
全局完成率最大差异sensing 0.16158365809101977

单步完成率最大差异 0.037576324959139984
单步完成率最大差异comm 0.05638867216566612
单步完成率最大差异comp 0.03628103409589983
单步完成率最大差异sensing 0.08094089531074716

'''

'''
D3QN_low
全局完成率最大差异 0.054066427946619755
全局完成率最大差异comm 0.1159251756488987
全局完成率最大差异comp 0.09772858133401419
全局完成率最大差异sensing 0.09350561448113826

单步完成率最大差异 0.049668902187952946
单步完成率最大差异comm 0.081454326677044
单步完成率最大差异comp 0.07766275473205952
单步完成率最大差异sensing 0.06424992631889181

D3QN_mid
全局完成率最大差异 0.08414730976268814
全局完成率最大差异comm 0.12663321545174577
全局完成率最大差异comp 0.08990181660777619
全局完成率最大差异sensing 0.19338849130734326

单步完成率最大差异 0.03140312363746356
单步完成率最大差异comm 0.08339801425190363
单步完成率最大差异comp 0.05238023336987285
单步完成率最大差异sensing 0.11347507514632094

D3QN_mid_high
全局完成率最大差异 0.0640318475754853
全局完成率最大差异comm 0.13243211822340367
全局完成率最大差异comp 0.07157069932657462
全局完成率最大差异sensing 0.16158493880542324

单步完成率最大差异 0.051327247451971925
单步完成率最大差异comm 0.10078458351442782
单步完成率最大差异comp 0.07046756232649626
单步完成率最大差异sensing 0.08992732328411579
'''
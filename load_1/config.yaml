config_simulation:
  bbox:
    longitude_range: [-180.0, 180.0] # 经度范围
    latitude_range: [-90.0,  90.0] # 纬度范围
  epoch: "2025-01-01 00:00:00"
  max_simulation_time: 200
  start_simulation_time: 0
  flight_interval: 1.0
  simulation_interval: 0.1
  schedule_interval: 0.1
  state_sync_interval: 1.0
  time_accuracy_digit: 3 # 0.001s

config_topology:
  mode: "stored" # "real" or "stored"
  export: False # True or False
  flight_path: "../../files/topology_files/500s/flight.pkl"
  view_path: "../../files/topology_files/500s/view.pkl"

config_grid:
  grid_path: "../../files/grid_files/grid.pkl"

config_node:
  # 是否导出时隙节点记录
  record: False # True or False
  export: False # True or False
  export_path: "../../files/other_files/60s/node_position.csv"
  # supported constellation:['starlink_v1','starlink_v2','gw','GEO_sensing','SSO_sensing']
  constellation_paths: [
    "../../spacetasksim/node_config/constellation/starlink_v1.yaml",
    "../../spacetasksim/node_config/constellation/geo_sensing.yaml",
    "../../spacetasksim/node_config/constellation/sso_sensing.yaml"
  ]
  # supported station:['China']
  station_paths: ["../../spacetasksim/node_config/station/China.yaml"]

config_task:
  # 是否导出时隙新任务记录
  record: False # True or False
  export: False # True or False
  export_path: "../../files/other_files/60s/task.csv"

  MTPD_threshold: 0.3 # The threshold of Maximum Tolerable Period Disruption for task exe
  communication:
    task_size: [10000,50000] # MB
    task_requirement_unit: 0.0005 # 0.0005 0.0010 0.0015 0.0020 0.0025 0.0030 0.0035 0.0040 0.0080 # MB s/person
    task_TTL_range: [ 10,20 ] # s
  computation:
    task_size: [1000,5000] # MB
    task_requirement_unit: 0.00006 # 0.00006 0.00012 0.00018 0.00024 0.00030 0.00036 0.00042 0.00048 0.00096 # MB s/person
    task_TTL_range: [10,20] # s
  sensing:
    sensing_time: [11,22] # s
    forest_age_seg: [50,100,150,200] # year
#    sensing_interval: [15,10,10,10,5] # minutes/time
#    sensing_interval: [30,15,15,15,10] # minutes/time
#    sensing_interval: [35,20,20,20,15] # minutes/time
#    sensing_interval: [45,30,30,30,20] # minutes/time
#    sensing_interval: [55,40,40,40,25] # minutes/time
#    sensing_interval: [60,45,45,45,30] # minutes/time
#    sensing_interval: [90,60,60,60,45] # minutes/time
#    sensing_interval: [120,60,60,60,45] # minutes/time
    sensing_interval: [240,120,120,120,90] # minutes/time


#    sensing_interval: [2880,1440,720,360,180] # minutes/time
#    sensing_interval: [1440,720,360,180,60] # minutes/time
#    sensing_interval: [720,360,180,120,60] # minutes/time
#    sensing_interval: [360,180,120,60,30] # minutes/time # 之前是这个
#    sensing_interval: [ 240,180,90,45,30 ] # minutes/time
#    sensing_interval: [ 180,120,60,30,15 ] # minutes/time
#    sensing_interval: [120,60,30,15,5] # minutes/time
#    sensing_interval: [60,30,15,5,1] # minutes/time
    task_TTL_range: [10,20] # 3-6s
    sensing_accuracy_range: [0.2,0.8]

config_resource:
  # 是否导出时隙资源记录
  record: False # True or False
  export: False # True or False
  export_path: "../../files/other_files/60s/resource.csv"
  # storage
  config_storage:
    capacity: 3.0e+7 # MB 30TB
  # computation
  config_computation:
    cycle_per_byte: 17 # B
    user:
      cpu: 1  # CPU capacity of mobile users
      frequency: [0.5,1.0] # Gc/s
    station:
      cpu: 2  # CPU capacity of ground stations
      frequency: [ 1.0,2.0 ] # Gc/s
    satellite:
      cpu: 4  # CPU capacity of LEO satellites
      frequency: [2.0,8.0] # Gc/s
  # communication
  config_communication:
    G2S:
      # Each ground station use fixed frequency
      beam_receive: 8 # Receive beam num on satellite
      beam_transmit: 1  # Transmit beam num on station
      bandwidth: [0.25,0.50] # GHz
      frequency_start: 30.0 # GHz
      power_dbw: [10,10] # 10W
    S2G:
      # Each ground station use fixed frequency
      beam_receive: 4 # Receive beam num on station
      beam_transmit: 16  # Transmit beam num on satellite
      bandwidth: [0.50,1.00] # GHz
      frequency_start: 40.0 # GHz
      power_dbw: [16.99,16.99] # 50W
    S2S:
      # ISL,Inter-SatelliteLink
      beam_receive: 10 # Receive beam num on satellite
      beam_transmit: 10  # Transmit beam num on satellite
      bandwidth: [1,4] # GHz
      frequency_start: 193500 # GHz 193.5THz 1550nm
      power_dbw: [-3.01,6.99] # 单位：dbw  0.5w=-3.01dbw 1w=0dbw 1.5w=1.76dbw 5W=6.99dbw
    #  U2S:
    #    bandwidth: 125 # MHz
    #    frequency_range: [14.0，14.5] # GHz
    #    power_dbw: -3.01 # 0.5W
    #  S2U:
    #    bandwidth: 250 # MHz
    #    frequency_range: [ 10.7,12.75 ] # GHz
    #    power_dbw: 16.99 # 50W
  # sensing
  config_sensing:
    camera: 6 # Camera num on each sensing node
    accuracy_range: [0.1,1.0] # Accuracy of camera
    data_increase_rate: 50 # MB/s accuracy=1.0
    elevation_mask_angle: 25 # Minimum elevation angle for sensing

config_view:
  clearance: 150 # Clearance for safe ISL transmitting, km
  elevation_mask_angle: 25 # Minimum elevation angle for satellite-station view



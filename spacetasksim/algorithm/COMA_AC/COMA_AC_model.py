import argparse
import json
import os
import pickle
import random

from .COMA_AC_network import Critic, Actor
import torch
from copy import deepcopy
from .COMA_AC_memory import Memory
from torch.optim import <PERSON>
from torch.optim import SGD
from torch.nn import functional as F
import torch.nn as nn
import numpy as np
import logging

def soft_update(target, source, tau):
    for target_param, source_param in zip(target.parameters(),source.parameters()):
        target_param.data.copy_((1 - tau) * target_param.data + tau * source_param.data)


def hard_update(target, source):
    for target_param, source_param in zip(target.parameters(),source.parameters()):
        target_param.data.copy_(source_param.data)


class COMA_AC:
    def __init__(self, dim_args, train_args):
        # 维度超参数
        self.n_agents = dim_args.n_agents
        self.dim_obs = dim_args.dim_obs
        self.dim_act = dim_args.dim_action


        # 训练超参数
        self.lr = train_args.learning_rate
        self.var = train_args.var
        self.var_min = train_args.var_end
        self.var_dec = train_args.var_dec
        self.gamma = train_args.gamma
        self.gae_lambda = train_args.gae_lambda
        self.epsilon = train_args.epsilon
        self.epoch = train_args.epoch
        self.device = train_args.device

        # 实例化策略网络*n
        self.actors = Actor(self.dim_obs,self.dim_act)
        self.old_actors = deepcopy(self.actors) # 旧行为策略
        # 实例化价值网络*n
        self.critics = Critic(self.n_agents, self.dim_obs, self.dim_act)
        self.old_critics = deepcopy(self.critics) # 旧评价网络
        # 策略训练网络优化器
        self.actor_optimizer = Adam(self.actors.parameters(), lr=self.lr)
        # 目标训练网络优化器
        self.critic_optimizer = Adam(self.critics.parameters(), lr=self.lr)

        # 经验池
        self.memory = [Memory() for i in range(self.n_agents)] # 每个agent构建独立经验池


        self.actors.to(self.device)
        self.old_actors.to(self.device)
        self.critics.to(self.device)
        self.old_critics.to(self.device)

        # 记录迭代次数
        self.steps_done = 0
        # 记录最大完成率
        self.best_succ_ratio=0

    def remember(self,agent_id, state,mask, action, reward, next_state,agent_mask):
        self.memory[agent_id].add(state,mask, action, reward, next_state,agent_mask)

    def take_action(self, obs_state, action_mask,agent_mask):
        """
        obs_state: [ state_dim]  # 所有智能体的状态
        action_mask: [action_dim]  # 动作掩码，True表示合法动作
        agent_mask: [1]  # 智能体是否需要动作
        """
        action = -1  # 默认无动作

        if not agent_mask:  # 如果智能体不需要动作
            self.steps_done += 1
            return action

        obs_state=torch.tensor(obs_state, dtype=torch.float).unsqueeze(0).to(self.device)
        action_mask = torch.tensor(action_mask, dtype=torch.bool).to(self.device)

        with torch.no_grad():
            # 1. 用 Actor 网络计算动作 logits：[action_dim]
            action_logits = self.old_actors(obs_state).flatten()

            # 2. 掩码非法动作 logits：将非法动作的 logits 设为 -1e10 避免 NaN（比 -inf 更安全）
            masked_logits = action_logits.masked_fill(~action_mask, -1e10)

            # 3. 构造合法动作概率分布
            action_probs = F.softmax(masked_logits, dim=-1)
            action_dist = torch.distributions.Categorical(probs=action_probs)

            # 4. ε-greedy 策略选择动作
            if random.random() > self.var:
                # 贪婪选择合法动作中最大概率的
                action = torch.argmax(action_probs).item()
            else:
                # 探索：从合法动作中按概率采样
                action = action_dist.sample().item()

        self.steps_done += 1
        return action


    def decrement_var(self):
        if self.var > self.var_min:
            self.var = self.var* self.var_dec
        else:
            self.var = self.var_min

    def update(self):
        # 初始化损失记录和缓冲区
        c_loss = [[] for _ in range(self.n_agents)]
        a_loss = [[] for _ in range(self.n_agents)]

        # 1. 从所有智能体的内存中收集数据（对应流程图第16行）
        for agent_idx in range(self.n_agents):
            memory = self.memory[agent_idx].get_all()
            if memory is None:
                continue

            # 2. 合并数据并转换为Tensor（对应流程图第18行）
            states, action_masks, actions, rewards, next_states, agent_masks = memory

            # 只处理需要动作的时隙
            valid_idx = [i for i, m in enumerate(agent_masks) if m]
            if len(valid_idx) < 5:  # 如果有效数据很少则放弃训练
                continue

            batch_size = len(valid_idx)
            states = torch.tensor(states[valid_idx], dtype=torch.float).to(self.device)  # [B, n_agents, state_dim]
            actions = torch.tensor(actions[valid_idx], dtype=torch.long).view(batch_size,-1).to(self.device)  # [B, n_agents]
            rewards = torch.tensor(rewards[valid_idx], dtype=torch.float).view(batch_size,-1).to(self.device)  # [B, 1]
            next_states = torch.tensor(next_states[valid_idx], dtype=torch.float).to(self.device)  # [B, n_agents, state_dim]
            action_masks = torch.tensor(action_masks[valid_idx], dtype=torch.bool).view(batch_size,-1).to(self.device)  # [B, act_dim]

            # 3. 计算TD(λ)目标（对应流程图第19-22行）
            with torch.no_grad():
                joint_states = states.view(states.shape[0], -1)  # [B, n_agents*state_dim]
                joint_next_states = next_states.view(next_states.shape[0], -1)  # [B, n_agents*state_dim]
                joint_actions = actions.view(actions.shape[0], -1)  # [B, n_agents]

                # 使用目标网络计算TD目标（公式17）
                target_q = self.old_critics(joint_next_states, joint_actions)  # [B, 1]
                td_target = rewards + self.gamma * target_q  # [B, 1]

            # 4. Critic网络更新（对应流程图第23-27行）
            for _ in range(self.epoch):
                # 计算当前Q值并更新Critic
                current_q = self.critics(joint_states, joint_actions)  # [B, 1]
                critic_loss = F.mse_loss(current_q, td_target)

                self.critic_optimizer.zero_grad()
                critic_loss.backward()
                self.critic_optimizer.step()

            # 5. Actor网络更新（对应流程图第28-32行）
            agent_obs = states[:, agent_idx, :]  # [B, state_dim]
            agent_actions = actions[:, agent_idx]  # [B]
            agent_action_masks = action_masks  # [B, act_dim]

            # 计算优势函数（公式16，COMA的反事实基线）
            with torch.no_grad():
                current_q = self.critics(joint_states, joint_actions)  # [B, 1]

                # 获取当前策略的概率分布（带掩码）
                logits = self.actors(agent_obs)  # [B, act_dim]
                logits = logits.masked_fill(~agent_action_masks, -1e10)
                action_probs = F.softmax(logits, dim=-1)  # [B, act_dim]

                # 计算基线值
                baseline = torch.zeros_like(current_q)
                for a in range(self.dim_act):
                    if not agent_action_masks[:, a].any():
                        continue
                    modified_actions = joint_actions.clone()
                    modified_actions[:, agent_idx] = a
                    q_a = self.critics(joint_states, modified_actions)  # [B, 1]
                    baseline += action_probs[:, a].unsqueeze(1) * q_a

                advantages = current_q - baseline  # [B, 1]

            # 策略梯度更新（对应流程图第30行）
            for _ in range(self.epoch):
                new_logits = self.actors(agent_obs)  # [B, act_dim]
                new_logits = new_logits.masked_fill(~agent_action_masks, -1e10)
                new_probs = F.softmax(new_logits, dim=-1)
                log_probs = torch.log(new_probs.gather(1, agent_actions.unsqueeze(1)) + 1e-10)  # [B, 1]

                actor_loss = -(log_probs * advantages).mean()
                self.actor_optimizer.zero_grad()
                actor_loss.backward()
                self.actor_optimizer.step()

                a_loss[agent_idx].append(actor_loss.item())

        # 定期更新目标网络（对应流程图第26行）
        hard_update(self.old_critics, self.critics)
        hard_update(self.old_actors, self.actors)

        # 6. 清空内存（对应流程图第4行）
        for i in range(self.n_agents):
            self.memory[i].clear()

        self.decrement_var()
        return a_loss, c_loss

    def save_models(self, episode, base_dir,final,succ_ratio):
        if final is True:
            file_dir = f"{base_dir}/final"
        else:
            file_dir=f"{base_dir}/episode_{episode}"
        model_type = "final" if final is True else "checkpoint"

        if not os.path.exists(file_dir):
            os.makedirs(file_dir)
        logging.basicConfig(
            level=logging.INFO,  # 日志级别
            format='%(asctime)s [%(levelname)s] %(message)s',  # 日志格式
            datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
            filename=f'{file_dir}/model.log',  # 日志文件名
            filemode='a'  # 追加模式写入日志文件
        )

        self.critics.save_model(file_dir + f'/MAPPO_critics.pth')
        print(f'Saving {model_type} episode_{episode} MAPPO_critics network successfully!')
        logging.info(f'Saving {model_type} episode_{episode} MAPPO_critics network successfully!')
        self.actors.save_model(file_dir + f'/MAPPO_actors.pth')
        print(f'Saving {model_type} episode_{episode} MAPPO_actors network successfully!')
        logging.info(f'Saving {model_type} episode_{episode} MAPPO_actors network successfully!')
        self.old_critics.save_model(file_dir + f'/MAPPO_old_critics.pth')
        print(f'Saving {model_type} episode_{episode} MAPPO_old_critics network successfully!')
        logging.info(f'Saving {model_type} episode_{episode} MAPPO_old_critics network successfully!')
        self.old_actors.save_model(file_dir + f'/MAPPO_old_actors.pth')
        print(f'Saving {model_type} episode_{episode} MAPPO_old_actors network successfully!')
        logging.info(f'Saving {model_type} episode_{episode} MAPPO_old_actors network successfully!')

        torch.save(self.actor_optimizer.state_dict(),file_dir + f'/actor_optimizer.pth')
        torch.save(self.critic_optimizer.state_dict(),file_dir + f'/critic_optimizer.pth')

        if succ_ratio > self.best_succ_ratio:
            self.best_succ_ratio = succ_ratio
            best_dir = f"{base_dir}/best"
            if not os.path.exists(best_dir):
                os.makedirs(best_dir)
            self.critics.save_model(best_dir + f'/MAPPO_critics.pth')
            self.actors.save_model(best_dir + f'/MAPPO_actors.pth')
            data = {
                'episode': episode,
                'best_succ_ratio': self.best_succ_ratio,
            }
            with open(best_dir + f'/params.json', 'w') as f:
                json.dump(data, f, indent=4)  # indent=4 美化 JSON 格式

        params = {
            'var': self.var,
            'steps_done': self.steps_done,
        }
        print(f'params: {params}')
        with open(file_dir + f'/params.json', 'w') as f:
            json.dump(params, f, indent=4)  # indent=4 美化 JSON 格式
        print(f'Saving {model_type} episode_{episode} params successfully!')
        logging.info(f'Saving {model_type} episode_{episode} params successfully!')


    def load_models(self, episode, base_dir,final):
        if final is True:
            file_dir = f"{base_dir}/final"
        else:
            file_dir=f"{base_dir}/episode_{episode}"

        model_type = "final" if final is True else "checkpoint"

        logging.basicConfig(
            level=logging.INFO,  # 日志级别
            format='%(asctime)s [%(levelname)s] %(message)s',  # 日志格式
            datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
            filename=f'{file_dir}/model.log',  # 日志文件名
            filemode='a'  # 追加模式写入日志文件
        )


        self.critics.load_model(file_dir + f'/MAPPO_critics.pth')
        self.critics.to(self.device)
        print(f'Loading {model_type} episode_{episode} MAPPO_critics network successfully!')
        logging.info(f'Loading {model_type} episode_{episode} MAPPO_critics network successfully!')
        self.actors.load_model(file_dir + f'/MAPPO_actors.pth')
        self.actors.to(self.device)
        print(f'Loading {model_type} episode_{episode} MAPPO_actors network successfully!')
        logging.info(f'Loading {model_type} episode_{episode} MAPPO_actors network successfully!')
        self.old_critics.load_model(file_dir + f'/MAPPO_old_critics.pth')
        self.old_critics.to(self.device)
        print(f'Loading {model_type} episode_{episode} MAPPO_old_critics network successfully!')
        logging.info(f'Loading {model_type} episode_{episode} MAPPO_old_critics network successfully!')
        self.old_actors.load_model(file_dir + f'/MAPPO_old_actors.pth')
        self.old_actors.to(self.device)
        print(f'Loading {model_type} episode_{episode} MAPPO_old_actors network successfully!')
        logging.info(f'Loading {model_type} episode_{episode} MAPPO_old_actors network successfully!')

        actor_optimizer_state=torch.load(file_dir + f'/actor_optimizer.pth')
        critic_optimizer_state=torch.load(file_dir + f'/critic_optimizer.pth')
        self.actor_optimizer.load_state_dict(actor_optimizer_state)
        self.critic_optimizer.load_state_dict(critic_optimizer_state)

        best_dir = f"{base_dir}/best"
        with open(best_dir + f'/params.json', "r") as f:
            data = json.load(f)
            self.best_succ_ratio = data['best_succ_ratio']

        with open(file_dir+f'/params.json', 'r') as f:
            params=json.load(f)
            print(f'params: {params}')
            var=params['var']
            steps_done=params['steps_done']
            self.var=var
            self.steps_done=steps_done

        print(f'Loading {model_type} episode_{episode} params successfully!')
        logging.info(f'Loading {model_type} episode_{episode} params successfully!')

    def getTrainingArgs(self):
        actor_lr=self.actor_optimizer.state_dict()['param_groups'][0]['lr']
        critic_lr=self.critic_optimizer.state_dict()['param_groups'][0]['lr']
        var=self.var
        args_dict={
            'actor_lr':actor_lr,
            'critic_lr':critic_lr,
            'var':var,
        }
        return args_dict
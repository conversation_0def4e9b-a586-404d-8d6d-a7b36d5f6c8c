import torch
import torch.nn as nn
import torch.nn.functional as F


class Critic(nn.Module):
    def __init__(self, n_agent, dim_observation, dim_action, dim_hidden=256):
        super(Critic, self).__init__()
        self.n_agent = n_agent
        self.dim_observation = dim_observation
        self.dim_action = dim_action
        obs_dim = self.dim_observation * self.n_agent
        act_dim = self.n_agent

        # Input is concatenation of all agents' observations and actions
        input_dim = obs_dim + act_dim

        # 5-layer fully connected network with ReLU activation
        self.fc1 = nn.Linear(input_dim, dim_hidden)
        self.fc2 = nn.Linear(dim_hidden, dim_hidden)
        self.fc3 = nn.Linear(dim_hidden, dim_hidden)
        self.fc4 = nn.Linear(dim_hidden, dim_hidden)
        self.fc5 = nn.Linear(dim_hidden, 1)  # Output single Q-value

    def forward(self, obs, actions):
        # obs shape: [batch_size, n_agent * dim_observation]
        # actions shape: [batch_size, n_agent]
        x = torch.cat([obs, actions], dim=1)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = F.relu(self.fc3(x))
        x = F.relu(self.fc4(x))
        x = self.fc5(x)
        return x

    def save_model(self, file_dir):
        torch.save(self.state_dict(), file_dir)

    def load_model(self, file_dir):
        self.load_state_dict(torch.load(file_dir))


class Actor(nn.Module):
    def __init__(self, dim_observation, dim_action, dim_hidden=128, lstm_units=128):
        super(Actor, self).__init__()
        self.dim_observation = dim_observation
        self.dim_action = dim_action
        self.lstm_units = lstm_units

        # 1. BiLSTM层（匹配论文中的128单元）
        self.bilstm = nn.LSTM(
            input_size=dim_observation,
            hidden_size=lstm_units,
            num_layers=2,  # 双层LSTM
            bidirectional=True,  # 双向LSTM
            batch_first=True
        )

        # 2. Attention层
        self.attention = nn.Sequential(
            nn.Linear(2 * lstm_units, lstm_units),  # BiLSTM输出是2*hidden_size
            nn.Tanh(),
            nn.Linear(lstm_units, 1),  # 生成注意力权重
            nn.Softmax(dim=1)
        )

        # 3. 全连接层（匹配论文中的结构）
        self.fc1 = nn.Linear(2 * lstm_units, dim_hidden)
        self.fc2 = nn.Linear(dim_hidden, dim_action)

    def forward(self, obs):
        # obs shape: [batch_size, seq_len, dim_observation]
        batch_size = obs.size(0)
        obs = obs.unsqueeze(1)  # -> [batch_size, 1, dim_observation]

        # BiLSTM处理
        lstm_out, _ = self.bilstm(obs)  # [batch, seq_len, 2*lstm_units]

        # Attention计算
        attention_weights = self.attention(lstm_out)  # [batch, seq_len, 1]
        context = torch.sum(attention_weights * lstm_out, dim=1)  # [batch, 2*lstm_units]

        # 全连接层
        x = torch.tanh(self.fc1(context))
        x = F.softmax(self.fc2(x), dim=-1)  # 输出动作概率

        return x

    def save_model(self, file_path):
        torch.save(self.state_dict(), file_path)

    def load_model(self, file_path):
        self.load_state_dict(torch.load(file_path))
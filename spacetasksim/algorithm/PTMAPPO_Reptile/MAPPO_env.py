import os

from .MAPPO_model import MAPPO
import numpy as np
import torch


class MAPPO_Env:

    def __init__(self,dim_args, train_args):
        # 模型文件路径
        # current_dir = os.path.dirname(os.path.abspath(__file__))
        # self.model_base_dir = os.path.join(current_dir, "model")
        self.model_base_dir=train_args.model_base_dir

        # 实例化 MAPPO
        self.agent = MAPPO(dim_args, train_args)

    def takeAction(self,obs_state,action_mask,agent_mask=True,task_type=None):
        # 状态state时做动作选择
        actions = self.agent.take_action(obs_state,action_mask,agent_mask,task_type)
        return actions

    def addExperience(self,task_type, agent_id, state,mask, action, reward, next_state,agent_mask):
        # 添加经验池
        self.agent.remember(task_type,agent_id,state,mask, action, reward, next_state,agent_mask)

    def addGlobalState(self,task_type,state_id,global_state):
        self.agent.addGlobalState(task_type,state_id,global_state)

    def train(self):
        self.agent.update()

    def saveModel(self,episode,final=False,succ_ratio=0):
        self.agent.save_models(episode,self.model_base_dir,final,succ_ratio)

    def loadModel(self,episode,final=False,eval=False):
        self.agent.load_models(episode,self.model_base_dir,final,eval)


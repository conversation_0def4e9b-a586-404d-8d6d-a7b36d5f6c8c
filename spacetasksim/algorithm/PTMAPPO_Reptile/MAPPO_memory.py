import collections
import pickle
from collections import namedtuple
import random
from pprint import pprint

import numpy as np
from spacetasksim.enum.task_type_enum import TaskTypeEnum


class Memory:
    def __init__(self):
        # 创建一个队列，先进先出
        self.buffer =collections.deque()
        self.global_state={}

    def add(self, state,action_mask,action,reward,next_state,agent_mask):
        self.buffer.append((state,action_mask,action,reward,next_state,agent_mask))

    def addGlobalState(self,state_id,global_state):
        self.global_state[state_id]=global_state

    def sample(self, batch_size):
        transitions=random.sample(self.buffer, batch_size)
        # 分别取出这些数据，*获取list中的所有值
        state,action_mask, action, reward, next_state,agent_mask = zip(*transitions)
        # 将state变成数组，后面方便计算
        return state,np.array(action_mask), np.array(action), np.array(reward), next_state,np.array(agent_mask)

    def get_all(self):
        if len(self.buffer)==0:
            return None
        # 获取所有经验，返回整个 buffer 中的数据
        state,action_mask, action, reward, next_state,agent_mask = zip(*self.buffer)
        return state,np.array(action_mask), np.array(action), np.array(reward), next_state,np.array(agent_mask)

    def size(self):
        return len(self.buffer)

    def clear(self):
        self.buffer = collections.deque()

    # def save(self, file_path):
    #     with open(file_path, 'wb') as f:
    #         pickle.dump(self.buffer, f)
    #     print(f"Memory saved to {file_path}")
    #
    # def load(self, file_path):
    #     with open(file_path, 'rb') as f:
    #         self.buffer = pickle.load(f)
    #     print(f"Memory loaded from {file_path}")

class GlobalMemory:
    def __init__(self):
        self.buffer = {}

    def add(self, state_id, state):
        self.buffer[state_id] = state

    def get(self, state_id):
        return self.buffer[state_id]

    def clear(self):
        self.buffer = {}

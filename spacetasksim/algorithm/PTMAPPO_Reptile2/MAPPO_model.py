import argparse
import copy
import json
import os
import pickle
import random

from matplotlib import pyplot as plt
import seaborn as sns

from .MAPPO_network import Critic, Actor
import torch
from copy import deepcopy
from .MAPPO_memory import Memory
from torch.optim import Adam
from torch.optim import SGD
from torch.nn import functional as F
import torch.nn as nn
import numpy as np
import logging

from spacetasksim.enum.task_type_enum import TaskTypeEnum

EPS = 1e-10
LOGIT_MASK = -1e10

def soft_update(target, source, tau):
    for target_param, source_param in zip(target.parameters(),source.parameters()):
        target_param.data.copy_((1 - tau) * target_param.data + tau * source_param.data)


def hard_update(target, source):
    for target_param, source_param in zip(target.parameters(),source.parameters()):
        target_param.data.copy_(source_param.data)

def compute_advantage(gamma, gae_lambda, td_error,device):
    # 用通用优势估计（Generalized Advantage Estimator, GAE）构建时序优势
    td_error = td_error.detach().cpu().numpy()
    advantage_list = []
    advantage = 0.0
    for delta in td_error[::-1]:
        advantage = gamma * gae_lambda * advantage + delta
        advantage_list.append(advantage)
    advantage_list.reverse()
    advantage_array = np.array(advantage_list)
    return torch.tensor(advantage_array, dtype=torch.float,device=device)

def normalize_advantage(adv: torch.Tensor) -> torch.Tensor:
    """
    对批量优势进行归一化（减去均值，除以标准差）
    Args:
        adv: 原始优势值，形状 [batch_size]
    Returns:
        adv_normalized: 归一化后的优势值，已阻断梯度
    """
    adv_mean = adv.mean()                # 计算均值
    adv_std = adv.std()                  # 计算标准差
    adv_normalized = (adv - adv_mean) / (adv_std + 1e-8)  # 标准化（防止除零）
    return adv_normalized.detach()       # 分离计算图

class MAPPO:
    def __init__(self, dim_args, train_args):
        # 维度超参数
        self.n_agents = dim_args.n_agents
        self.dim_hiddens = dim_args.dim_hiddens
        self.dim_obs = dim_args.dim_obs
        self.dim_act = dim_args.dim_action


        # 训练超参数
        self.lr = train_args.learning_rate
        self.var = train_args.var
        self.var_min = train_args.var_end
        self.var_dec = train_args.var_dec
        self.gamma = train_args.gamma
        self.gae_lambda = train_args.gae_lambda
        self.epsilon = train_args.epsilon
        self.epoch = train_args.epoch
        self.tau_comm=train_args.tau_comm
        self.tau_comp = train_args.tau_comp
        self.tau_sens = train_args.tau_sens
        self.device = train_args.device

        # 实例化策略网络*n
        self.actors = Actor(self.dim_obs,self.dim_act, self.dim_hiddens)
        # 实例化价值网络*n
        self.critics = Critic(self.n_agents, self.dim_obs, self.dim_act, self.dim_hiddens)

        # 经验池
        self.memory = {task_type:[Memory() for i in range(self.n_agents)] for task_type in TaskTypeEnum}# 每种类型每个agent构建独立经验池


        self.actors.to(self.device)
        self.critics.to(self.device)

        # 记录迭代次数
        self.steps_done = 0
        # 记录最大完成率
        self.best_succ_ratio=0

    def remember(self,task_type,agent_idx, state,mask, action, reward, next_state,agent_mask):
        self.memory[task_type][agent_idx].add(state,mask, action, reward, next_state,agent_mask)

    def addGlobalState(self,state_id,global_state):
        self.memory.

    def take_action(self, obs_state, action_mask,agent_mask,task_type):
        """
        obs_state: [ state_dim]  # 所有智能体的状态
        action_mask: [action_dim]  # 动作掩码，True表示合法动作
        agent_mask: [1]  # 智能体是否需要动作
        """
        action = -1  # 默认无动作

        if not agent_mask:  # 如果智能体不需要动作
            self.steps_done += 1
            return action

        obs_state=torch.tensor(obs_state, dtype=torch.float).unsqueeze(0).to(self.device)
        action_mask = torch.tensor(action_mask, dtype=torch.bool).to(self.device)

        with torch.no_grad():
            # 1. 用 Actor 网络计算动作 logits：[action_dim]
            action_logits = self.actors(obs_state,task_type).flatten()

            # 2. 掩码非法动作 logits：将非法动作的 logits 设为 -1e10 避免 NaN（比 -inf 更安全）
            masked_logits = action_logits.masked_fill(~action_mask, LOGIT_MASK)

            # 3. 构造合法动作概率分布
            action_probs = F.softmax(masked_logits, dim=-1)
            action_dist = torch.distributions.Categorical(probs=action_probs)

            # 4. ε-greedy 策略选择动作
            if random.random() > self.var:
                # 贪婪选择合法动作中最大概率的
                action = torch.argmax(action_probs).item()
            else:
                # 探索：从合法动作中按概率采样
                action = action_dist.sample().item()

        self.steps_done += 1
        return action


    def decrement_var(self):
        if self.var > self.var_min:
            self.var = self.var* self.var_dec
        else:
            self.var = self.var_min

    def update(self):
        # 保存全局模型参数
        meta_actor = deepcopy(self.actors)
        meta_critic = deepcopy(self.critics)

        task_types = [TaskTypeEnum.SENSING,TaskTypeEnum.COMPUTATION,TaskTypeEnum.COMMUNICATION]
        # # random.shuffle(task_types)
        # task_types.sort(key=lambda x: x.value, reverse=True)
        for task_type in task_types:
            # 复制当前参数做局部更新
            task_actor = deepcopy(meta_actor)
            task_critic = deepcopy(meta_critic)
            actor_opt = Adam(task_actor.parameters(), lr=self.lr)
            critic_opt = Adam(task_critic.parameters(), lr=self.lr)

            # 在某类型任务上训练 n epoch（使用 task-specific memory）
            self._train_on_one_type(task_type, task_actor, task_critic, actor_opt, critic_opt)

            if task_type == TaskTypeEnum.COMMUNICATION:
                tau=self.tau_comm
            elif task_type == TaskTypeEnum.COMPUTATION:
                tau=self.tau_comp
            else:
                tau=self.tau_sens

            # Soft update 回 meta model
            soft_update(meta_actor, task_actor, tau)
            soft_update(meta_critic, task_critic, tau)

        # 更新全局模型
        self.actors.load_state_dict(meta_actor.state_dict())
        self.critics.load_state_dict(meta_critic.state_dict())

        # 清空经验池
        for task_type in TaskTypeEnum:
            for i in range(self.n_agents):
                self.memory[task_type][i].clear()

        self.decrement_var()

    def _train_on_one_type(self,task_type, actor_model, critic_model,actor_opt, critic_opt):

        all_rewards=[]
        rewards_by_agent = []
        reward_lengths = []

        old_actor_model = copy.deepcopy(actor_model)
        for agent_idx in range(self.n_agents):
            memory = self.memory[task_type][agent_idx].get_all()
            if memory is None:
                continue
            states, action_masks, actions, rewards, next_states, agent_masks = memory
            valid_idx = [i for i, m in enumerate(agent_masks) if m]
            if len(valid_idx) < 5:
                continue
            all_rewards.extend(rewards)
            rewards_by_agent.append(rewards)
            reward_lengths.append(len(rewards))

            # 转换为Tensor并只保留有效数据
            batch_size = len(valid_idx)
            # [batch_size, agent_dim, state_dim]
            states = torch.tensor(states[valid_idx], dtype=torch.float).to(self.device)
            # [batch_size, 1]
            actions = torch.tensor(actions[valid_idx], dtype=torch.long).view(batch_size,-1).to(self.device)
            # [batch_size, 1]
            rewards = torch.tensor(rewards[valid_idx], dtype=torch.float).view(batch_size,-1).to(self.device)
            # [batch_size, agent_dim, state_dim]
            next_states = torch.tensor(next_states[valid_idx], dtype=torch.float).to(self.device)
            # [batch_size, action_dim]
            action_masks = torch.tensor(action_masks[valid_idx], dtype=torch.bool).view(batch_size,-1).to(self.device)

            # 计算Advantage
            with torch.no_grad():
                values = critic_model(states.view(batch_size, -1), task_type)
                next_values = critic_model(next_states.view(batch_size, -1), task_type)
                td_targets = rewards + self.gamma * next_values
                td_error = td_targets - values
                advantages = compute_advantage(self.gamma, self.gae_lambda, td_error, self.device)
                advantages = normalize_advantage(advantages)
                old_action_probs = old_actor_model(states[:, agent_idx, :],task_type)
                old_action_probs = old_action_probs.masked_fill(~action_masks, LOGIT_MASK)
                old_action_probs = F.softmax(old_action_probs, dim=-1)
                old_log_probs = torch.log(old_action_probs.gather(1, actions) + EPS)

            # PPO 训练
            for i in range(self.epoch):
                new_action_probs = actor_model(states[:, agent_idx, :],task_type)
                new_action_probs = new_action_probs.masked_fill(~action_masks, LOGIT_MASK)
                new_action_probs = F.softmax(new_action_probs, dim=-1)
                new_log_probs = torch.log(new_action_probs.gather(1, actions) + EPS)
                ratio = torch.exp(new_log_probs - old_log_probs.detach())
                surr1 = ratio * advantages
                surr2 = torch.clamp(ratio, 1 - self.epsilon, 1 + self.epsilon) * advantages
                actor_loss = -torch.min(surr1, surr2).mean()
                actor_opt.zero_grad()
                actor_loss.backward()
                actor_opt.step()

                value_preds = critic_model(states.view(batch_size, -1),task_type)
                critic_loss = F.mse_loss(value_preds, td_targets.detach())
                critic_opt.zero_grad()
                critic_loss.backward()
                critic_opt.step()

        # plt.figure(clear=True)
        # # 方案1：直方图 + 密度曲线（推荐）
        # sns.histplot(all_rewards, kde=True, bins=30, color='skyblue')
        # plt.title(f'任务类型 [{task_type.name}] 的奖励分布', fontsize=14)
        # plt.xlabel('奖励值', fontsize=12)
        # plt.ylabel('出现频率', fontsize=12)
        # # 添加统计指标线
        # mean_val = np.mean(all_rewards)
        # median_val = np.median(all_rewards)
        # plt.axvline(mean_val, color='red', linestyle='--', label=f'均值: {mean_val:.2f}')
        # plt.axvline(median_val, color='green', linestyle=':', label=f'中位数: {median_val:.2f}')
        # plt.legend()
        # plt.grid(axis='y', alpha=0.3)
        # plt.show()
        #
        # plt.figure(clear=True)
        # sns.boxplot(data=rewards_by_agent)
        # plt.title(f'Reward Distribution by Agent for Task Type: {task_type.name}')
        # plt.xlabel('Agent Index')
        # plt.ylabel('Reward Value')
        # plt.show()
        #
        # plt.figure(clear=True)
        # # 方案1：直方图（适合展示数量分布）
        # plt.subplot(1, 2, 1)
        # sns.histplot(reward_lengths, bins=20, kde=False, color='orange')
        # plt.title('每个智能体的reward数量分布')
        # plt.xlabel('reward列表长度')
        # plt.ylabel('智能体数量')

    def save_models(self, episode, base_dir,final,succ_ratio):
        if final is True:
            file_dir = f"{base_dir}/final"
        else:
            file_dir=f"{base_dir}/episode_{episode}"
        model_type = "final" if final is True else "checkpoint"

        if not os.path.exists(file_dir):
            os.makedirs(file_dir)
        logging.basicConfig(
            level=logging.INFO,  # 日志级别
            format='%(asctime)s [%(levelname)s] %(message)s',  # 日志格式
            datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
            filename=f'{file_dir}/model.log',  # 日志文件名
            filemode='a'  # 追加模式写入日志文件
        )

        self.critics.save_model(file_dir + f'/MAPPO_critics.pth')
        print(f'Saving {model_type} episode_{episode} MAPPO_critics network successfully!')
        logging.info(f'Saving {model_type} episode_{episode} MAPPO_critics network successfully!')
        self.actors.save_model(file_dir + f'/MAPPO_actors.pth')
        print(f'Saving {model_type} episode_{episode} MAPPO_actors network successfully!')
        logging.info(f'Saving {model_type} episode_{episode} MAPPO_actors network successfully!')

        if succ_ratio > self.best_succ_ratio:
            self.best_succ_ratio = succ_ratio
            best_dir = f"{base_dir}/best"
            if not os.path.exists(best_dir):
                os.makedirs(best_dir)
            self.critics.save_model(best_dir + f'/MAPPO_critics.pth')
            self.actors.save_model(best_dir + f'/MAPPO_actors.pth')
            data = {
                'episode': episode,
                'best_succ_ratio': self.best_succ_ratio,
            }
            with open(best_dir + f'/params.json', 'w') as f:
                json.dump(data, f, indent=4)  # indent=4 美化 JSON 格式

        params = {
            'var': self.var,
            'steps_done': self.steps_done,
        }
        print(f'params: {params}')
        with open(file_dir + f'/params.json', 'w') as f:
            json.dump(params, f, indent=4)  # indent=4 美化 JSON 格式
        print(f'Saving {model_type} episode_{episode} params successfully!')
        logging.info(f'Saving {model_type} episode_{episode} params successfully!')


    def load_models(self, episode, base_dir,final):
        if final is True:
            file_dir = f"{base_dir}/final"
        else:
            file_dir=f"{base_dir}/episode_{episode}"
        model_type = "final" if final is True else "checkpoint"

        logging.basicConfig(
            level=logging.INFO,  # 日志级别
            format='%(asctime)s [%(levelname)s] %(message)s',  # 日志格式
            datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
            filename=f'{file_dir}/model.log',  # 日志文件名
            filemode='a'  # 追加模式写入日志文件
        )


        self.critics.load_model(file_dir + f'/MAPPO_critics.pth')
        print(f'Loading {model_type} episode_{episode} MAPPO_critics network successfully!')
        logging.info(f'Loading {model_type} episode_{episode} MAPPO_critics network successfully!')
        self.actors.load_model(file_dir + f'/MAPPO_actors.pth')
        print(f'Loading {model_type} episode_{episode} MAPPO_actors network successfully!')
        logging.info(f'Loading {model_type} episode_{episode} MAPPO_actors network successfully!')

        best_dir = f"{base_dir}/best"
        with open(best_dir + f'/params.json', "r") as f:
            data = json.load(f)
            self.best_succ_ratio = data['best_succ_ratio']

        with open(file_dir+f'/params.json', 'r') as f:
            params=json.load(f)
            print(f'params: {params}')
            var=params['var']
            steps_done=params['steps_done']
            self.var=var
            self.steps_done=steps_done

        print(f'Loading {model_type} episode_{episode} params successfully!')
        logging.info(f'Loading {model_type} episode_{episode} params successfully!')

import torch
import torch.nn as nn
import torch.nn.functional as F

from spacetasksim.enum.task_type_enum import TaskTypeEnum


class Critic(nn.Module):
    def __init__(self, n_agent, dim_observation, dim_action, dim_hidden,
                 dim_model=256, nhead=8, num_layers=6):
        super(Critic, self).__init__()
        self.n_agent = n_agent
        self.dim_observation = dim_observation
        self.dim_action = dim_action
        obs_dim = self.dim_observation * self.n_agent
        act_dim = self.dim_action * self.n_agent

        # Observation embedding
        self.obs_embedding = nn.Linear(dim_observation, dim_model)

        # Transformer Encoder
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=dim_model,
                nhead=nhead,
                activation=F.gelu,
                batch_first=True
            ),
            num_layers=num_layers
        )

        self.fc1 = nn.Linear(dim_model * n_agent, dim_hidden)
        self.fc2 = nn.Linear(dim_hidden, dim_hidden)
        self.fc3 = nn.Linear(dim_hidden, 1)


    # obs: batch_size * obs_dim
    # acts: batch_size * act_dim
    def forward(self, obs,task_type):
        # obs shape: [batch_size, n_agent * dim_observation]
        batch_size = obs.size(0)

        # Reshape and embed observations
        obs = obs.view(batch_size, self.n_agent, self.dim_observation)
        obs_emb = F.leaky_relu_(self.obs_embedding(obs))  # [batch_size, n_agent, dim_model]

        # Transformer processing
        transformer_output = self.transformer(obs_emb)  # [batch_size, n_agent, dim_model]

        # Flatten and process
        x = transformer_output.reshape(batch_size, -1)  # [batch_size, n_agent*dim_model]
        x = F.leaky_relu_(self.fc1(x))
        x = F.leaky_relu_(self.fc2(x))
        x = self.fc3(x)

        return x

    def save_model(self, file_dir):
        torch.save(self.state_dict(), file_dir, _use_new_zipfile_serialization=False)

    def load_model(self, file_dir):
        self.load_state_dict(torch.load(file_dir))


class Actor(nn.Module):
    def __init__(self, dim_observation, dim_action,dim_hidden,
                 dim_model=256, nhead=8, num_layers=6):
        super(Actor, self).__init__()
        self.dim_model = dim_model

        # Observation embedding
        self.obs_embedding = nn.Linear(dim_observation, dim_model)

        # Transformer Encoder
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=dim_model,
                nhead=nhead,
                activation=F.gelu,
                batch_first=True
            ),
            num_layers=num_layers
        )

        # Fully connected layers
        self.fc1 = nn.Linear(dim_model, dim_hidden)
        self.fc2 = nn.Linear(dim_hidden, dim_hidden)
        self.fc3 = nn.Linear(dim_hidden, dim_action)


    def forward(self, obs,task_type):
        # obs shape: [batch_size, dim_observation]
        batch_size = obs.size(0)

        # Embed observation
        obs_emb = F.leaky_relu_(self.obs_embedding(obs.unsqueeze(1)))  # [batch_size, 1, dim_model]

        # Transformer processing
        transformer_output = self.transformer(obs_emb)  # [batch_size, 1, dim_model]

        # Process output
        x = transformer_output.squeeze(1)  # [batch_size, dim_model]
        x = F.leaky_relu_(self.fc1(x))
        x = F.leaky_relu_(self.fc2(x))
        x = F.leaky_relu_(self.fc3(x))
        x = F.softmax(x, dim=-1)

        return x

    def save_model(self, file_path):
        torch.save(self.state_dict(), file_path, _use_new_zipfile_serialization=False)

    def load_model(self, file_path):
        self.load_state_dict(torch.load(file_path))

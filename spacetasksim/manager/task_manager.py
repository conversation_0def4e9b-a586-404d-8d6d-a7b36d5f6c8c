import math
import random

import numpy as np
from matplotlib import pyplot as plt
from collections import Counter

from spacetasksim.enum.task_type_enum import TaskTypeEnum
from spacetasksim.enum.step_type_enum import StepTypeEnum
from spacetasksim.enum.node_type_enum import NodeTypeEnum
from spacetasksim.enum.function_enum import FunctionEnum
from spacetasksim.enum.orbit_enum import OrbitEnum
from spacetasksim.task.communicate_task import CommunicateTask
from spacetasksim.task.compute_task import ComputeTask
from spacetasksim.task.sense_task import SenseTask
from spacetasksim.task.step import CommunicateStep,ComputeStep,SenseStep
from spacetasksim.utils.file_util import TaskInfoSerializer
from spacetasksim.utils import geo_util, math_util
from spacetasksim.utils.log_util import CustomLogger


class TaskManager:
    def __init__(self,time_accuracy_digit,config_task,config_resource,grids,stations):
        self._time_accuracy_digit = time_accuracy_digit
        self._config_task = config_task
        self._MTPD_threshold=config_task.get('MTPD_threshold',0.5)

        # computation task config
        self._comp_task_size=config_task['computation'].get('task_size',[3000,6000]) # MB
        self._comp_task_require_unit=config_task['computation'].get('task_requirement_unit',5) # MB s/person
        self._comp_task_TTL_range=config_task['computation'].get('task_TTL_range',[20,30])
        self._comp_cycle_per_byte=config_resource['config_computation'].get('cycle_per_byte',50)
        self._comp_requirement_sort={}

        # communication task config
        self._comm_task_size=config_task['communication'].get('task_size',[30000,60000]) # MB
        self._comm_task_require_unit=config_task['communication'].get('task_requirement_unit',0.5) # MB s/person
        self._comm_task_TTL_range = config_task['communication'].get('task_TTL_range', [5,10])
        self._comm_requirement_sort={}

        # sensing task config
        self._sensing_time=config_task['sensing'].get('sensing_time',[3,6]) # s
        self._forest_age_seg=config_task['sensing'].get('forest_age_seg',[50,100,150,200]) # year
        self._sensing_interval=config_task['sensing'].get('sensing_interval',[720,360,180,120,60]) # minutes/time
        self._sensing_task_TTL_range = config_task['sensing'].get('task_TTL_range', [30, 50])
        self._sensing_accuracy_range=config_task['sensing'].get('sensing_accuracy_range',[0.1,0.9])
        self._sensing_data_increase_rate=config_resource['config_sensing'].get('data_increase_rate',50) # MB/s

        self._task_id_counter=0

        self._station_province_id_mapping={}
        self._generateStationMapping(stations)

        self._ground_task_req_on_grid = {}
        self._ground_task_req_on_station = {}
        self._comm_task_prob_on_station={}
        self._comp_task_prob_on_station = {}
        self._calculateGroundTaskRequirements(grids,stations)
        self._calculateGroundTaskProb()


        self._executing_tasks={enum_type:{} for enum_type in TaskTypeEnum} # {task_type_enum :{ task_id : task}}
        self._finished_tasks={enum_type:{} for enum_type in TaskTypeEnum}  # {task_type_enum :{ task_id : task}}
        self._failed_tasks={enum_type:{} for enum_type in TaskTypeEnum}  # {task_type_enum :{ task_id : task}}

        self._to_scheduled_steps={enum_type:{} for enum_type in StepTypeEnum}  # {step_type_enum:{step_id:step}}
        self._to_start_steps={enum_type:{} for enum_type in StepTypeEnum}  # {step_type_enum:{step_id:step}}
        self._executing_steps={enum_type:{} for enum_type in StepTypeEnum}  # {step_type_enum:{step_id:step}}

        self._last_sche_interval_finished_tasks={enum_type:{} for enum_type in TaskTypeEnum}
        self._last_sche_interval_failed_tasks={enum_type:{} for enum_type in TaskTypeEnum}
        self._last_sim_interval_finished_tasks={enum_type:{} for enum_type in TaskTypeEnum}
        self._last_sim_interval_failed_tasks={enum_type:{} for enum_type in TaskTypeEnum}

        self._last_sche_interval_finished_steps={enum_type:{} for enum_type in StepTypeEnum}
        self._last_sche_interval_failed_steps={enum_type:{} for enum_type in StepTypeEnum}
        self._last_sim_interval_finished_steps={enum_type:{} for enum_type in StepTypeEnum}
        self._last_sim_interval_failed_steps={enum_type:{} for enum_type in StepTypeEnum}

        self._task_gen_factors = {}
        self._new_task_serializer=TaskInfoSerializer(config_task['export_path'],self._time_accuracy_digit)
        self._record = config_task['record']  # bool
        self._export = config_task['export'] # bool
        self._track_ids=[]

        self._comm_require_buffer={}
        self._comp_require_buffer = {}

        #------------统计信息------------#
        self.sum_num=0
        self.sum_comm_num=0
        self.sum_comp_num=0
        self.sum_sens_num=0

        self.tx_positions=[]
        self.rx_positions=[]
        self.comp_positions=[]
        self.sens_positions=[]

        self.comm_task_size_generated=[]
        self.comp_task_size_generated=[]

        self.comm_ttl_history={}
        self.comp_ttl_history={}
        self.sens_ttl_history={}
        self.comm_size_history={}
        self.comp_size_history = {}
        self.sens_time_history = {}
        self.bandwidth_max_history={}
        self.frequency_max_history={}
        self.accuracy_max_frequency={}

        # ------------日志信息------------#
        self.logger=CustomLogger()
        self.log={}
        self.incremental_log={}

    def reset(self):
        self._task_gen_factors = {}
        self._new_task_serializer.clear()
        self._track_ids = []

        self.sum_num=0
        self.sum_comm_num=0
        self.sum_comp_num=0
        self.sum_sens_num=0

        self._executing_tasks={enum_type:{} for enum_type in TaskTypeEnum} # {task_type_enum :{ task_id : task}}
        self._finished_tasks={enum_type:{} for enum_type in TaskTypeEnum}  # {task_type_enum :{ task_id : task}}
        self._failed_tasks={enum_type:{} for enum_type in TaskTypeEnum}  # {task_type_enum :{ task_id : task}}

        self._to_scheduled_steps={enum_type:{} for enum_type in StepTypeEnum}  # {step_type_enum:{step_id:step}}
        self._to_start_steps={enum_type:{} for enum_type in StepTypeEnum}  # {step_type_enum:{step_id:step}}
        self._executing_steps={enum_type:{} for enum_type in StepTypeEnum}  # {step_type_enum:{step_id:step}}

        self._last_interval_finished_tasks={enum_type:{} for enum_type in TaskTypeEnum}
        self._last_interval_failed_tasks={enum_type:{} for enum_type in TaskTypeEnum}
        self._last_interval_finished_steps={enum_type:{} for enum_type in StepTypeEnum}
        self._last_interval_failed_steps={enum_type:{} for enum_type in StepTypeEnum}

        self._comm_require_buffer.clear()
        self._comp_require_buffer.clear()

        # self.draw_trans_positions()
        self.tx_positions.clear()
        self.rx_positions.clear()

        # self.draw_comp_positions()
        self.comp_positions.clear()

        # self.draw_sens_positions()
        self.sens_positions.clear()

        # self.draw_comm_task_size()
        self.comm_task_size_generated.clear()

        # self.draw_comp_task_size()
        self.comp_task_size_generated.clear()

        self.comm_ttl_history={}
        self.comp_ttl_history={}
        self.sens_ttl_history={}
        self.comm_size_history={}
        self.comp_size_history = {}
        self.sens_time_history = {}
        self.bandwidth_max_history={}
        self.frequency_max_history={}
        self.accuracy_max_frequency={}

        self.log={}
        self.incremental_log={}

    def save(self):
        if self._export is True:
            self._new_task_serializer.save()

    def draw_trans_positions(self):
        # 绘制发射端（tx）位置
        plt.figure(clear=True)
        plt.scatter(
            [pos[1] for pos in self.tx_positions],  # x坐标
            [pos[0] for pos in self.tx_positions],  # y坐标
            color='blue',  # 颜色
            alpha=0.2,  # 透明度（值越小越透明）
            s=2,  # 点大小
            edgecolors='none'  # 移除点边缘线条
        )
        plt.title("Transmitter (TX) Positions")
        plt.xlabel("Lon")
        plt.ylabel("Lat")
        plt.grid(True)
        plt.show()

        # 绘制接收端（rx）位置
        plt.figure(clear=True)
        plt.scatter(
            [pos[1] for pos in self.rx_positions],  # x坐标
            [pos[0] for pos in self.rx_positions],  # y坐标
            color='red',  # 颜色
            alpha=0.2,  # 透明度
            s=2,  # 点大小
            edgecolors='none'  # 移除点边缘线条
        )
        plt.title("Receiver (RX) Positions")
        plt.xlabel("Lon")
        plt.ylabel("Lat")
        plt.grid(True)
        plt.show()

    def draw_comp_positions(self):
        # 绘制发射端（tx）位置
        plt.figure(clear=True)
        plt.scatter(
            [pos[1] for pos in self.comp_positions],  # x坐标
            [pos[0] for pos in self.comp_positions],  # y坐标
            color='blue',  # 颜色
            alpha=0.2,  # 透明度（值越小越透明）
            s=2,  # 点大小
            edgecolors='none'  # 移除点边缘线条
        )
        plt.title("Comp Positions")
        plt.xlabel("Lon")
        plt.ylabel("Lat")
        plt.grid(True)
        plt.show()

    def draw_sens_positions(self):
        # 绘制发射端（tx）位置
        plt.figure(clear=True)
        plt.scatter(
            [pos[1] for pos in self.sens_positions],  # x坐标
            [pos[0] for pos in self.sens_positions],  # y坐标
            color='blue',  # 颜色
            alpha=0.2,  # 透明度（值越小越透明）
            s=2,  # 点大小
            edgecolors='none'  # 移除点边缘线条
        )
        plt.title("Sens Positions")
        plt.xlabel("Lon")
        plt.ylabel("Lat")
        plt.grid(True)
        plt.show()

    def draw_comm_task_size(self):
        size_counts = Counter(self.comm_task_size_generated)
        sizes = list(size_counts.keys())
        counts = list(size_counts.values())

        plt.figure(clear=True)  # 设置图形大小
        # 绘制柱状图
        plt.bar(sizes, counts, width=0.8, edgecolor='black', alpha=0.7)

        # 添加标签和标题
        plt.xlabel("Task Size", fontsize=12)
        plt.ylabel("Frequency (Count)", fontsize=12)
        plt.title("Distribution of Comm Task Sizes", fontsize=14)

        # 显示网格
        plt.grid(axis='y', linestyle='--', alpha=0.5)

        # 显示图形
        plt.show()

    def draw_comp_task_size(self):
        size_counts = Counter(self.comp_task_size_generated)
        sizes = list(size_counts.keys())
        counts = list(size_counts.values())

        plt.figure(clear=True)  # 设置图形大小
        # 绘制柱状图
        plt.bar(sizes, counts, width=0.8, edgecolor='black', alpha=0.7)

        # 添加标签和标题
        plt.xlabel("Task Size", fontsize=12)
        plt.ylabel("Frequency (Count)", fontsize=12)
        plt.title("Distribution of Comp Task Sizes", fontsize=14)

        # 显示网格
        plt.grid(axis='y', linestyle='--', alpha=0.5)

        # 显示图形
        plt.show()

    def _generateTaskId(self):
        task_idx=self._task_id_counter
        self._task_id_counter+=1
        step_id = f"task_{task_idx}"
        return step_id

    def _generateStepId(self,task_id,step_idx):
        step_id = f"{task_id}.step_{step_idx}"
        return step_id

    def _generateStationMapping(self,stations):
        for station_id,station in stations.items():
            province=station.getProvince(lang="zh")
            self._station_province_id_mapping[province]=station_id

    def _calculateGroundTaskRequirements(self,grids,stations):
        station_requirements={}
        for station_id,station in stations.items():
            position_geo = stations[station_id].getPosition()
            station_requirements[station_id]={
                'position_geo': position_geo,  # (lat,lon,alt)
                'comp_requirement': [],
                'comm_requirement': [],
                'up_ratio': [],
                'sensing_frequency': [],  # times/s
            }

        gdp_density_list=[]
        for grid_id, grid in grids.items():
            gdp_density=grid.gdp_density
            gdp_density_list.append(gdp_density)

        gdp_density_max=max(gdp_density_list)
        gdp_density_log_max=math.log10(1+10*gdp_density_max)
        for grid_id,grid in grids.items():
            geo_center=grid.geo_center
            position_geo=(geo_center[0],geo_center[1],0)
            gdp_density=grid.gdp_density
            population_density=grid.population_density
            forest_age=grid.forest_age

            gdp_density_log=math.log10(1 + 10 * gdp_density)
            up_ratio=1-gdp_density_log/gdp_density_log_max
            computation_requirement=population_density*self._comp_task_require_unit
            communication_requirement=population_density*self._comm_task_require_unit
            max_idx=len(self._forest_age_seg)
            sensing_interval_idx=max_idx
            for idx in range(max_idx):
                if forest_age<self._forest_age_seg[idx]:
                    sensing_interval_idx=idx
                    break
            sensing_interval=self._sensing_interval[sensing_interval_idx]
            sensing_frequency=1/(60*sensing_interval)

            self._ground_task_req_on_grid[grid_id]={
                'position_geo':position_geo, # (lat,lon,alt)
                'comp_requirement':computation_requirement,
                'comm_requirement':communication_requirement,
                'up_ratio':up_ratio,
                'sensing_frequency':sensing_frequency, # times/s
            }

            # 按地面站计算地面任务需求
            region_name=grid.region_name
            station_id=self._station_province_id_mapping[region_name]
            station_requirements[station_id]['comp_requirement'].append(computation_requirement)
            station_requirements[station_id]['comm_requirement'].append(communication_requirement)
            station_requirements[station_id]['up_ratio'].append(up_ratio)
            station_requirements[station_id]['sensing_frequency'].append(sensing_frequency)

        # 聚合地面站任务需求
        for station_id,station in stations.items():
            position_geo = stations[station_id].getPosition()
            comp_requirement_list=station_requirements[station_id]['comp_requirement']
            comm_requirement_list=station_requirements[station_id]['comm_requirement']
            up_ratio_list=station_requirements[station_id]['up_ratio']
            sensing_frequency_list=station_requirements[station_id]['sensing_frequency']
            self._ground_task_req_on_station[station_id]={
                'position_geo': position_geo,  # (lat,lon,alt)
                'comp_requirement': sum(comp_requirement_list),
                'comm_requirement': sum(comm_requirement_list),
                'up_ratio': sum(up_ratio_list)/len(up_ratio_list),
                'sensing_frequency': sum(sensing_frequency_list),  # times/s
            }

        # 获取计算需求排序
        comp_requirement_sort = sorted(
            self._ground_task_req_on_station.items(),
            key=lambda x: x[1]['comp_requirement'], # 正序
        )
        self._comp_requirement_sort = {
            station_id: idx
            for idx, (station_id, _) in enumerate(comp_requirement_sort)
        }

        # 获取通信需求排序
        comm_requirement_sort = sorted(
            self._ground_task_req_on_station.items(),
            key=lambda x: x[1]['comm_requirement'], # 正序
        )
        self._comm_requirement_sort = {
            station_id: idx
            for idx, (station_id, _) in enumerate(comm_requirement_sort)
        }

    def _calculateGroundTaskProb(self):
        station_ids = list(self._ground_task_req_on_station.keys())
        for task_type in TaskTypeEnum:
            if task_type==TaskTypeEnum.COMMUNICATION:
                requirements = [req['comm_requirement'] for req in self._ground_task_req_on_station.values()]
                prob_dict=self._comm_task_prob_on_station
            elif task_type==TaskTypeEnum.COMPUTATION:
                requirements = [req['comp_requirement'] for req in self._ground_task_req_on_station.values()]
                prob_dict = self._comp_task_prob_on_station
            else:
                break

            # 对数缩放（加1避免log(0)）
            log_requirements = [np.log(req + 1) for req in requirements]

            # 应用sigmoid
            sigmoid_weights = [1 / (1 + np.exp(-x)) for x in log_requirements]

            # 归一化权重
            total_weight = sum(sigmoid_weights)
            if total_weight == 0:
                return None

            normalized_weights = [w / total_weight for w in sigmoid_weights]

            for idx,weight in enumerate(normalized_weights):
                prob_dict[station_ids[idx]]=weight

    def _selectCommEndStation(self):
        station_ids = list(self._ground_task_req_on_station.keys())
        comm_requirements = [req['comm_requirement'] for req in self._ground_task_req_on_station.values()]
        total_comm = sum(comm_requirements)
        if total_comm == 0:
            return None  # 避免除以零

        # 按权重随机选择一个地面站
        selected_station_id = random.choices(
            population=station_ids,
            weights=comm_requirements,
        )[0]
        return selected_station_id

    def _selectStation(self,task_type):
        if task_type==TaskTypeEnum.COMMUNICATION:
            station_ids = list(self._comm_task_prob_on_station.keys())
            probs = list(self._comm_task_prob_on_station.values())
        elif task_type==TaskTypeEnum.COMPUTATION:
            station_ids = list(self._comp_task_prob_on_station.keys())
            probs = list(self._comp_task_prob_on_station.values())
        else:
            raise TypeError(task_type)

        selected_station_id = random.choices(
            population=station_ids,
            weights=probs,
        )[0]
        return selected_station_id

    def _updateTaskGenFactor(self,simulation_interval,sat_transit,nodes):
        sats=nodes[NodeTypeEnum.SAT]
        leo_sat_ids=[sat_id for sat_id,sat in sats.items() if sat.getOrbitType()==OrbitEnum.LEO]
        self._task_gen_factors.clear()
        requirement_on_sats={}
        for station_id,sat_transit_infos in sat_transit.items():
            # station_position=self._ground_task_req_on_station[station_id]['position_geo']
            transit_sat_ids=list(sat_transit_infos.keys())
            task_gen_sat_ids=list(set(transit_sat_ids)&set(leo_sat_ids))
            sat_num=len(task_gen_sat_ids)
            comp_requirement=self._ground_task_req_on_station[station_id]['comp_requirement']*simulation_interval
            comm_requirement=self._ground_task_req_on_station[station_id]['comm_requirement']*simulation_interval
            up_ratio=self._ground_task_req_on_station[station_id]['up_ratio']
            sensing_frequency=self._ground_task_req_on_station[station_id]['sensing_frequency']*simulation_interval

            avg_comp_req=comp_requirement*up_ratio/sat_num
            avg_comm_req=comm_requirement*up_ratio/sat_num
            avg_sensing_frequency=sensing_frequency/sat_num

            # sat_positions=[]
            for sat_id in task_gen_sat_ids:
                # sat=sats[sat_id]
                # sat_position=sat.getPosition()
                # sat_positions.append(sat_position)
                # transit_info=sat_transit_infos[sat_id]
                # print(sat_position,transit_info['altitude_deg'])

                if sat_id not in requirement_on_sats:
                    requirement_on_sats[sat_id]={
                        'comp_requirement':0,
                        'comm_requirement':0,
                        'sensing_frequency':0,
                        'sensing_frequency_dict':{},

                        'comp_require_dict':{},
                        'comm_require_dict': {}
                    }
                requirement_on_sats[sat_id]['comp_requirement']+=avg_comp_req
                requirement_on_sats[sat_id]['comm_requirement']+=avg_comm_req
                requirement_on_sats[sat_id]['sensing_frequency']+=avg_sensing_frequency
                requirement_on_sats[sat_id]['sensing_frequency_dict'][station_id]=avg_sensing_frequency
                requirement_on_sats[sat_id]['comp_require_dict'][station_id]=avg_comp_req
                requirement_on_sats[sat_id]['comm_require_dict'][station_id] = avg_comm_req
            # self.drawSensTaskOffloadMap(station_position,station_position,sat_positions)
            # raise TypeError

        for sat_id,requirement in requirement_on_sats.items():
            avg_comp_task_size=(self._comp_task_size[0]+self._comp_task_size[1])/2
            avg_comm_task_size=(self._comm_task_size[0]+self._comm_task_size[1])/2
            comp_lambda=requirement['comp_requirement']/avg_comp_task_size
            comm_lambda=requirement['comm_requirement']/avg_comm_task_size
            sensing_lambda=requirement['sensing_frequency']
            sensing_lambda_dict=requirement['sensing_frequency_dict']
            comp_require_dict=requirement['comp_require_dict']
            comm_require_dict=requirement['comm_require_dict']
            self._task_gen_factors[sat_id]={
                # comm comp保留lamda生成方式，新增按省份生成模式
                'comp_lambda':comp_lambda,
                'comm_lambda':comm_lambda,
                'sensing_lambda':sensing_lambda,
                'sensing_lambda_dict':sensing_lambda_dict,

                'comp_require_sum':requirement['comp_requirement'],
                'comm_require_sum': requirement['comm_requirement'],
                'comp_require_dict': comp_require_dict,
                'comm_require_dict': comm_require_dict
            }

    def _generateTTL(self,task_type:TaskTypeEnum):
        if task_type==TaskTypeEnum.COMMUNICATION:
            # mean=(self._comm_task_TTL_range[0]+self._comm_task_TTL_range[1])/2
            # std=(self._comm_task_TTL_range[1]-self._comm_task_TTL_range[0])/4
            # TTL=np.random.normal(mean,std)
            # TTL = max(self._comm_task_TTL_range[0], min(TTL, self._comm_task_TTL_range[1]))
            TTL=np.random.uniform(low=self._comm_task_TTL_range[0],high=self._comm_task_TTL_range[1])
        elif task_type==TaskTypeEnum.COMPUTATION:
            # mean=(self._comp_task_TTL_range[0]+self._comp_task_TTL_range[1])/2
            # std=(self._comp_task_TTL_range[1]-self._comp_task_TTL_range[0])/4
            # TTL=np.random.normal(mean,std)
            # TTL = max(self._comp_task_TTL_range[0], min(TTL, self._comp_task_TTL_range[1]))
            TTL=np.random.uniform(low=self._comp_task_TTL_range[0],high=self._comp_task_TTL_range[1])
        elif task_type==TaskTypeEnum.SENSING:
            # mean=(self._sensing_task_TTL_range[0]+self._sensing_task_TTL_range[1])/2
            # std=(self._sensing_task_TTL_range[1]-self._sensing_task_TTL_range[0])/4
            # TTL=np.random.normal(mean,std)
            # TTL = max(self._sensing_task_TTL_range[0], min(TTL, self._sensing_task_TTL_range[1]))
            TTL=np.random.uniform(low=self._sensing_task_TTL_range[0],high=self._sensing_task_TTL_range[1])
        else:
            raise TypeError('Invalid task type')

        return TTL

    def _generateSensingAccuracy(self):
        mean=(self._sensing_accuracy_range[0]+self._sensing_accuracy_range[1])/2
        std=(self._sensing_accuracy_range[1]-self._sensing_accuracy_range[0])/4
        accuracy=np.random.normal(mean,std)
        accuracy = max(self._sensing_accuracy_range[0], min(accuracy, self._sensing_accuracy_range[1]))
        # accuracy=np.random.uniform(self._sensing_accuracy_range[0],self._sensing_accuracy_range[1])
        return accuracy

    def _generateSensingStation(self,sat_id):
        station_ids = list(self._task_gen_factors[sat_id]['sensing_lambda_dict'].keys())
        lambda_value_list = [lambda_value for lambda_value in self._task_gen_factors[sat_id]['sensing_lambda_dict'].values()]
        total_lambda = sum(lambda_value_list)
        if total_lambda == 0:
            return None  # 避免除以零

        # 按权重随机选择一个地面站
        selected_station_id = random.choices(
            population=station_ids,
            weights=lambda_value_list,
        )[0]
        return selected_station_id

    def _generateTasks(self,current_time,simulation_interval,sat_transit,nodes,task_node_ids=None):
        sats=nodes[NodeTypeEnum.SAT]
        leo_sat_ids=[sat_id for sat_id,sat in sats.items() if sat.getOrbitType()==OrbitEnum.LEO]
        stations=nodes[NodeTypeEnum.GS]
        station_num=len(stations)
        new_tasks=[]

        slot_task_num={}

        for sat_id,gen_factor in self._task_gen_factors.items():
            if task_node_ids is not None and sat_id not in task_node_ids:
                continue
            sat_position=sats[sat_id].getPosition()

            # generate communication tasks
            comm_require_span_unit=(self._comm_task_size[1]-self._comm_task_size[0])/(station_num-1)
            comm_require_buffer=self._comm_require_buffer.get(sat_id,{})
            task_idx=0
            to_generate_task_size={}
            for station_id,requirement in gen_factor['comm_require_dict'].items():
                station_require_idx = self._comm_requirement_sort[station_id]
                task_size = min(self._comm_task_size[0] + station_require_idx * comm_require_span_unit,
                                     self._comm_task_size[1])
                station_require=comm_require_buffer.get(station_id,0)
                station_require+=requirement
                task_num = math.floor(station_require / task_size) # 相当于确定了lambda
                for idx in range(task_num):
                    to_generate_task_size[task_idx]=task_size
                    task_idx+=1
                station_require=max(0,station_require-task_num*task_size)
                comm_require_buffer[station_id]=station_require
            self._comm_require_buffer[sat_id]=comm_require_buffer
            # keys = list(gen_factor['comm_require_dict'].keys())
            # weights = list(gen_factor['comm_require_dict'].values())
            # station_id=random.choices(keys, weights=weights, k=1)[0]  # k=1 表示抽取 1 次
            # station_require_idx=self._comm_requirement_sort[station_id]
            # comm_task_size=min(self._comm_task_size[0]+station_require_idx*comm_require_span_unit,self._comm_task_size[1]) # task_size 方法1
            # comm_require_sum=gen_factor['comm_require_sum']+self._comm_require_buffer.get(sat_id,0)
            # comm_task_num=math.floor(comm_require_sum / comm_task_size) # task_num 方法1
            # comm_require_sum=max(0,comm_require_sum-comm_task_num*comm_task_size)
            # self._comm_require_buffer[sat_id]=comm_require_sum
            # comm_task_num = np.random.poisson(gen_factor['comm_lambda']) # task_num 方法2
            comm_type_name=TaskTypeEnum.getNameByEnum(TaskTypeEnum.COMMUNICATION)
            comm_task_num=len(to_generate_task_size)
            self.sum_num+=comm_task_num
            self.sum_comm_num+=comm_task_num
            for i in range(comm_task_num):
                task_id=self._generateTaskId()
                TTL=self._generateTTL(TaskTypeEnum.COMMUNICATION)
                deadline=current_time+TTL
                comm_task_size=to_generate_task_size[i]
                mean=(self._comm_task_size[0]+self._comm_task_size[1])/2
                std=(self._comm_task_size[1]-self._comm_task_size[0])/4
                comm_task_size=np.random.normal(mean,std)
                comm_task_size = max(self._comm_task_size[0], min(comm_task_size, self._comm_task_size[1])) # task_size 方法2
                # comm_task_size=random.uniform(self._comm_task_size[0],self._comm_task_size[1]) # task_size 方法3
                if task_node_ids is not None:
                    all_transit_ids = [
                        transit_id
                        for transit_info in sat_transit.values()
                        for transit_id in transit_info.keys()
                    ]
                    common_ids = set(all_transit_ids) & set(leo_sat_ids)  # 计算交集
                else:
                    rx_station_id=self._selectCommEndStation()
                    rx_station_geo=stations[rx_station_id].getPosition()[:2]
                    sat_transit_ids = list(sat_transit[rx_station_id].keys())
                    common_ids = set(sat_transit_ids)  & set(leo_sat_ids)  # 计算交集
                common_ids.discard(sat_id)
                candidate_rx_sat_ids = list(common_ids)
                # if task_node_ids is not None:
                #     candidate_rx_sat_ids = list(set(candidate_rx_sat_ids)&set(task_node_ids))
                rx_sat_id = random.choice(candidate_rx_sat_ids)
                rx_sat_position=sats[rx_sat_id].getPosition()
                tx_station_id=self._selectStation(TaskTypeEnum.COMMUNICATION)
                sat_transit_ids = list(sat_transit[tx_station_id].keys())
                common_ids = list(set(sat_transit_ids) & set(leo_sat_ids) & set(task_node_ids))  # 计算交集
                gen_id=random.choice(common_ids)
                gen_position=sats[gen_id].getPosition()
                comm_task=CommunicateTask(task_id,gen_id,rx_sat_id,current_time,TTL,deadline,comm_task_size,self._generateStepId)
                comm_task.start(current_time)
                new_tasks.append(comm_task)
                self._executing_tasks[TaskTypeEnum.COMMUNICATION][task_id]=comm_task
                if sat_id in self._track_ids:
                    self._new_task_serializer.add(task_id=task_id,node_id=sat_id,task_type=comm_type_name,gen_geo=gen_position,arrival_time=current_time,
                                                  ttl=TTL,ddl=deadline,comm_size=comm_task_size,comm_start_geo=gen_position,comm_end_geo=rx_sat_position)
                new_task_num=slot_task_num.get(sat_id,0)
                slot_task_num[sat_id]=new_task_num+1

                self.tx_positions.append(gen_position)
                self.rx_positions.append(rx_sat_position)
                self.comm_task_size_generated.append(comm_task_size)
                task_grid_id=self.getTaskGridId(gen_position[0],gen_position[1])
                self.comm_size_history[task_grid_id]=self.comm_size_history.get(task_grid_id,[])
                self.comm_size_history[task_grid_id].append(comm_task_size)
                self.comm_ttl_history[task_grid_id]=self.comm_ttl_history.get(task_grid_id,[])
                self.comm_ttl_history[task_grid_id].append(TTL)

            # generate computation tasks
            comp_require_span_unit=(self._comp_task_size[1]-self._comp_task_size[0])/(station_num-1)
            comp_require_buffer=self._comp_require_buffer.get(sat_id,{})
            task_idx=0
            to_generate_task_size={}
            for station_id,requirement in gen_factor['comp_require_dict'].items():
                station_require_idx = self._comp_requirement_sort[station_id]
                task_size = min(self._comp_task_size[0] + station_require_idx * comp_require_span_unit,
                                     self._comp_task_size[1])
                station_require=comp_require_buffer.get(station_id,0)
                station_require+=requirement
                task_num = math.floor(station_require / task_size)
                for idx in range(task_num):
                    to_generate_task_size[task_idx]=task_size
                    task_idx+=1
                station_require=max(0,station_require-task_num*task_size)
                comp_require_buffer[station_id]=station_require
            self._comp_require_buffer[sat_id]=comp_require_buffer
            # keys = list(gen_factor['comp_require_dict'].keys())
            # weights = list(gen_factor['comp_require_dict'].values())
            # station_id=random.choices(keys, weights=weights, k=1)[0]  # k=1 表示抽取 1 次
            # station_require_idx=self._comp_requirement_sort[station_id]
            # comp_task_size=min(self._comp_task_size[0]+station_require_idx*comp_require_span_unit,self._comp_task_size[1]) # task_size 方法1
            # comp_require_sum = gen_factor['comp_require_sum'] + self._comp_require_buffer.get(sat_id, 0)
            # comp_task_num = math.floor(comp_require_sum / comp_task_size)  # task_num 方法1
            # comp_require_sum = max(0, comp_require_sum - comp_task_num * comp_task_size)
            # self._comp_require_buffer[sat_id] = comp_require_sum
            # comp_task_num = np.random.poisson(gen_factor['comp_lambda']) # task_num 方法2
            comp_type_name=TaskTypeEnum.getNameByEnum(TaskTypeEnum.COMPUTATION)
            comp_task_num=len(to_generate_task_size)
            self.sum_num += comp_task_num
            self.sum_comp_num += comp_task_num
            for i in range(comp_task_num):
                task_id=self._generateTaskId()
                TTL=self._generateTTL(TaskTypeEnum.COMPUTATION)
                deadline=current_time+TTL
                comp_task_size = to_generate_task_size[i]
                mean=(self._comp_task_size[0]+self._comp_task_size[1])/2
                std=(self._comp_task_size[1]-self._comp_task_size[0])/4
                comp_task_size=np.random.normal(mean,std)
                comp_task_size = max(self._comp_task_size[0], min(comp_task_size, self._comp_task_size[1])) # task_size 方法2
                # comp_task_size=random.uniform(self._comp_task_size[0],self._comp_task_size[1]) # task_size 方法3
                tx_station_id=self._selectStation(TaskTypeEnum.COMPUTATION)
                sat_transit_ids = list(sat_transit[tx_station_id].keys())
                common_ids = list(set(sat_transit_ids) & set(leo_sat_ids) & set(task_node_ids))  # 计算交集
                gen_id=random.choice(common_ids)
                gen_position = sats[gen_id].getPosition()
                comp_task=ComputeTask(task_id,gen_id,current_time,TTL,deadline,comp_task_size,self._generateStepId)
                comp_task.start(current_time)
                new_tasks.append(comp_task)
                self._executing_tasks[TaskTypeEnum.COMPUTATION][task_id]=comp_task
                if sat_id in self._track_ids:
                    self._new_task_serializer.add(task_id=task_id,node_id=sat_id,task_type=comp_type_name,gen_geo=gen_position,arrival_time=current_time,
                                                  ttl=TTL,ddl=deadline,comp_size=comp_task_size,comp_cpb=self._comp_cycle_per_byte)
                new_task_num = slot_task_num.get(sat_id, 0)
                slot_task_num[sat_id] = new_task_num + 1

                self.comp_positions.append(gen_position)
                self.comp_task_size_generated.append(comp_task_size)
                task_grid_id = self.getTaskGridId(gen_position[0], gen_position[1])
                self.comp_size_history[task_grid_id]=self.comp_size_history.get(task_grid_id,[])
                self.comp_size_history[task_grid_id].append(comp_task_size)
                self.comp_ttl_history[task_grid_id]=self.comp_ttl_history.get(task_grid_id,[])
                self.comp_ttl_history[task_grid_id].append(TTL)

            # generate sensing tasks
            sensing_task_num = np.random.poisson(gen_factor['sensing_lambda'])
            sensing_type_name=TaskTypeEnum.getNameByEnum(TaskTypeEnum.SENSING)
            self.sum_num += sensing_task_num
            self.sum_sens_num += sensing_task_num
            for i in range(sensing_task_num):
                task_id=self._generateTaskId()
                TTL=self._generateTTL(TaskTypeEnum.SENSING)
                deadline=current_time+TTL
                accuracy=self._generateSensingAccuracy()
                mean=(self._sensing_time[0]+self._sensing_time[1])/2
                std=(self._sensing_time[1]-self._sensing_time[0])/4
                sensing_time=np.random.normal(mean,std)
                sensing_time = max(self._sensing_time[0], min(sensing_time, self._sensing_time[1]))
                # sensing_time=random.uniform(self._sensing_time[0],self._sensing_time[1])
                station_id=self._generateSensingStation(sat_id)
                station_position = stations[station_id].getPosition()
                data_size=sensing_time*self._sensing_data_increase_rate*accuracy
                sat_transit_ids = list(sat_transit[station_id].keys())
                common_ids = list(set(sat_transit_ids) & set(leo_sat_ids) & set(task_node_ids))  # 计算交集
                gen_id = random.choice(common_ids)
                gen_position = sats[gen_id].getPosition()
                sensing_task=SenseTask(task_id,gen_id,current_time,TTL,deadline,accuracy,sensing_time,data_size,station_position,self._generateStepId)
                sensing_task.start(current_time)
                new_tasks.append(sensing_task)
                self._executing_tasks[TaskTypeEnum.SENSING][task_id]=sensing_task
                if sat_id in self._track_ids:
                    self._new_task_serializer.add(task_id=task_id,node_id=sat_id,task_type=sensing_type_name,gen_geo=gen_position,arrival_time=current_time,
                                                  ttl=TTL,ddl=deadline,sensing_time=sensing_time,sensing_accuracy=accuracy,sensing_position_geo=station_position)
                new_task_num = slot_task_num.get(sat_id, 0)
                slot_task_num[sat_id] = new_task_num + 1

                self.sens_positions.append(gen_position)
                task_grid_id = self.getTaskGridId(gen_position[0], gen_position[1])
                self.sens_time_history[task_grid_id]=self.sens_time_history.get(task_grid_id,[])
                self.sens_time_history[task_grid_id].append(sensing_time)
                self.sens_ttl_history[task_grid_id]=self.sens_ttl_history.get(task_grid_id,[])
                self.sens_ttl_history[task_grid_id].append(TTL)

    def setTrackIds(self,track_ids):
        self._track_ids=track_ids

    def getTaskIdByStepId(self,step_id):
        parts = step_id.split(".")
        return parts[0]

    def getTaskById(self,task_id):
        for task_type in TaskTypeEnum:
            if task_id in  self._executing_tasks[task_type]:
                return self._executing_tasks[task_type][task_id]
            elif task_id in self._finished_tasks[task_type]:
                return self._finished_tasks[task_type][task_id]
            elif task_id in self._failed_tasks[task_type]:
                return self._finished_tasks[task_type][task_id]


    def getToScheduledSteps(self,step_type=None):
        if step_type is None:
            return self._to_scheduled_steps
        assert step_type in TaskTypeEnum,f"Step type is not valid"
        return self._to_scheduled_steps.get(step_type,{})

    def getExecutingSteps(self,step_type=None):
        if step_type is None:
            return self._executing_steps
        assert step_type in StepTypeEnum,f"Step type is not valid"
        return self._executing_steps.get(step_type,{})

    def scheduleCommunicationSteps(self,simulation_time,step_sche_infos,to_start_communication_step_ids):
        to_delete_step_ids=[]
        for step_id,step_sche_info in step_sche_infos.items():
            step=self._to_scheduled_steps[StepTypeEnum.COMMUNICATION][step_id]
            task_id=step.task_id
            task_type=self.getTaskById(task_id).task_type
            required_trans_size=step_sche_info['required_trans_size']
            to_trans_route=step_sche_info['to_trans_route']
            msg=f'Simulation time: {simulation_time}, {task_type.name} TASK: {task_id} offload to {to_trans_route[1]}'
            self.addLog(to_trans_route[0],msg)
            if len(list(set(to_trans_route)))==1:
                to_trans_route=[to_trans_route[0]] # 本地执行
            step.initialize(simulation_time,required_trans_size,to_trans_route)

        #     if step.is_executing:
        #         self._executing_steps[StepTypeEnum.COMMUNICATION][step_id]=step
        #     to_delete_step_ids.append(step_id)
        # for step_id in to_delete_step_ids:
        #     del self._to_scheduled_steps[StepTypeEnum.COMMUNICATION][step_id]

        for step_id in to_start_communication_step_ids:
            if step_id in self._to_scheduled_steps[StepTypeEnum.COMMUNICATION]:
                step = self._to_scheduled_steps[StepTypeEnum.COMMUNICATION][step_id]
            else:
                step = self._to_start_steps[StepTypeEnum.COMMUNICATION][step_id]
            step.start(simulation_time)


    def scheduleComputingSteps(self,simulation_time,step_sche_infos,to_start_computation_step_ids):
        to_delete_step_ids = []
        for step_id, step_sche_info in step_sche_infos.items():
            step = self._to_scheduled_steps[StepTypeEnum.COMPUTATION][step_id]
            node_id=step_sche_info['node_id']
            cpu_id=step_sche_info['cpu_id']
            required_compute_size=step_sche_info['required_compute_size']
            step.initialize(simulation_time,node_id,cpu_id,required_compute_size)
        #     if step.is_executing:
        #         self._executing_steps[StepTypeEnum.COMPUTATION][step_id]=step
        #     to_delete_step_ids.append(step_id)
        # for step_id in to_delete_step_ids:
        #     del self._to_scheduled_steps[StepTypeEnum.COMPUTATION][step_id]

        for step_id in to_start_computation_step_ids:
            if step_id in self._to_scheduled_steps[StepTypeEnum.COMPUTATION]:
                step = self._to_scheduled_steps[StepTypeEnum.COMPUTATION][step_id]
            else:
                step = self._to_start_steps[StepTypeEnum.COMPUTATION][step_id]
            step.start(simulation_time)

    def scheduleSensingSteps(self,simulation_time,step_sche_infos,to_start_sensing_step_ids):
        to_delete_step_ids = []
        for step_id, step_sche_info in step_sche_infos.items():
            step = self._to_scheduled_steps[StepTypeEnum.SENSING][step_id]
            node_id=step_sche_info['node_id']
            camera_id=step_sche_info['camera_id']
            accuracy=step_sche_info['accuracy']
            required_sensing_accuracy=step_sche_info['required_sensing_accuracy']
            required_sensing_time = step_sche_info['required_sensing_time']*(required_sensing_accuracy/accuracy)
            data_size = step_sche_info['data_size']
            target_position = step_sche_info['target_position']

            step.initialize(simulation_time,node_id,camera_id,accuracy,required_sensing_accuracy,required_sensing_time,data_size,target_position)
        #     if step.is_executing:
        #         self._executing_steps[StepTypeEnum.SENSING][step_id]=step
        #     to_delete_step_ids.append(step_id)
        # for step_id in to_delete_step_ids:
        #     del self._to_scheduled_steps[StepTypeEnum.SENSING][step_id]

        for step_id in to_start_sensing_step_ids:
            if step_id in self._to_scheduled_steps[StepTypeEnum.SENSING]:
                step = self._to_scheduled_steps[StepTypeEnum.SENSING][step_id]
            else:
                step = self._to_start_steps[StepTypeEnum.SENSING][step_id]
            step.start(simulation_time)

    def executeCommunicationStep(self,simulation_time,step_id,tx_id,rx_id,step_trans_size,delay):
        step=self._executing_steps[StepTypeEnum.COMMUNICATION][step_id]
        step.update(simulation_time,tx_id,rx_id,step_trans_size,delay)

    def executeCommunicationSteps(self,simulation_time,step_exe_infos):
        for step_id,step_exe_info in step_exe_infos.items():
            step=self._executing_steps[StepTypeEnum.COMMUNICATION][step_id]
            tx_id=step_exe_info['tx_id']
            rx_id=step_exe_info['rx_id']
            step_trans_size=step_exe_info['step_trans_size']
            delay=step_exe_info['delay']
            step.update(simulation_time, tx_id, rx_id, step_trans_size, delay)

    def executeComputingStep(self,simulation_time,step_id,step_compute_size):
        step = self._executing_steps[StepTypeEnum.COMPUTATION][step_id]
        step.update(simulation_time,step_compute_size)

    def executeComputingSteps(self,simulation_time,step_exe_infos):
        for step_id,step_exe_info in step_exe_infos.items():
            step=self._executing_steps[StepTypeEnum.COMPUTATION][step_id]
            step_compute_size=step_exe_info['step_compute_size']
            step.update(simulation_time, step_compute_size)

    def executingSensingStep(self,simulation_time,step_id,step_sense_time):
        step=self._executing_steps[StepTypeEnum.SENSING][step_id]
        step.update(simulation_time,step_sense_time)

    def executeSensingSteps(self,simulation_time,step_exe_infos):
        for step_id,step_exe_info in step_exe_infos.items():
            step=self._executing_steps[StepTypeEnum.SENSING][step_id]
            step_sense_time=step_exe_info['step_sense_time']
            step.update(simulation_time, step_sense_time)

    def _checkSteps(self,current_time):
        comm_steps=self._executing_steps[StepTypeEnum.COMMUNICATION]
        for step_id,step in comm_steps.items():
            last_transmit_time=step.last_transmit_time
            if current_time-last_transmit_time>self._MTPD_threshold:
                step._fail(current_time)

        comp_steps=self._executing_steps[StepTypeEnum.COMPUTATION]
        for step_id,step in comp_steps.items():
            last_compute_time=step.last_compute_time
            if current_time-last_compute_time>self._MTPD_threshold:
                step._fail(current_time)

        sens_steps=self._executing_steps[StepTypeEnum.SENSING]
        for step_id,step in sens_steps.items():
            last_sense_time=step.last_sense_time
            if current_time-last_sense_time>self._MTPD_threshold:
                step._fail(current_time)

    def update(self,current_time,simulation_interval,sche_interval,sat_transit,nodes,task_node_ids=None):
        current_time=round(current_time,self._time_accuracy_digit)
        self._updateTaskGenFactor(simulation_interval,sat_transit,nodes)
        self._generateTasks(current_time,simulation_interval,sat_transit,nodes,task_node_ids)
        self._checkSteps(current_time)
        print("sum_num:",self.sum_num,"sum_comm:",self.sum_comm_num,'sum_comp:',self.sum_comp_num,'sum_sens:',self.sum_sens_num)

        self._last_sim_interval_finished_steps = {enum_type: {} for enum_type in StepTypeEnum}
        self._last_sim_interval_failed_steps = {enum_type: {} for enum_type in StepTypeEnum}
        self._last_sim_interval_finished_tasks={enum_type:{} for enum_type in TaskTypeEnum}
        self._last_sim_interval_failed_tasks = {enum_type: {} for enum_type in TaskTypeEnum}

        if math_util.float_mod(current_time , sche_interval,self._time_accuracy_digit) < 1e-9:
            self._last_sche_interval_finished_steps = {enum_type: {} for enum_type in StepTypeEnum}
            self._last_sche_interval_failed_steps = {enum_type: {} for enum_type in StepTypeEnum}
            self._last_sche_interval_finished_tasks = {enum_type: {} for enum_type in TaskTypeEnum}
            self._last_sche_interval_failed_tasks = {enum_type: {} for enum_type in TaskTypeEnum}

        for step_type_enum, step_dicts in self._executing_steps.items():
            for step_id, step in step_dicts.items():
                if step.is_finished:
                    self._last_sim_interval_finished_steps[step_type_enum][step_id]=step
                    self._last_sche_interval_finished_steps[step_type_enum][step_id] = step
                elif step.is_failed:
                    self._last_sim_interval_failed_steps[step_type_enum][step_id]=step
                    self._last_sche_interval_failed_steps[step_type_enum][step_id] = step

        self._executing_steps={enum_type:{} for enum_type in StepTypeEnum}
        self._to_scheduled_steps={enum_type:{} for enum_type in StepTypeEnum}
        self._to_start_steps = {enum_type: {} for enum_type in StepTypeEnum}
        to_delete_task_ids={enum_type:[] for enum_type in TaskTypeEnum}
        for task_type_enum,task_dicts in self._executing_tasks.items():
            for task_id,task in task_dicts.items():
                task.update(current_time)
                if task.is_finished:
                    self._finished_tasks[task_type_enum]=self._finished_tasks.get(task_type_enum,{})
                    self._finished_tasks[task_type_enum][task_id]=task
                    self._last_sim_interval_finished_tasks[task_type_enum][task_id]=task
                    self._last_sche_interval_finished_tasks[task_type_enum][task_id] = task
                    to_delete_task_ids[task_type_enum].append(task_id)
                elif task.is_failed:
                    self._failed_tasks[task_type_enum]=self._finished_tasks.get(task_type_enum,{})
                    self._failed_tasks[task_type_enum][task_id]=task
                    self._last_sim_interval_failed_tasks[task_type_enum][task_id]=task
                    self._last_sche_interval_finished_tasks[task_type_enum][task_id] = task
                    to_delete_task_ids[task_type_enum].append(task_id)

                else:
                    next_steps=task.getNextSteps()
                    for step_id,step in next_steps.items():
                        step_type = step.step_type
                        # if task.isLastStepFinished(step) == True and step.is_initialized:
                        #     step.start()

                        if step.is_not_initialized:
                            self._to_scheduled_steps[step_type][step_id]=step
                        elif step.is_initialized:
                            self._to_start_steps[step_type][step_id]=step
                        elif step.is_executing:
                            self._executing_steps[step_type][step_id]=step
        for task_type,task_ids in to_delete_task_ids.items():
            for task_id in task_ids:
                del self._executing_tasks[task_type][task_id]



    # 获取step
    def getLastScheIntervalFinishedSteps(self,step_type:StepTypeEnum=None):
        if step_type is None:
            return self._last_sche_interval_finished_steps
        else:
            return self._last_sche_interval_finished_steps.get(step_type,{})
    def getLastScheIntervalFailedSteps(self,step_type:StepTypeEnum=None):
        if step_type is None:
            return self._last_sche_interval_failed_steps
        else:
            return self._last_sche_interval_failed_steps.get(step_type,{})
    def getLastSimIntervalFinishedSteps(self,step_type:StepTypeEnum=None):
        if step_type is None:
            return self._last_sim_interval_finished_steps
        else:
            return self._last_sim_interval_finished_steps.get(step_type,{})
    def getLastSimIntervalFailedSteps(self,step_type:StepTypeEnum=None):
        if step_type is None:
            return self._last_sim_interval_failed_steps
        else:
            return self._last_sim_interval_failed_steps.get(step_type,{})
    def getLastScheIntervalTerminatedSteps(self,step_type:StepTypeEnum=None):
        step_dict={enum_type: {} for enum_type in StepTypeEnum}
        if step_type is None:
            for type_enum in StepTypeEnum:
                step_dict[type_enum].update(self._last_sche_interval_finished_steps[type_enum])
                step_dict[type_enum].update(self._last_sche_interval_failed_steps[type_enum])
        else:
            step_dict[step_type].update(self._last_sche_interval_finished_steps[step_type])
            step_dict[step_type].update(self._last_sche_interval_failed_steps[step_type])
        return step_dict
    def getLastSimIntervalTerminatedSteps(self,step_type:StepTypeEnum=None):
        step_dict={enum_type: {} for enum_type in StepTypeEnum}
        if step_type is None:
            for type_enum in StepTypeEnum:
                step_dict[type_enum].update(self._last_sim_interval_finished_steps[type_enum])
                step_dict[type_enum].update(self._last_sim_interval_failed_steps[type_enum])
        else:
            step_dict[step_type].update(self._last_sim_interval_finished_steps[step_type])
            step_dict[step_type].update(self._last_sim_interval_failed_steps[step_type])
        return step_dict

    # 获取task
    def getLastScheIntervalFinishedTasks(self,task_type:TaskTypeEnum=None):
        if task_type is None:
            return self._last_sche_interval_finished_tasks
        else:
            return self._last_sche_interval_finished_tasks.get(task_type,{})
    def getLastScheIntervalFailedTasks(self,task_type:TaskTypeEnum=None):
        if task_type is None:
            return self._last_sche_interval_failed_tasks
        else:
            return self._last_sche_interval_failed_tasks.get(task_type,{})
    def getLastSimIntervalFinishedTasks(self,task_type:TaskTypeEnum=None):
        if task_type is None:
            return self._last_sim_interval_finished_tasks
        else:
            return self._last_sim_interval_finished_tasks.get(task_type,{})
    def getLastSimIntervalFailedTasks(self,task_type:TaskTypeEnum=None):
        if task_type is None:
            return self._last_sim_interval_failed_tasks
        else:
            return self._last_sim_interval_failed_tasks.get(task_type,{})

    def drawSensTaskOffloadMap(self,gen_position,sense_position,sat_positions):
        # 提取坐标（只取 lat 和 lon）
        gen_lat, gen_lon = gen_position[0], gen_position[1]
        sense_lat, sense_lon = sense_position[0], sense_position[1]
        sat_lat_lon = [(pos[0], pos[1]) for pos in sat_positions]

        # 拆分卫星坐标
        if sat_lat_lon:
            sat_lats, sat_lons = zip(*sat_lat_lon)
        else:
            sat_lats, sat_lons = [], []

        # 画图
        plt.figure(clear=True)
        plt.scatter(gen_lon, gen_lat, c='blue', label='Gen Position', marker='o', s=100)
        plt.scatter(sense_lon, sense_lat, c='green', label='Task Position', marker='^', s=100)
        plt.scatter(sat_lons, sat_lats, c='red', label='Sat Positions', marker='s')

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.title('Task Offload Map')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()

    def getTaskGridId(self,lat,lon):
        lat_id=int(lat/5)
        lon_id=int(lon/5)
        task_grid_id=f'lat_{lat_id}.lon_{lon_id}'
        return task_grid_id

    def addLog(self,node_id,message):
        log=self.logger.info(message)
        self.log[node_id]=self.log.get(node_id,[])
        self.log[node_id].append(log)
        self.incremental_log[node_id]=self.incremental_log.get(node_id,[])
        self.incremental_log[node_id].append(log)






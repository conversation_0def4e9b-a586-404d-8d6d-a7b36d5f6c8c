import random

from matplotlib import pyplot as plt

from spacetasksim.spacetasksim_scheduler import SpaceTaskSimScheduler
from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from spacetasksim.enum.function_enum import FunctionEnum
from spacetasksim.enum.step_type_enum import StepTypeEnum
from spacetasksim.enum.task_type_enum import TaskTypeEnum
from spacetasksim.enum.node_type_enum import NodeTypeEnum
# from spacetasksim.predition.predict_gcnlstm_attention import predict

from copy import deepcopy

from spacetasksim.utils import math_util


class BaseStrategyModule():
    def __init__(self):
        super().__init__()
        self.module_tag = "Base"
        print('strategy module: ', self.module_tag)
        self.resourceScheduler = SpaceTaskSimScheduler.getResourceScheduler()
        self.taskScheduler=SpaceTaskSimScheduler.getTaskScheduler()
        self.viewScheduler=SpaceTaskSimScheduler.getViewScheduler()
        self.nodeScheduler=SpaceTaskSimScheduler.getNodeScheduler()
        self.algorithmScheduler=SpaceTaskSimScheduler.getAlgorithmScheduler()

    def getModuleTag(self):
        return self.module_tag

    def initialize(self, env: SpaceTaskSimEnv):
        self.env = env
        self.node_type_dict={
            NodeTypeEnum.SAT:'s',
            NodeTypeEnum.GS:'g'
        }

        self.comm_resources_usage_cache = {}
        self.comp_resources_usage_cache = {}
        self.sensing_resources_usage_cache = {}
        self.storage_resources_usage_cache = {}

        self.collaboration_group={}

    def reset(self):
        pass

    def scheduleStep(self):
        time_accuracy_digit = self.env.time_accuracy_digit
        schedule_interval = self.env.schedule_interval
        simulation_time = self.env.simulation_time
        if not (math_util.float_mod(simulation_time, schedule_interval, time_accuracy_digit) < 1e-9):
            return

        self.comm_resources_usage_cache.clear()
        self.comp_resources_usage_cache.clear()
        self.sensing_resources_usage_cache.clear()
        self.storage_resources_usage_cache.clear()

        self.collaboration_group.clear()

        self.scheduleSensingOffloading()
        self.scheduleCompOffloading()
        self.scheduleCommOffloading()
        self.scheduleComputation()
        self.scheduleSensing()


    def scheduleCommOffloading(self):
        simulation_time=self.env.simulation_time
        to_sched_comm_steps=self.taskScheduler.getToScheduledSteps(self.env,StepTypeEnum.COMMUNICATION,TaskTypeEnum.COMMUNICATION)
        print('comm to_sched_comm_steps:',len(to_sched_comm_steps))
        used_node_ids=[]
        i=0
        for step_id,step in to_sched_comm_steps.items():
            task_id=step.task_id
            task=self.taskScheduler.getTaskById(self.env,step.task_id)
            ttl=task.ttl
            ddl=task.deadline
            last_step = task.getLastStepByStepId(step_id)
            if last_step is None:
                current_node_id = task.init_node_id
            else:
                current_node_id = last_step.current_node_id
            if current_node_id in used_node_ids:
                continue
            required_trans_size = task.required_trans_size
            tx_node_id=current_node_id
            tx_node_type=self.nodeScheduler.getNodeTypeById(self.env,tx_node_id)
            tx_storage_usage_cache = self.storage_resources_usage_cache.get(tx_node_id,{})
            rx_node_id = task.endpoint_node_id
            rx_node_type = self.nodeScheduler.getNodeTypeById(self.env,rx_node_id)
            rx_storage_usage_cache = self.storage_resources_usage_cache.get(rx_node_id,{})

            tx_storage_available=self.resourceScheduler.checkStorageResourcesAvailable(self.env,tx_node_id,required_trans_size,tx_storage_usage_cache)
            rx_storage_available = self.resourceScheduler.checkStorageResourcesAvailable(self.env, rx_node_id,required_trans_size,rx_storage_usage_cache)
            if not (tx_storage_available and rx_storage_available):
                continue


            tx_position=self.nodeScheduler.getNodePositionById(self.env,tx_node_id)
            tx_view_sat_ids = self.viewScheduler.getVisibleSatIds(self.env, tx_position,D=3000,function_type=FunctionEnum.RELAY)
            rx_position=self.nodeScheduler.getNodePositionById(self.env,rx_node_id)
            rx_view_sat_ids=self.viewScheduler.getVisibleSatIds(self.env,rx_position,D=3000,function_type=FunctionEnum.RELAY)
            intersection_sat_ids= list(set(tx_view_sat_ids) & set(rx_view_sat_ids))
            candidate_node_ids=self.viewScheduler.getMiddleSats(self.env,tx_node_id,rx_node_id,intersection_sat_ids,k=30)
            relay_node_id=self.algorithmScheduler.selectRelayNodeId(self.env,required_trans_size,ttl,ddl,tx_node_id,rx_node_id,
                                                                           self.comm_resources_usage_cache,self.storage_resources_usage_cache,candidate_node_ids,mode="predict")
            if relay_node_id is None:
                continue
            elif relay_node_id==-1:
                step._fail(self.env.simulation_time)
                continue

            relay_node_type = self.nodeScheduler.getNodeTypeById(self.env,relay_node_id)
            to_trans_route = [tx_node_id, relay_node_id, rx_node_id]
            node_type_list=[tx_node_type,relay_node_type,rx_node_type]
            self._updateCommResourceUsageCache(to_trans_route,node_type_list)
            self._updateStorageResourceUsageCache(to_trans_route,required_trans_size)
            self.taskScheduler.setCommStepScheInfo(self.env,step_id,required_trans_size,to_trans_route)
            self.collaboration_group[task_id]=candidate_node_ids
            i+=1
        print('comm offload:',i)


    def scheduleCompOffloading(self):
        to_sched_comm_steps = self.taskScheduler.getToScheduledSteps(self.env, StepTypeEnum.COMMUNICATION,TaskTypeEnum.COMPUTATION)
        print('comp to_sched_comm_steps:', len(to_sched_comm_steps))
        used_node_ids=[]
        i=0
        for step_id, step in to_sched_comm_steps.items():
            task_id = step.task_id
            task=self.taskScheduler.getTaskById(self.env,step.task_id)
            ttl=task.ttl
            ddl=task.deadline
            required_compute_size = task.required_compute_size
            last_step = task.getLastStepByStepId(step_id)
            if last_step is None:
                current_node_id = task.init_node_id
            else:
                current_node_id = last_step.current_node_id
            if current_node_id in used_node_ids:
                continue
            current_position=self.nodeScheduler.getNodePositionById(self.env, current_node_id)
            current_node_type=self.nodeScheduler.getNodeTypeById(self.env,current_node_id)

            candidate_node_ids=self.viewScheduler.getVisibleSatIds(self.env,current_position,D=3000,K=30,function_type=FunctionEnum.COMPUTATION)
            storage_usage_cache = self.storage_resources_usage_cache.get(current_node_id, {})
            storage_available = self.resourceScheduler.checkStorageResourcesAvailable(self.env, current_node_id,required_compute_size,storage_usage_cache)
            if not storage_available:
                continue
            offload_node_id=self.algorithmScheduler.selectCompNodeId(self.env,required_compute_size,ttl,ddl,current_node_id,self.comp_resources_usage_cache,
                                                                                 self.comm_resources_usage_cache,self.storage_resources_usage_cache,candidate_node_ids,mode="predict")
            if offload_node_id is None:
                continue
            elif offload_node_id==-1:
                step._fail(self.env.simulation_time)
                continue
            offload_node_type = self.nodeScheduler.getNodeTypeById(self.env,offload_node_id)
            to_trans_route = [current_node_id, offload_node_id]
            node_type_list=[current_node_type,offload_node_type]
            self._updateCommResourceUsageCache(to_trans_route, node_type_list)
            self._updateStorageResourceUsageCache(to_trans_route, required_compute_size)
            self.taskScheduler.setCommStepScheInfo(self.env,step_id,required_compute_size,to_trans_route)
            self.collaboration_group[task_id] = candidate_node_ids
            i += 1
        print('comp offload:', i)

    def scheduleSensingOffloading(self):
        to_sched_comm_steps = self.taskScheduler.getToScheduledSteps(self.env, StepTypeEnum.COMMUNICATION,TaskTypeEnum.SENSING)
        print('sensing to_sched_comm_steps:', len(to_sched_comm_steps))
        used_node_ids=[]
        i=0
        for step_id, step in to_sched_comm_steps.items():
            task_id = step.task_id
            task = self.taskScheduler.getTaskById(self.env, step.task_id)
            ttl=task.ttl
            ddl=task.deadline
            data_size = task.data_size
            accuracy=task.required_sensing_accuracy
            sensing_position=task.sensing_position
            required_sensing_time=task.required_sensing_time
            last_step = task.getLastStepByStepId(step_id)
            if last_step is None:
                current_node_id = task.init_node_id
            else:
                current_node_id = last_step.current_node_id
            if current_node_id in used_node_ids:
                continue
            current_position = self.nodeScheduler.getNodePositionById(self.env, current_node_id)
            current_node_type = self.nodeScheduler.getNodeTypeById(self.env, current_node_id)

            candidate_node_ids = self.viewScheduler.getVisibleSatIds(self.env, current_position,D=3000,K=30,function_type=FunctionEnum.SENSING)
            storage_usage_cache = self.storage_resources_usage_cache.get(current_node_id, {})
            storage_available = self.resourceScheduler.checkStorageResourcesAvailable(self.env, current_node_id,data_size,storage_usage_cache)
            if not storage_available:
                continue
            offload_node_id = self.algorithmScheduler.selectSensingNodeId(self.env,ttl,ddl, sensing_position,required_sensing_time,data_size,accuracy,current_node_id, self.sensing_resources_usage_cache,
                                                                                      self.comm_resources_usage_cache,self.storage_resources_usage_cache,candidate_node_ids,mode="predict")
            if offload_node_id is None:
                continue
            elif offload_node_id==-1:
                step._fail(self.env.simulation_time)
                continue
            offload_node_type = self.nodeScheduler.getNodeTypeById(self.env, offload_node_id)
            to_trans_route = [current_node_id, offload_node_id]
            # node_type_list = [current_node_type, offload_node_type]
            # self._updateCommResourceUsageCache(to_trans_route, node_type_list)
            # self._updateStorageResourceUsageCache(to_trans_route, data_size)
            self.taskScheduler.setCommStepScheInfo(self.env,step_id,0,to_trans_route)
            self.collaboration_group[task_id] = candidate_node_ids
            i += 1
        print('sens offload:', i)

    def scheduleComputation(self):
        to_sched_comp_steps=self.taskScheduler.getToScheduledSteps(self.env,StepTypeEnum.COMPUTATION,TaskTypeEnum.COMPUTATION)
        print('comp to_sched_comp_steps:', len(to_sched_comp_steps))
        i=0
        for step_id,step in to_sched_comp_steps.items():
            task=self.taskScheduler.getTaskById(self.env,step.task_id)
            required_compute_size=task.required_compute_size
            last_step=task.getLastStepByStepId(step_id)
            if last_step is None:
                current_node_id=task.init_node_id
            else:
                current_node_id=last_step.current_node_id
            comp_usage_cache=self.comp_resources_usage_cache.get(current_node_id,{})
            comp_resource_available=self.resourceScheduler.checkCompResourcesAvailable(self.env,current_node_id,comp_usage_cache)
            if not comp_resource_available:
                continue
            cpu_id=self.algorithmScheduler.selectCPU(self.env,current_node_id,comp_usage_cache)
            if cpu_id is None:
                continue
            self._updateCompResourceUsageCache(current_node_id,cpu_id)
            self._updateStorageResourceUsageCache([current_node_id], required_compute_size)
            self.taskScheduler.setCompStepScheInfo(self.env,step_id,current_node_id,cpu_id,required_compute_size)
            i+=1
        print('comp cpu allocation:', i)

    def scheduleSensing(self):
        to_sched_sensing_steps=self.taskScheduler.getToScheduledSteps(self.env,StepTypeEnum.SENSING,TaskTypeEnum.SENSING)
        print('sensing to_sched_sensing_steps:', len(to_sched_sensing_steps))
        i=0
        for step_id, step in to_sched_sensing_steps.items():
            task = self.taskScheduler.getTaskById(self.env, step.task_id)
            data_size=task.data_size
            target_position=task.sensing_position
            required_sensing_time=task.required_sensing_time
            required_sensing_accuracy=task.required_sensing_accuracy
            last_step = task.getLastStepByStepId(step_id)
            if last_step is None:
                current_node_id = task.init_node_id
            else:
                current_node_id = last_step.current_node_id
            sensing_usage_cache=self.sensing_resources_usage_cache.get(current_node_id,{})
            sensing_resource_available=self.resourceScheduler.checkSensingResourcesAvailable(self.env,current_node_id,required_sensing_accuracy,sensing_usage_cache)
            if not sensing_resource_available:
                continue
            camera_id,accuracy=self.algorithmScheduler.selectCamera(self.env,current_node_id,required_sensing_accuracy,sensing_usage_cache)
            if camera_id is None:
                continue
            self._updateSensingResourceUsageCache(current_node_id,camera_id)
            self._updateStorageResourceUsageCache([current_node_id], data_size)
            self.taskScheduler.setSensingScheInfo(self.env,step_id,current_node_id,camera_id,accuracy,required_sensing_accuracy,required_sensing_time,data_size,target_position)
            i += 1
        print('sensing camera allocation:', i)

    def _updateCommResourceUsageCache(self,route_list,node_type_list):
        comm_resources_usage_cache_template={
            'busy_s2s_beam_transmit': 0,
            'busy_s2s_beam_receive': 0,
            'busy_g2s_beam_transmit': 0,
            'busy_g2s_beam_receive': 0,
            'busy_s2g_beam_transmit': 0,
            'busy_s2g_beam_receive': 0,
        }
        route_length=len(route_list)
        for idx in range(route_length - 1):
            tx_node_id = route_list[idx]
            tx_node_type = node_type_list[idx]
            rx_node_id = route_list[idx + 1]
            rx_node_type = node_type_list[idx + 1]
            if tx_node_id not in self.comm_resources_usage_cache:
                self.comm_resources_usage_cache[tx_node_id] = deepcopy(comm_resources_usage_cache_template)
            if rx_node_id not in self.comm_resources_usage_cache:
                self.comm_resources_usage_cache[rx_node_id] = deepcopy(comm_resources_usage_cache_template)
            tx_type_code = self.node_type_dict[tx_node_type]
            rx_type_code = self.node_type_dict[rx_node_type]
            self.comm_resources_usage_cache[tx_node_id][f'busy_{tx_type_code}2{rx_type_code}_beam_transmit'] += 1
            self.comm_resources_usage_cache[rx_node_id][f'busy_{tx_type_code}2{rx_type_code}_beam_receive'] += 1

    def _updateCompResourceUsageCache(self,node_id,cpu_id):
        comp_resources_usage_cache_template = {
            'busy_cpu_ids': [],
        }
        if node_id not in self.comp_resources_usage_cache:
            self.comp_resources_usage_cache[node_id] = deepcopy(comp_resources_usage_cache_template)
        self.comp_resources_usage_cache[node_id]['busy_cpu_ids'].append(cpu_id)

    def _updateSensingResourceUsageCache(self,node_id,camera_id):
        sensing_resources_usage_cache_template = {
            'busy_camera_ids': [],
        }
        if node_id not in self.sensing_resources_usage_cache:
            self.sensing_resources_usage_cache[node_id] = deepcopy(sensing_resources_usage_cache_template)
        self.sensing_resources_usage_cache[node_id]['busy_camera_ids'].append(camera_id)

    def _updateStorageResourceUsageCache(self,node_id_list,apply_size):
        storage_resources_usage_cache_template={
            'occupied_space': 0,
        }
        for idx, node_id in enumerate(node_id_list):
            if node_id not in self.storage_resources_usage_cache:
                self.storage_resources_usage_cache[node_id] = deepcopy(storage_resources_usage_cache_template)
            self.storage_resources_usage_cache[node_id][f'occupied_space'] += apply_size


    def predictRequirement(self):
        simulation_time = self.env.simulation_time
        simulation_interval=self.env.simulation_interval
        time_accuracy_digit = self.env.time_accuracy_digit
        sequence_length=5

        for i in range(sequence_length, 0, -1):
            time_slot=round(simulation_time-i*simulation_interval, time_accuracy_digit)
            resource_info=self.env.resource_manager._resource_serializer.add(time_slot)
            task_info=self.env.task_manager._new_task_serializer.get(time_slot)



import json
import os

from matplotlib import pyplot as plt

from .spacetasksim_env import SpaceTaskSimEnv
from spacetasksim.enum.task_type_enum import TaskTypeEnum
from spacetasksim.enum.fail_result_enum import CommResult,CompR<PERSON>ult,SenseResult
class SpaceTaskSimEvaluation:
    def __init__(self,env:SpaceTaskSimEnv, sche_strategy_module,base_path="./evaluation/"):
        # Path to save image
        self.base_path = base_path # 和main在同一文件夹下
        self.algorithm_module=sche_strategy_module
        self.tag = sche_strategy_module.getModuleTag()
        self.env=env
        self.color={
            'yellow':(248/255,230/255,32/255),
            'green':(53/255,183/255,119/255),
            'blue':(48/255,104/255,141/255),
            'purple':(68/255,4/255,90/255)
        }
        self.color_list=list(self.color.values())
        if not os.path.exists(self.base_path):
            os.makedirs(self.base_path)

        self.initOrResetStepIndicators()
        self.initOrResetStepRecords()
        self.initOrResetEpisodeRecords()

    def reset(self):
        self.initOrResetStepIndicators()
        self.initOrResetStepRecords()
        self.initOrResetEpisodeRecords()

    def reset_sche_strategy_module(self,sche_strategy_module):
        self.algorithm_module=sche_strategy_module
        self.tag = sche_strategy_module.getModuleTag()
        self.initOrResetStepIndicators()
        self.initOrResetStepRecords()
        self.initOrResetEpisodeRecords()

    def initOrResetStepIndicators(self):
        # 仿真时间
        self.simulation_time = None
        self.step_num = 0

        # 完成数量
        self.finish_num=0
        self.finish_num_comm=0
        self.finish_num_comp = 0
        self.finish_num_sensing = 0

        # 失败数量
        self.fail_num=0
        self.fail_num_comm=0
        self.fail_num_comp = 0
        self.fail_num_sensing = 0

        # 终止数量
        self.terminated_num=0
        self.terminated_num_comm=0
        self.terminated_num_comp = 0
        self.terminated_num_sensing = 0

        # 完成率
        self.completion_ratio = 0
        self.completion_ratio_comm=0
        self.completion_ratio_comp=0
        self.completion_ratio_sensing = 0

        # 成功任务总完成时间
        self.sum_finish_time=0
        self.sum_finish_time_comm=0
        self.sum_finish_time_comp=0
        self.sum_finish_time_sensing = 0

        # 成功任务平均完成时间
        self.avg_finish_time=0
        self.avg_finish_time_comm=0
        self.avg_finish_time_comp=0
        self.avg_finish_time_sensing = 0

        # 终止任务总完成时间
        self.sum_terminated_time=0
        self.sum_terminated_time_comm=0
        self.sum_terminated_time_comp=0
        self.sum_terminated_time_sensing = 0

        # 终止任务平均完成时间
        self.avg_terminated_time=0
        self.avg_terminated_time_comm=0
        self.avg_terminated_time_comp=0
        self.avg_terminated_time_sensing = 0

        # 任务失败原因
        self.comm_fail_num={result_enum.name:0 for result_enum in CommResult}
        self.comp_fail_num = {result_enum.name: 0 for result_enum in CompResult}
        self.sense_fail_num = {result_enum.name: 0 for result_enum in SenseResult}


    def initOrResetStepRecords(self):
        # 完成数量
        self.steps_finish_num=[]
        self.steps_finish_num_comm=[]
        self.steps_finish_num_comp = []
        self.steps_finish_num_sensing = []

        # 失败数量
        self.steps_fail_num=[]
        self.steps_fail_num_comm=[]
        self.steps_fail_num_comp = []
        self.steps_fail_num_sensing = []

        # 终止数量
        self.steps_terminated_num=[]
        self.steps_terminated_num_comm=[]
        self.steps_terminated_num_comp = []
        self.steps_terminated_num_sensing = []

        # 完成率
        self.steps_completion_ratio = []
        self.steps_completion_ratio_comm=[]
        self.steps_completion_ratio_comp=[]
        self.steps_completion_ratio_sensing = []

        # 成功任务平均完成时间
        self.steps_avg_finish_time=[]
        self.steps_avg_finish_time_comm=[]
        self.steps_avg_finish_time_comp=[]
        self.steps_avg_finish_time_sensing = []

        # 终止任务平均完成时间
        self.steps_avg_terminated_time=[]
        self.steps_avg_terminated_time_comm=[]
        self.steps_avg_terminated_time_comp=[]
        self.steps_avg_terminated_time_sensing = []

        # 任务失败原因
        self.steps_comm_fail_num={result_enum.name:[] for result_enum in CommResult}
        self.steps_comp_fail_num = {result_enum.name: [] for result_enum in CompResult}
        self.steps_sense_fail_num = {result_enum.name: [] for result_enum in SenseResult}

    def initOrResetEpisodeRecords(self):
        # 完成数量
        self.episodes_finish_num=[]
        self.episodes_finish_num_comm=[]
        self.episodes_finish_num_comp = []
        self.episodes_finish_num_sensing = []

        # 失败数量
        self.episodes_fail_num=[]
        self.episodes_fail_num_comm=[]
        self.episodes_fail_num_comp = []
        self.episodes_fail_num_sensing = []

        # 终止数量
        self.episodes_terminated_num=[]
        self.episodes_terminated_num_comm=[]
        self.episodes_terminated_num_comp = []
        self.episodes_terminated_num_sensing = []

        # 完成率
        self.episodes_completion_ratio = []
        self.episodes_completion_ratio_comm=[]
        self.episodes_completion_ratio_comp=[]
        self.episodes_completion_ratio_sensing = []

        # 成功任务平均完成时间
        self.episodes_avg_finish_time=[]
        self.episodes_avg_finish_time_comm=[]
        self.episodes_avg_finish_time_comp=[]
        self.episodes_avg_finish_time_sensing = []

        # 终止任务平均完成时间
        self.episodes_avg_terminated_time=[]
        self.episodes_avg_terminated_time_comm=[]
        self.episodes_avg_terminated_time_comp=[]
        self.episodes_avg_terminated_time_sensing = []

        # 任务失败原因
        self.episodes_comm_fail_num={result_enum.name:[] for result_enum in CommResult}
        self.episodes_comp_fail_num = {result_enum.name: [] for result_enum in CompResult}
        self.episodes_sense_fail_num = {result_enum.name: [] for result_enum in SenseResult}

    def evaluateStep(self):
        self.step_num+=1
        last_step_finished_tasks=self.env.task_manager.getLastSimIntervalFinishedTasks()
        last_step_failed_tasks=self.env.task_manager.getLastSimIntervalFailedTasks()
        # 完成数量
        self.finish_num_comm+=len(last_step_finished_tasks[TaskTypeEnum.COMMUNICATION])
        self.finish_num_comp += len(last_step_finished_tasks[TaskTypeEnum.COMPUTATION])
        self.finish_num_sensing += len(last_step_finished_tasks[TaskTypeEnum.SENSING])
        self.finish_num = self.finish_num_comm+self.finish_num_comp+self.finish_num_sensing

        # 失败数量
        self.fail_num_comm+=len(last_step_failed_tasks[TaskTypeEnum.COMMUNICATION])
        self.fail_num_comp += len(last_step_failed_tasks[TaskTypeEnum.COMPUTATION])
        self.fail_num_sensing += len(last_step_failed_tasks[TaskTypeEnum.SENSING])
        self.fail_num = self.fail_num_comm+self.fail_num_comp+self.fail_num_sensing

        # 终止数量
        self.terminated_num_comm=self.finish_num_comm+self.fail_num_comm
        self.terminated_num_comp = self.finish_num_comp+self.fail_num_comp
        self.terminated_num_sensing =self.finish_num_sensing+self.fail_num_sensing
        self.terminated_num = self.finish_num+self.fail_num

        # 完成率
        self.completion_ratio_comm=self.finish_num_comm/self.terminated_num_comm if self.terminated_num_comm > 0 else 0
        self.completion_ratio_comp=self.finish_num_comp/self.terminated_num_comp if self.terminated_num_comp > 0 else 0
        self.completion_ratio_sensing = self.finish_num_sensing/self.terminated_num_sensing if self.terminated_num_sensing > 0 else 0
        self.completion_ratio = self.finish_num/self.terminated_num if self.terminated_num > 0 else 0

        for task_id,task in last_step_finished_tasks[TaskTypeEnum.COMMUNICATION].items():
            finish_time=task.finish_time-task.start_time
            self.sum_finish_time+=finish_time
            self.sum_finish_time_comm+=finish_time
            self.sum_terminated_time+=finish_time
            self.sum_terminated_time_comm+=finish_time
        for task_id,task in last_step_finished_tasks[TaskTypeEnum.COMPUTATION].items():
            finish_time=task.finish_time-task.start_time
            self.sum_finish_time+=finish_time
            self.sum_finish_time_comp+=finish_time
            self.sum_terminated_time+=finish_time
            self.sum_terminated_time_comp+=finish_time
        for task_id,task in last_step_finished_tasks[TaskTypeEnum.SENSING].items():
            finish_time=task.finish_time-task.start_time
            self.sum_finish_time+=finish_time
            self.sum_finish_time_sensing+=finish_time
            self.sum_terminated_time+=finish_time
            self.sum_terminated_time_sensing+=finish_time

        for task_id,task in last_step_failed_tasks[TaskTypeEnum.COMMUNICATION].items():
            fail_time=task.fail_time-task.start_time
            self.sum_terminated_time+=fail_time
            self.sum_terminated_time_comm+=fail_time
            self.comm_fail_num[task.fail_result.name]+=1
        for task_id,task in last_step_failed_tasks[TaskTypeEnum.COMPUTATION].items():
            fail_time = task.fail_time - task.start_time
            self.sum_terminated_time+=fail_time
            self.sum_terminated_time_comp+=fail_time
            self.comp_fail_num[task.fail_result.name] += 1
        for task_id,task in last_step_failed_tasks[TaskTypeEnum.SENSING].items():
            fail_time = task.fail_time - task.start_time
            self.sum_terminated_time+=fail_time
            self.sum_terminated_time_sensing+=fail_time
            self.sense_fail_num[task.fail_result.name] += 1

        self.avg_finish_time=self.sum_finish_time/self.finish_num if self.finish_num>0 else 0
        self.avg_finish_time_comm=self.sum_finish_time_comm/self.finish_num_comm if self.finish_num_comm>0 else 0
        self.avg_finish_time_comp=self.sum_finish_time_comp/self.finish_num_comp if self.finish_num_comp>0 else 0
        self.avg_finish_time_sensing = self.sum_finish_time_sensing/self.finish_num_sensing if self.finish_num_sensing>0 else 0

        self.avg_terminated_time=self.sum_terminated_time/self.terminated_num if self.terminated_num>0 else 0
        self.avg_terminated_time_comm=self.sum_terminated_time_comm/self.terminated_num_comm if self.terminated_num_comm>0 else 0
        self.avg_terminated_time_comp=self.sum_terminated_time_comp/self.terminated_num_comp if self.terminated_num_comp>0 else 0
        self.avg_terminated_time_sensing = self.sum_terminated_time_sensing/self.terminated_num_sensing if self.terminated_num_sensing>0 else 0

        print('completion_ratio:', self.completion_ratio,self.completion_ratio_comm,self.completion_ratio_comp,self.completion_ratio_sensing)
        print('finish_num:', self.finish_num,self.finish_num_comm,self.finish_num_comp,self.finish_num_sensing)
        print('fail_num:', self.fail_num, self.fail_num_comm, self.fail_num_comp, self.fail_num_sensing)

    def recordStepInfo(self):
        # 完成数量
        self.steps_finish_num.append(self.finish_num)
        self.steps_finish_num_comm.append(self.finish_num_comm)
        self.steps_finish_num_comp.append(self.finish_num_comp)
        self.steps_finish_num_sensing.append(self.finish_num_sensing)

        # 失败数量
        self.steps_fail_num.append(self.fail_num)
        self.steps_fail_num_comm.append(self.fail_num_comm)
        self.steps_fail_num_comp.append(self.fail_num_comp)
        self.steps_fail_num_sensing.append(self.fail_num_sensing)

        # 终止数量
        self.steps_terminated_num.append(self.terminated_num)
        self.steps_terminated_num_comm.append(self.terminated_num_comm)
        self.steps_terminated_num_comp.append(self.terminated_num_comp)
        self.steps_terminated_num_sensing.append(self.terminated_num_sensing)

        # 完成率
        self.steps_completion_ratio.append(self.completion_ratio)
        self.steps_completion_ratio_comm.append(self.completion_ratio_comm)
        self.steps_completion_ratio_comp.append(self.completion_ratio_comp)
        self.steps_completion_ratio_sensing.append(self.completion_ratio_sensing)

        # 成功任务平均完成时间
        self.steps_avg_finish_time.append(self.avg_finish_time)
        self.steps_avg_finish_time_comm.append(self.avg_finish_time_comm)
        self.steps_avg_finish_time_comp.append(self.avg_finish_time_comp)
        self.steps_avg_finish_time_sensing.append(self.avg_finish_time_sensing)

        # 终止任务平均完成时间
        self.steps_avg_terminated_time.append(self.avg_terminated_time)
        self.steps_avg_terminated_time_comm.append(self.avg_terminated_time_comm)
        self.steps_avg_terminated_time_comp.append(self.avg_terminated_time_comp)
        self.steps_avg_terminated_time_sensing.append(self.avg_terminated_time_sensing)

        # 任务失败原因
        for fail_result,fail_num in self.comm_fail_num.items():
            self.steps_comm_fail_num[fail_result].append(fail_num)
        for fail_result,fail_num in self.comp_fail_num.items():
            self.steps_comp_fail_num[fail_result].append(fail_num)
        for fail_result,fail_num in self.sense_fail_num.items():
            self.steps_sense_fail_num[fail_result].append(fail_num)

    def recordEpisodeInfo(self):
        # 完成数量
        self.episodes_finish_num.append(self.finish_num)
        self.episodes_finish_num_comm.append(self.finish_num_comm)
        self.episodes_finish_num_comp.append(self.finish_num_comp)
        self.episodes_finish_num_sensing.append(self.finish_num_sensing)

        # 失败数量
        self.episodes_fail_num.append(self.fail_num)
        self.episodes_fail_num_comm.append(self.fail_num_comm)
        self.episodes_fail_num_comp.append(self.fail_num_comp)
        self.episodes_fail_num_sensing.append(self.fail_num_sensing)

        # 终止数量
        self.episodes_terminated_num.append(self.terminated_num)
        self.episodes_terminated_num_comm.append(self.terminated_num_comm)
        self.episodes_terminated_num_comp.append(self.terminated_num_comp)
        self.episodes_terminated_num_sensing.append(self.terminated_num_sensing)

        # 完成率
        self.episodes_completion_ratio.append(self.completion_ratio)
        self.episodes_completion_ratio_comm.append(self.completion_ratio_comm)
        self.episodes_completion_ratio_comp.append(self.completion_ratio_comp)
        self.episodes_completion_ratio_sensing.append(self.completion_ratio_sensing)

        # 成功任务平均完成时间
        self.episodes_avg_finish_time.append(self.avg_finish_time)
        self.episodes_avg_finish_time_comm.append(self.avg_finish_time_comm)
        self.episodes_avg_finish_time_comp.append(self.avg_finish_time_comp)
        self.episodes_avg_finish_time_sensing.append(self.avg_finish_time_sensing)

        # 终止任务平均完成时间
        self.episodes_avg_terminated_time.append(self.avg_terminated_time)
        self.episodes_avg_terminated_time_comm.append(self.avg_terminated_time_comm)
        self.episodes_avg_terminated_time_comp.append(self.avg_terminated_time_comp)
        self.episodes_avg_terminated_time_sensing.append(self.avg_terminated_time_sensing)

        # 任务失败原因
        for fail_result,fail_num in self.comm_fail_num.items():
            self.episodes_comm_fail_num[fail_result].append(fail_num)
        for fail_result,fail_num in self.comp_fail_num.items():
            self.episodes_comp_fail_num[fail_result].append(fail_num)
        for fail_result,fail_num in self.sense_fail_num.items():
            self.episodes_sense_fail_num[fail_result].append(fail_num)

    def stepInfoToFile(self,episode):
        folder_dir = self.base_path + f"episode/episode_{episode}/"
        if not os.path.exists(folder_dir):
            os.makedirs(folder_dir)
        file_dir = folder_dir + f"evaluation.json"

        data={
            'step_num':self.step_num,

            'steps_finish_num':self.steps_finish_num,
            'steps_finish_num_comm':self.steps_finish_num_comm,
            'steps_finish_num_comp':self.steps_finish_num_comp,
            'steps_finish_num_sensing':self.steps_finish_num_sensing,

            'steps_fail_num':self.steps_fail_num,
            'steps_fail_num_comm':self.steps_fail_num_comm,
            'steps_fail_num_comp':self.steps_fail_num_comp,
            'steps_fail_num_sensing':self.steps_fail_num_sensing,

            'steps_terminated_num':self.steps_terminated_num,
            'steps_terminated_num_comm':self.steps_terminated_num_comm,
            'steps_terminated_num_comp':self.steps_terminated_num_comp,
            'steps_terminated_num_sensing':self.steps_terminated_num_sensing,

            'steps_completion_ratio':self.steps_completion_ratio,
            'steps_completion_ratio_comm':self.steps_completion_ratio_comm,
            'steps_completion_ratio_comp':self.steps_completion_ratio_comp,
            'steps_completion_ratio_sensing':self.steps_completion_ratio_sensing,

            'steps_avg_finish_time':self.steps_avg_finish_time,
            'steps_avg_finish_time_comm':self.steps_avg_finish_time_comm,
            'steps_avg_finish_time_comp':self.steps_avg_finish_time_comp,
            'steps_avg_finish_time_sensing':self.steps_avg_finish_time_sensing,

            'steps_avg_terminated_time':self.steps_avg_terminated_time,
            'steps_avg_terminated_time_comm':self.steps_avg_terminated_time_comm,
            'steps_avg_terminated_time_comp':self.steps_avg_terminated_time_comp,
            'steps_avg_terminated_time_sensing':self.steps_avg_terminated_time_sensing,

            'steps_comm_fail_num':self.steps_comm_fail_num,
            'steps_comp_fail_num': self.steps_comp_fail_num,
            'steps_sense_fail_num': self.steps_sense_fail_num,

        }
        # 将数据写回文件
        with open(file_dir, "w") as file:
            json.dump(data, file, indent=4)  # json格式缩进4个空格

        print(f"save {self.tag} episode_{episode} step records successfully")

    def drawStepInfoByFile(self, episode):
        # json文件路径
        file_path = self.base_path + f"episode/episode_{episode}/evaluation.json"
        # 保存路径
        path_to_save = self.base_path + f'episode/episode_{episode}/'

        if not os.path.exists(path_to_save):
            os.makedirs(path_to_save)

        # 读取 JSON 文件并累积数据
        with open(file_path, 'r') as file:
            data = json.load(file)
            step_num = data['step_num']

            steps_finish_num=data['steps_finish_num']
            steps_finish_num_comm=data['steps_finish_num_comm']
            steps_finish_num_comp=data['steps_finish_num_comp']
            steps_finish_num_sensing=data['steps_finish_num_sensing']

            steps_fail_num=data['steps_fail_num']
            steps_fail_num_comm=data['steps_fail_num_comm']
            steps_fail_num_comp=data['steps_fail_num_comp']
            steps_fail_num_sensing=data['steps_fail_num_sensing']

            steps_terminated_num=data['steps_terminated_num']
            steps_terminated_num_comm=data['steps_terminated_num_comm']
            steps_terminated_num_comp=data['steps_terminated_num_comp']
            steps_terminated_num_sensing=data['steps_terminated_num_sensing']

            steps_completion_ratio=data['steps_completion_ratio']
            steps_completion_ratio_comm=data['steps_completion_ratio_comm']
            steps_completion_ratio_comp=data['steps_completion_ratio_comp']
            steps_completion_ratio_sensing=data['steps_completion_ratio_sensing']

            steps_avg_finish_time=data['steps_avg_finish_time']
            steps_avg_finish_time_comm=data['steps_avg_finish_time_comm']
            steps_avg_finish_time_comp=data['steps_avg_finish_time_comp']
            steps_avg_finish_time_sensing=data['steps_avg_finish_time_sensing']

            steps_avg_terminated_time=data['steps_avg_terminated_time']
            steps_avg_terminated_time_comm=data['steps_avg_terminated_time_comm']
            steps_avg_terminated_time_comp=data['steps_avg_terminated_time_comp']
            steps_avg_terminated_time_sensing=data['steps_avg_terminated_time_sensing']

            steps_comm_fail_num=data['steps_comm_fail_num']
            steps_comp_fail_num=data['steps_comp_fail_num']
            steps_sense_fail_num=data['steps_sense_fail_num']

        # 1.过程监控
        # 1.1 成功任务
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, steps_finish_num, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, steps_finish_num_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, steps_finish_num_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, steps_finish_num_sensing, label='sens', color=self.color['purple'])
        # plt.title('Step Finished Tasks')
        plt.xlabel('Step')
        plt.ylabel('The Number of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'StepFinishedTasks.png')
        plt.close()

        # 1.2 失败任务
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, steps_fail_num, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, steps_fail_num_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, steps_fail_num_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, steps_fail_num_sensing, label='sens', color=self.color['purple'])
        # plt.title('Step Failed Tasks')
        plt.xlabel('Step')
        plt.ylabel('The Number of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'StepFailedTasks.png')
        plt.close()

        # 1.3 已终止任务
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, steps_terminated_num, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, steps_terminated_num_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, steps_terminated_num_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, steps_terminated_num_sensing, label='sens', color=self.color['purple'])
        # plt.title('Step Terminated Tasks')
        plt.xlabel('Step')
        plt.ylabel('The Number of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'StepTerminatedTasks.png')
        plt.close()

        # 2. 完成率
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, steps_completion_ratio, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, steps_completion_ratio_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, steps_completion_ratio_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, steps_completion_ratio_sensing, label='sens', color=self.color['purple'])
        # plt.title('Step Completion Ratio')
        plt.xlabel('Step')
        plt.ylabel('Completion Ratio of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'StepCompletionRatio.png')
        plt.close()

        # 3. 完成时间
        # 3.1 成功任务平均时延
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, steps_avg_finish_time, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, steps_avg_finish_time_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, steps_avg_finish_time_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, steps_avg_finish_time_sensing, label='sens', color=self.color['purple'])
        # plt.title('Avg Spend Time')
        plt.xlabel('Step')
        plt.ylabel('Time')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'FinishTasksAvgSpendTime.png')
        plt.close()

        # 3.2 终止任务平均时延
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, steps_avg_terminated_time, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, steps_avg_terminated_time_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, steps_avg_terminated_time_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, steps_avg_terminated_time_sensing, label='sens', color=self.color['purple'])
        # plt.title('Avg Spend Time')
        plt.xlabel('Step')
        plt.ylabel('Time')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'TerminatedTasksAvgSpendTime.png')
        plt.close()

        # 4. 失败原因
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        for idx, (fail_result, fail_num_list) in enumerate(steps_comm_fail_num.items()):
            plt.plot(x_indices, fail_num_list, label=fail_result, color=self.color_list[idx])
        # plt.title('Fail Result')
        plt.xlabel('Step')
        plt.ylabel('Num')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=3)
        plt.savefig(path_to_save + 'CommTaskFailNum.png')
        plt.close()

        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        for idx, (fail_result, fail_num_list) in enumerate(steps_comp_fail_num.items()):
            plt.plot(x_indices, fail_num_list, label=fail_result, color=self.color_list[idx])
        # plt.title('Fail Result')
        plt.xlabel('Step')
        plt.ylabel('Num')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=3)
        plt.savefig(path_to_save + 'CompTaskFailNum.png')
        plt.close()

        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        for idx, (fail_result, fail_num_list) in enumerate(steps_sense_fail_num.items()):
            plt.plot(x_indices, fail_num_list, label=fail_result, color=self.color_list[idx])
        # plt.title('Fail Result')
        plt.xlabel('Step')
        plt.ylabel('Num')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=3)
        plt.savefig(path_to_save + 'SenseTaskFailNum.png')
        plt.close()

    def episodeInfoToFile(self, episode):
        folder_dir=self.base_path + f"final/"
        if not os.path.exists(folder_dir):
            os.makedirs(folder_dir)
        file_dir = folder_dir+"evaluation.json"

        data = {
            'episode': episode,

            'episode_finish_num': self.finish_num,
            'episode_finish_num_comm': self.finish_num_comm,
            'episode_finish_num_comp': self.finish_num_comp,
            'episode_finish_num_sensing': self.finish_num_sensing,

            'episode_fail_num': self.fail_num,
            'episode_fail_num_comm': self.fail_num_comm,
            'episode_fail_num_comp': self.fail_num_comp,
            'episode_fail_num_sensing': self.fail_num_sensing,

            'episode_terminated_num': self.terminated_num,
            'episode_terminated_num_comm': self.terminated_num_comm,
            'episode_terminated_num_comp': self.terminated_num_comp,
            'episode_terminated_num_sensing': self.terminated_num_sensing,

            'episode_completion_ratio': self.completion_ratio,
            'episode_completion_ratio_comm': self.completion_ratio_comm,
            'episode_completion_ratio_comp': self.completion_ratio_comp,
            'episode_completion_ratio_sensing': self.completion_ratio_sensing,

            'episode_avg_finish_time': self.avg_finish_time,
            'episode_avg_finish_time_comm': self.avg_finish_time_comm,
            'episode_avg_finish_time_comp': self.avg_finish_time_comp,
            'episode_avg_finish_time_sensing': self.avg_finish_time_sensing,

            'episode_avg_terminated_time': self.avg_terminated_time,
            'episode_avg_terminated_time_comm': self.avg_terminated_time_comm,
            'episode_avg_terminated_time_comp': self.avg_terminated_time_comp,
            'episode_avg_terminated_time_sensing': self.avg_terminated_time_sensing,

            'episode_comm_fail_num': self.comm_fail_num,
            'episode_comp_fail_num': self.comp_fail_num,
            'episode_sense_fail_num': self.sense_fail_num,
        }
        # 检查文件是否存在
        if os.path.exists(file_dir):
            # 如果文件存在，读取现有数据并追加新数据
            with open(file_dir, "r") as file:
                existing_data = json.load(file)
            existing_data.append(data)
        else:
            # 如果文件不存在，初始化为嵌套列表
            existing_data = [data]

        # 将数据写回文件
        with open(file_dir, "w") as file:
            json.dump(existing_data, file, indent=4)  # json格式缩进4个空格

    def drawEpisodeInfoByFile(self):
        # json文件路径
        file_path = self.base_path + f"final/evaluation.json"
        # 保存路径
        path_to_save = self.base_path + f'final/'

        if not os.path.exists(path_to_save):
            os.makedirs(path_to_save)

        # 读取 JSON 文件并累积数据
        with open(file_path, 'r') as file:
            data = json.load(file)
            episode_idxs = [entry["episode"] for entry in data]
            step_num = len(episode_idxs)

            episodes_finish_num = [entry['episode_finish_num'] for entry in data]
            episodes_finish_num_comm = [entry['episode_finish_num_comm'] for entry in data]
            episodes_finish_num_comp = [entry['episode_finish_num_comp'] for entry in data]
            episodes_finish_num_sensing = [entry['episode_finish_num_sensing'] for entry in data]

            episodes_fail_num = [entry['episode_fail_num'] for entry in data]
            episodes_fail_num_comm = [entry['episode_fail_num_comm'] for entry in data]
            episodes_fail_num_comp = [entry['episode_fail_num_comp'] for entry in data]
            episodes_fail_num_sensing = [entry['episode_fail_num_sensing'] for entry in data]

            episodes_terminated_num = [entry['episode_terminated_num'] for entry in data]
            episodes_terminated_num_comm = [entry['episode_terminated_num_comm'] for entry in data]
            episodes_terminated_num_comp = [entry['episode_terminated_num_comp'] for entry in data]
            episodes_terminated_num_sensing = [entry['episode_terminated_num_sensing'] for entry in data]

            episodes_completion_ratio = [entry['episode_completion_ratio'] for entry in data]
            episodes_completion_ratio_comm = [entry['episode_completion_ratio_comm'] for entry in data]
            episodes_completion_ratio_comp = [entry['episode_completion_ratio_comp'] for entry in data]
            episodes_completion_ratio_sensing = [entry['episode_completion_ratio_sensing'] for entry in data]

            episodes_avg_finish_time = [entry['episode_avg_finish_time'] for entry in data]
            episodes_avg_finish_time_comm = [entry['episode_avg_finish_time_comm'] for entry in data]
            episodes_avg_finish_time_comp = [entry['episode_avg_finish_time_comp'] for entry in data]
            episodes_avg_finish_time_sensing = [entry['episode_avg_finish_time_sensing'] for entry in data]

            episodes_avg_terminated_time = [entry['episode_avg_terminated_time'] for entry in data]
            episodes_avg_terminated_time_comm = [entry['episode_avg_terminated_time_comm'] for entry in data]
            episodes_avg_terminated_time_comp = [entry['episode_avg_terminated_time_comp'] for entry in data]
            episodes_avg_terminated_time_sensing = [entry['episode_avg_terminated_time_sensing'] for entry in data]

            episodes_comm_fail_num={result_enum.name:[] for result_enum in CommResult}
            episodes_comp_fail_num = {result_enum.name: [] for result_enum in CompResult}
            episodes_sense_fail_num = {result_enum.name: [] for result_enum in SenseResult}
            for entry in data:
                episode_comm_fail_num=entry['episode_comm_fail_num']
                for fail_result,fail_num in episode_comm_fail_num.items():
                    episodes_comm_fail_num[fail_result].append(fail_num)
                episode_comp_fail_num=entry['episode_comp_fail_num']
                for fail_result,fail_num in episode_comp_fail_num.items():
                    episodes_comp_fail_num[fail_result].append(fail_num)
                episode_sense_fail_num=entry['episode_sense_fail_num']
                for fail_result,fail_num in episode_sense_fail_num.items():
                    episodes_sense_fail_num[fail_result].append(fail_num)

        # 1.过程监控
        # 1.1 成功任务
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, episodes_finish_num, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, episodes_finish_num_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, episodes_finish_num_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, episodes_finish_num_sensing, label='sens', color=self.color['purple'])
        # plt.title('Episode Finished Tasks')
        plt.xlabel('Episode')
        plt.ylabel('The Number of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'EpisodeFinishedTasks.png')
        plt.close()

        # 1.2 失败任务
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, episodes_fail_num, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, episodes_fail_num_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, episodes_fail_num_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, episodes_fail_num_sensing, label='sens', color=self.color['purple'])
        # plt.title('Episode Failed Tasks')
        plt.xlabel('Episode')
        plt.ylabel('The Number of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'EpisodeFailedTasks.png')
        plt.close()

        # 1.3 已终止任务
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, episodes_terminated_num, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, episodes_terminated_num_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, episodes_terminated_num_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, episodes_terminated_num_sensing, label='sens', color=self.color['purple'])
        # plt.title('Episode Terminated Tasks')
        plt.xlabel('Episode')
        plt.ylabel('The Number of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'EpisodeTerminatedTasks.png')
        plt.close()

        # 2. 完成率
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, episodes_completion_ratio, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, episodes_completion_ratio_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, episodes_completion_ratio_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, episodes_completion_ratio_sensing, label='sens', color=self.color['purple'])
        # plt.title('Episode Completion Ratio')
        plt.xlabel('Episode')
        plt.ylabel('Completion Ratio of Tasks')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'EpisodeCompletionRatio.png')
        plt.close()

        # 3. 完成时间
        # 3.1 成功任务平均时延
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, episodes_avg_finish_time, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, episodes_avg_finish_time_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, episodes_avg_finish_time_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, episodes_avg_finish_time_sensing, label='sens', color=self.color['purple'])
        # plt.title('Avg Spend Time')
        plt.xlabel('Episode')
        plt.ylabel('Time')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'FinishTasksAvgSpendTime.png')
        plt.close()

        # 3.2 终止任务平均时延
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        plt.plot(x_indices, episodes_avg_terminated_time, label='sum', color=self.color['yellow'])
        plt.plot(x_indices, episodes_avg_terminated_time_comm, label='comm', color=self.color['green'])
        plt.plot(x_indices, episodes_avg_terminated_time_comp, label='comp', color=self.color['blue'])
        plt.plot(x_indices, episodes_avg_terminated_time_sensing, label='sens', color=self.color['purple'])
        # plt.title('Avg Spend Time')
        plt.xlabel('Episode')
        plt.ylabel('Time')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1),fancybox=True, shadow=False, ncol=4)
        plt.savefig(path_to_save + 'TerminatedTasksAvgSpendTime.png')
        plt.close()

        # 4. 失败原因
        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        for idx, (fail_result, fail_num_list) in enumerate(episodes_comm_fail_num.items()):
            plt.plot(x_indices, fail_num_list, label=fail_result, color=self.color_list[idx])
        # plt.title('Fail Result')
        plt.xlabel('Step')
        plt.ylabel('Num')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1), fancybox=True, shadow=False, ncol=3)
        plt.savefig(path_to_save + 'CommTaskFailNum.png')
        plt.close()

        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        for idx, (fail_result, fail_num_list) in enumerate(episodes_comp_fail_num.items()):
            plt.plot(x_indices, fail_num_list, label=fail_result, color=self.color_list[idx])
        # plt.title('Fail Result')
        plt.xlabel('Step')
        plt.ylabel('Num')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1), fancybox=True, shadow=False, ncol=3)
        plt.savefig(path_to_save + 'CompTaskFailNum.png')
        plt.close()

        x_indices = list(range(1, step_num + 1))
        plt.figure(clear=True)
        for idx, (fail_result, fail_num_list) in enumerate(episodes_sense_fail_num.items()):
            plt.plot(x_indices, fail_num_list, label=fail_result, color=self.color_list[idx])
        # plt.title('Fail Result')
        plt.xlabel('Step')
        plt.ylabel('Num')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, alpha=0.7)
        plt.legend(loc='lower center', bbox_to_anchor=(0.5, 1), fancybox=True, shadow=False, ncol=3)
        plt.savefig(path_to_save + 'SenseTaskFailNum.png')
        plt.close()

    def resetStepEvaluation(self):
        self.initOrResetStepIndicators()
        self.initOrResetStepRecords()

    def resetAllEvaluation(self):
        self.initOrResetStepIndicators()
        self.initOrResetStepRecords()
        self.initOrResetEpisodeRecords()

    def getCompletionRatio(self):
        return self.completion_ratio
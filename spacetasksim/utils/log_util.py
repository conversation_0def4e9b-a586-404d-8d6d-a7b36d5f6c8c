from datetime import datetime


class CustomLogger():
     def __init__(self):
         pass

     def debug(self, message):
         time_str=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
         log=f"{time_str} [DEBUG] {message}"
         return log

     def info(self, message):
         time_str=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
         log=f"{time_str} [INFO] {message}"
         return log

     def warning(self, message):
         time_str=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
         log=f"{time_str} [WARNING] {message}"
         return log

     def error(self, message):
         time_str=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
         log=f"{time_str} [ERROR] {message}"
         return log

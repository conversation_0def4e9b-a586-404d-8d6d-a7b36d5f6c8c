import argparse
import math
import random

import numpy as np
import torch

from spacetasksim.enum.function_enum import FunctionEnum
from spacetasksim.enum.node_type_enum import NodeTypeEnum
from spacetasksim.enum.step_type_enum import StepTypeEnum
from spacetasksim.enum.task_type_enum import TaskTypeEnum
from spacetasksim.sche_strategy.base_strategy import BaseStrategyModule
from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from spacetasksim.utils import geo_util, math_util
from spacetasksim.algorithm.PTMAPPO_Reptile.MAPPO_env import MAPPO_Env

# device = "cuda" if torch.cuda.is_available() else "cpu"
cuda_num = torch.cuda.device_count()
base_dir="/home/<USER>/data/project/spacetasksim"


if cuda_num > 0:
    cuda_list = list(range(cuda_num))
    device = f"cuda:{cuda_list[cuda_num - 2]}"
else:
    device = "cpu"

print('device: ',device)
print('torch_version: ',torch.__version__)
print('cuda_num: ',cuda_num)


def parseMAPPOTrainArgs():
    parser = argparse.ArgumentParser(description='MAPPO train arguments')
    parser.add_argument('--learning_rate', type=float, default=1e-4)  # 学习率
    parser.add_argument('--var', type=float, default=0.9)  # 探索系数
    parser.add_argument('--var_end', type=float, default=0.01)  # 最低探索系数
    parser.add_argument('--var_dec', type=float, default=0.9)  # 探索系数衰减率
    parser.add_argument('--gamma', type=float, default=0.9)  # 折扣因子
    parser.add_argument('--gae_lambda', type=float, default=0.98)  # GAE调整方差与偏差的系数，即GAE折扣因子，0.96-0.99
    parser.add_argument('--epsilon', type=float, default=0.2)  # 对估计优势的函数进行裁剪
    parser.add_argument('--epoch', type=int, default=10)  # episode数据训练轮数
    parser.add_argument('--tau_comm', type=float, default=1.0)  # 策略更新平滑因子
    parser.add_argument('--tau_comp', type=float, default=1.0)  # 策略更新平滑因子
    parser.add_argument('--tau_sens', type=float, default=1.0)  # 策略更新平滑因子
    parser.add_argument('--device', type=str, default=device)  # 训练设备(GPU/CPU)
    parser.add_argument('--model_base_dir', type=str, default=f"../../models/PTMAPPO_Reptile2")  # 模型文件路径
    args, unknown = parser.parse_known_args()
    return args


def parseMAPPODimArgs():
    # 1.task_state
    # [time_urgency_ratio,size_ratio,encoded_x,encoded_y,encoded_z]
    comm_task_state=5
    # [time_urgency_ratio,size_ratio]
    comp_task_state=2
    # [time_urgency_ratio,accuracy_require,sensing_time_ratio,encoded_x,encoded_y,encoded_z]
    sensing_task_state=6

    # 2.node_state
    # [x, y, z,encode_distance]
    dim_basic_state = 4
    # [idle_ratio,encode_s2s_power_db,encode_s2s_bandwidth]
    dim_comm_state = 3
    # [idle_cpu_ratio, encode_avg_cpu_frequency]
    dim_comp_state = 2
    # [idle_camera_ratio,avg_camera_accuracy,encode_elevation]
    dim_sensing_state = 3
    # [free_space_ratio]
    dim_storage_state = 1
    # [time_available,available]
    dim_available = 2

    dim_one_hot=len(TaskTypeEnum)
    dim_obs_task_state=comm_task_state+comp_task_state+sensing_task_state
    dim_obs_node_state=dim_basic_state+dim_comm_state+dim_comp_state+dim_sensing_state+dim_storage_state+dim_available
    dim_obs_node=30
    dim_obs=dim_one_hot+dim_obs_task_state+dim_obs_node_state*dim_obs_node

    dim_action=30
    n_agents=311

    parser = argparse.ArgumentParser(description='MAPPO dimension arguments')
    # 分解维度
    parser.add_argument('--dim_basic_state', type=int, default=dim_basic_state)
    parser.add_argument('--dim_comm_state', type=int, default=dim_comm_state)
    parser.add_argument('--dim_comp_state', type=int, default=dim_comp_state)
    parser.add_argument('--dim_sensing_state', type=int, default=dim_sensing_state)
    parser.add_argument('--dim_storage_state', type=int, default=dim_storage_state)
    parser.add_argument('--dim_available', type=int, default=dim_available)

    parser.add_argument('--dim_comm_task', type=int, default=comm_task_state)
    parser.add_argument('--dim_comp_task', type=int, default=comp_task_state)
    parser.add_argument('--dim_sensing_task', type=int, default=sensing_task_state)

    parser.add_argument('--dim_obs_task_state', type=int, default=dim_obs_task_state)  # Dimension of observation state to a single task
    parser.add_argument('--dim_obs_node_state', type=int, default=dim_obs_node_state)  # Dimension of observation state to a single node
    parser.add_argument('--dim_obs_node', type=int, default=dim_obs_node)  # Dimension of observation node

    # 算法实际使用的维度
    parser.add_argument('--dim_obs', type=int, default=dim_obs)  # Dimension of observation
    parser.add_argument('--dim_action', type=int, default=dim_action)  # Dimension of action
    parser.add_argument('--n_agents', type=int, default=n_agents)  # Number of agents
    parser.add_argument('--dim_hiddens', type=float, default=512)  # Dimension of hidden layer
    args, unknown = parser.parse_known_args()
    return args

class MAPPOStrategyModule(BaseStrategyModule):
    class ReplayBuffer():
        def __init__(self):
            # 创建一个字典，长度不限
            self.buffer = {task_type_enum:{} for task_type_enum in TaskTypeEnum}

        def __expToFlattenArray(self, exp):
            combined_states_id = exp['combined_states_id']
            action_mask = exp['action_mask']
            action = exp['action']
            reward = exp['reward']
            next_combined_states_id = exp['next_combined_states_id']
            agent_mask = exp['agent_mask']
            return combined_states_id, action_mask, action, reward, next_combined_states_id,agent_mask

        def add(self,node_id, exp_id,task_type, combined_states_id,action_mask, action, reward=None, next_combined_states_id=None, agent_mask=None):
            if node_id not in self.buffer[task_type]:
                self.buffer[task_type][node_id] = {}
            self.buffer[task_type][node_id][exp_id]={'combined_states_id': combined_states_id, 'action_mask': np.array(action_mask),
                                          'action': np.array(action) if action is not None else None,
                                          'reward':np.array(reward) if reward is not None else None,
                                          'next_combined_states_id': next_combined_states_id if next_combined_states_id is not None else None,
                                          'agent_mask': np.array(agent_mask) if agent_mask is not None else None
                                          }

        def setNextState(self,node_id, exp_id, task_type, next_combined_states_id):
            node_exps=self.buffer[task_type].get(node_id,{})
            if exp_id not in node_exps:
                return
            self.buffer[task_type][node_id][exp_id]['next_combined_states_id'] = np.array(next_combined_states_id)

        def completeExperience(self,node_id, exp_id,task_type, reward):
            node_exps=self.buffer[task_type].get(node_id,{})
            if exp_id not in node_exps:
                return
            self.buffer[task_type][node_id][exp_id]['reward'] = np.array(reward)

        def get(self):
            return self.buffer

        def clear(self):
            self.buffer={task_type_enum:{} for task_type_enum in TaskTypeEnum}

    def __init__(self):
        super().__init__()
        self.module_tag = "MAPPO"
        print('algorithm: ', self.module_tag)

    def initialize(self, env: SpaceTaskSimEnv,last_episode=0,final= False,eval=False):
        super().initialize(env)
        self.eval=eval

        self.MAPPO_train_args = parseMAPPOTrainArgs()
        self.MAPPO_dim_args = parseMAPPODimArgs()

        if eval:
            self.MAPPO_train_args.var=0
        print(self.MAPPO_train_args)
        self.MAPPO_env = MAPPO_Env(self.MAPPO_dim_args, self.MAPPO_train_args)
        if eval or (not eval and last_episode > 0):
            self.MAPPO_env.loadModel(last_episode,final,eval)

        self.exp_buffer=self.ReplayBuffer()
        self.last_offload_task_ids={task_type_enum:{} for task_type_enum in TaskTypeEnum}
        self.used_node_ids=[]

        self.agent_node_ids=self.env.init_cn_transit_sat_ids
        self.agent_idx_dict={node_id:idx for idx,node_id in enumerate(self.agent_node_ids)}
        print('agent_num:', len(self.agent_node_ids))
        self.task_node_dict={}
        self.n_agents=self.MAPPO_dim_args.n_agents
        self.dim_obs=self.MAPPO_dim_args.dim_obs

        self.combined_obs_space={task_type_enum:np.zeros((self.n_agents,self.dim_obs)) for task_type_enum in TaskTypeEnum}
        self.combined_obs_id={task_type_enum:0 for task_type_enum in TaskTypeEnum}

        self.sum_offload=0
        self.sum_succ_task_num=0
        self.sum_succ_task_reward=0
        self.sum_terminated_task_num=0
        self.sum_terminated_task_reward=0

        self.suc_comm_rewards=[]
        self.suc_comp_rewards=[]
        self.suc_sens_rewards=[]


    def reset(self):
        self.exp_buffer.clear()
        self.last_offload_task_ids={task_type_enum:{} for task_type_enum in TaskTypeEnum}
        self.used_node_ids=[]
        self.task_node_dict.clear()

        self.combined_obs_space = {task_type_enum: np.zeros((self.n_agents, self.dim_obs)) for task_type_enum in
                                   TaskTypeEnum}

        self.sum_offload = 0
        self.sum_succ_task_num=0
        self.sum_succ_task_reward=0
        self.sum_terminated_task_num=0
        self.sum_terminated_task_reward=0

        # self.suc_comm_rewards.clear()
        # self.suc_comp_rewards.clear()
        # self.suc_sens_rewards.clear()

    def train(self):
        self.MAPPO_env.train()

    def saveModel(self, episode,final,succ_ratio):
        self.MAPPO_env.saveModel(episode,final,succ_ratio)


    def _encodeNodeStates(self,dim_action,dim_obs_node_state,obs_position,node_ids,basic_states,comm_states,comp_states,sensing_states,storage_states,
                     task_type=None,accuracy_require=0,sensing_time=0,space_require=0,target_position=None,gen_id=None,tx_node_id=None,rx_node_id=None,ddl=0):
        R_E=geo_util.R_E
        elevation_mask_angle=self.env.config['config_resource']['config_sensing']['elevation_mask_angle']
        cpb=self.env.resource_manager.computation_manager._cycle_per_byte
        states=[]
        masks=[]
        time_masks=[]
        for node_id in node_ids:
            # encode basic state
            # input:
            # [node_id,node_type,lat,lon,alt_km,x,y,z]
            # output:
            # [encoded_x,encoded_y,encoded_z,encode_distance]
            # 0-3
            basic_state=basic_states[node_id]
            node_position = (basic_state[2], basic_state[3], basic_state[4])
            encoded_x=min((1+basic_state[5]/(R_E+1000))/2,1)
            encoded_y=min((1+basic_state[6]/(R_E+1000))/2,1)
            encoded_z=min((1+basic_state[7]/(R_E+1000))/2,1)
            encode_distance=geo_util.distance_between_points(obs_position,node_position)/3000
            encoded_basic_state=[encoded_x,encoded_y,encoded_z,encode_distance]

            # encode comm state
            # input:
            # [s2s_t_idle_beam,s2s_t_busy_beam,s2s_r_idle_beam,s2s_r_busy_beam,
            #  s2g_t_idle_beam,s2g_t_busy_beam,g2s_r_idle_beam,g2s_r_busy_beam,
            #  s2s_power_db,s2g_power_db,s2s_bandwidth,s2g_bandwidth]
            # output:
            # [idle_ratio,encode_s2s_power_db,encode_s2s_bandwidth]
            # 4-6
            s2s_power_db=self.env.config['config_resource']['config_communication']['S2S']['power_dbw']
            s2s_bandwidth=self.env.config['config_resource']['config_communication']['S2S']['bandwidth']
            comm_state=comm_states[node_id]
            s2s_t_beam_sum=comm_state[0]+comm_state[1]
            s2s_t_idle_ratio=comm_state[0]/s2s_t_beam_sum if s2s_t_beam_sum>0 else 0
            s2s_r_beam_sum=comm_state[2]+comm_state[3]
            s2s_r_idle_ratio=comm_state[2]/s2s_r_beam_sum if s2s_r_beam_sum>0 else 0
            encode_s2s_power_db=(comm_state[8]-s2s_power_db[0])/(s2s_power_db[1]-s2s_power_db[0])
            encode_s2s_bandwidth=(comm_state[10]-s2s_bandwidth[0])/(s2s_bandwidth[1]-s2s_bandwidth[0])
            idle_ratio=min(s2s_t_idle_ratio,s2s_r_idle_ratio)
            encoded_comm_state=[idle_ratio,encode_s2s_power_db,encode_s2s_bandwidth]

            # encode comp state
            # input:
            # [idle_cpu_num,busy_cpu_num,avg_cpu_frequency]
            # output:
            # [idle_cpu_ratio,encode_avg_cpu_frequency]
            # 7-8
            sat_cpu_frequency=self.env.config['config_resource']['config_computation']['satellite']['frequency']
            comp_state=comp_states[node_id]
            sum_cpu_num=comp_state[0]+comp_state[1]
            avg_cpu_frequency=comp_state[2]
            idle_cpu_ratio=comp_state[0]/sum_cpu_num if sum_cpu_num>0 else 0
            encode_avg_cpu_frequency=(avg_cpu_frequency-sat_cpu_frequency[0])/(sat_cpu_frequency[1]-sat_cpu_frequency[0])
            encoded_comp_state=[idle_cpu_ratio,encode_avg_cpu_frequency]

            # encode sensing state
            # input:
            # [idle_camera_num,busy_camera_num,avg_camera_accuracy]
            # output:
            # [idle_camera_ratio,avg_camera_accuracy,encode_elevation]
            # 9-11
            sensing_state=sensing_states[node_id]
            sum_camera_num=sensing_state[0]+sensing_state[1]
            idle_camera_ratio=sensing_state[0]/sum_camera_num if sum_camera_num>0 else 0
            elevation=geo_util.calculate_elevation(target_position,node_position) if target_position is not None else 0
            encode_elevation = math.sin(math.radians(elevation))
            avg_camera_accuracy=sensing_state[2]
            encoded_sensing_state=[idle_camera_ratio,avg_camera_accuracy,encode_elevation]


            # encode storage state
            # input:
            # [free_space,occupied_space]
            # output:
            # [free_space_ratio]
            # 12
            storage_state=storage_states[node_id]
            sum_space=storage_state[0]+storage_state[1]
            free_space_ratio=storage_state[0]/sum_space if sum_space>0 else 0
            encoded_storage_state=[free_space_ratio]

            space_available=storage_state[0]>space_require
            trans_r_available=comm_state[2]>0 or node_id==tx_node_id
            if task_type==TaskTypeEnum.COMMUNICATION:
                trans_t_available=comm_state[0]>0
                tx_position=self.env.node_manager.getNodeById(tx_node_id).getPosition()
                rx_position=self.env.node_manager.getNodeById(rx_node_id).getPosition()
                rate1 = self.env.resource_manager.communication_manager.estimateDataRate(tx_position, node_position,tx_node_id, node_id)
                rate2 = self.env.resource_manager.communication_manager.estimateDataRate(node_position, rx_position,node_id, rx_node_id)
                trans_time1 = space_require / rate1
                trans_time2 = space_require / rate2
                time_available=ddl - self.env.simulation_time >= trans_time1+trans_time2
                available=space_available & trans_r_available & trans_t_available & (node_id!=tx_node_id) & (node_id!=rx_node_id) & time_available
            elif task_type==TaskTypeEnum.COMPUTATION:
                cpu_available=comp_state[0]>0
                comm_rate = self.env.resource_manager.communication_manager.estimateDataRate(obs_position,node_position,gen_id, node_id)
                comm_time = space_require / comm_rate
                comp_time = (space_require * cpb) / (avg_cpu_frequency * 1e3)
                exe_time = comm_time + comp_time
                # left_time = ddl - self.env.simulation_time
                # comp_size_max = left_time * avg_cpu_frequency *1e3/cpb
                time_available=ddl - self.env.simulation_time >= exe_time
                available=space_available & trans_r_available & cpu_available
            elif task_type==TaskTypeEnum.SENSING:
                camera_available=sensing_state[0]>0
                view_available=elevation>elevation_mask_angle
                actual_sensing_time=(accuracy_require / avg_camera_accuracy) * sensing_time
                time_available=ddl - self.env.simulation_time>=actual_sensing_time
                available=space_available  & camera_available & view_available
            else:
                available=False
                time_available=False

            state=encoded_basic_state+encoded_comm_state+encoded_comp_state+encoded_sensing_state+encoded_storage_state+[time_available,available]
            states.append((node_id, state))  # 绑定 (node_id, state)
            time_masks.append(time_available)

        # 排序时使用 state 作为 key
        if task_type == TaskTypeEnum.COMMUNICATION:
            sorted_pairs = sorted(states, key=lambda x: (not x[1][-1], -x[1][6], -x[1][4])) # encode_s2s_bandwidth,encode_idle_ratio
        elif task_type == TaskTypeEnum.COMPUTATION:
            sorted_pairs = sorted(states, key=lambda x: (not x[1][-1], -x[1][8], -x[1][6])) # encode_cpu_frequency,encode_s2s_bandwidth
        elif task_type == TaskTypeEnum.SENSING:
            sorted_pairs = sorted(states, key=lambda x: (not x[1][-1], -x[1][10], -x[1][11], -x[1][9])) # avg_camera_accuracy,encode_elevation,idle_camera_ratio
        else:
            sorted_pairs=states

        # 解绑，得到排序后的 states 和 node_ids
        sorted_node_states = [state for (node_id, state) in sorted_pairs]
        sorted_node_ids = [node_id for (node_id, state) in sorted_pairs]

        # 生成 masks
        masks = [state[-1] for state in sorted_node_states]

        # 如果 node_ids 不足 dim_action，用 0 补齐
        if len(sorted_node_ids) < dim_action:
            pad_length = dim_action - len(sorted_node_ids)
            sorted_node_ids += [None] * pad_length  # 补 0
            masks += [False] * pad_length  # 掩码对应位置补 False
            zero_state = [0] * dim_obs_node_state  # n 维全零向量
            sorted_node_states += [zero_state] * pad_length  # 补齐状态向量

        return sorted_node_states, masks, sorted_node_ids,time_masks  # 返回排序后的 node_ids

    def _encodeCommTaskState(self,current_time=0,trans_size=0,target_xyz=(0,0,0),ddl=0,ttl=0,empty=False):
        if empty:
            return [0 for _ in range(self.MAPPO_dim_args.dim_comm_task)]
        R_E=geo_util.R_E
        task_TTL_range=self.env.config['config_task']['communication']['task_TTL_range']
        left_time=ddl-current_time
        # time_urgency_ratio=(ttl-task_TTL_range[0])/(task_TTL_range[1]-task_TTL_range[0])
        time_urgency_ratio=(ddl-current_time)/task_TTL_range[1]
        trans_size_range=self.env.config['config_task']['communication']['task_size']
        size_ratio=(trans_size-trans_size_range[0])/(trans_size_range[1]-trans_size_range[0])
        encoded_x = min((1+target_xyz[0] / (R_E + 1000))/2, 1)
        encoded_y = min((1+target_xyz[1] / (R_E + 1000))/2, 1)
        encoded_z = min((1+target_xyz[2] / (R_E + 1000))/2, 1)
        return [time_urgency_ratio,size_ratio,encoded_x,encoded_y,encoded_z]

    def _encodeCompTaskState(self,current_time=0,compute_size=0,ddl=0,ttl=0,empty=False):
        if empty:
            return [0 for _ in range(self.MAPPO_dim_args.dim_comp_task)]
        task_TTL_range=self.env.config['config_task']['computation']['task_TTL_range']
        # time_urgency_ratio=(ttl-task_TTL_range[0])/(task_TTL_range[1]-task_TTL_range[0])
        time_urgency_ratio = (ddl - current_time) / task_TTL_range[1]
        compute_size_range=self.env.config['config_task']['computation']['task_size']
        size_ratio=(compute_size-compute_size_range[0])/(compute_size_range[1]-compute_size_range[0])
        return [time_urgency_ratio,size_ratio]

    def _encodeSensingTaskState(self,current_time=0,accuracy_require=0,sensing_time_require=0,target_xyz=(0,0,0),ddl=0,ttl=0,empty=False):
        if empty:
            return [0 for _ in range(self.MAPPO_dim_args.dim_sensing_task)]
        R_E=geo_util.R_E
        task_TTL_range=self.env.config['config_task']['sensing']['task_TTL_range']
        sensing_time_range=self.env.config['config_task']['sensing']['sensing_time']
        # time_urgency_ratio=(ttl-task_TTL_range[0])/(task_TTL_range[1]-task_TTL_range[0])
        time_urgency_ratio = (ddl - current_time) / task_TTL_range[1]
        sensing_time_ratio=(sensing_time_require-sensing_time_range[0])/(sensing_time_range[1]-sensing_time_range[0])
        encoded_x = min((1+target_xyz[0] / (R_E + 1000))/2, 1)
        encoded_y = min((1+target_xyz[1] / (R_E + 1000))/2, 1)
        encoded_z = min((1+target_xyz[2] / (R_E + 1000))/2, 1)
        return [time_urgency_ratio,accuracy_require,sensing_time_ratio,encoded_x,encoded_y,encoded_z]

    def scheduleStep(self):
        time_accuracy_digit=self.env.time_accuracy_digit
        schedule_interval=self.env.schedule_interval
        simulation_time=self.env.simulation_time
        if not (math_util.float_mod(simulation_time,schedule_interval,time_accuracy_digit)< 1e-9):
            return

        self.comm_resources_usage_cache.clear()
        self.comp_resources_usage_cache.clear()
        self.sensing_resources_usage_cache.clear()
        self.storage_resources_usage_cache.clear()
        self.used_node_ids=[]
        for task_type in TaskTypeEnum:
            self.combined_obs_space[task_type][:,:]=0

        self.scheduleOffloading()
        self.scheduleComputation()
        self.scheduleSensing()

    def scheduleOffloading(self):
        simulation_time = self.env.simulation_time
        dim_action=self.MAPPO_dim_args.dim_action
        dim_obs_node_state=self.MAPPO_dim_args.dim_obs_node_state
        to_sched_comm_steps_on_comm_task = self.taskScheduler.getToScheduledSteps(self.env, StepTypeEnum.COMMUNICATION,
                                                                     TaskTypeEnum.COMMUNICATION)
        to_sched_comm_steps_on_comp_task=self.taskScheduler.getToScheduledSteps(self.env, StepTypeEnum.COMMUNICATION,
                                                                     TaskTypeEnum.COMPUTATION)
        to_sched_comm_steps_on_sensing_task=self.taskScheduler.getToScheduledSteps(self.env, StepTypeEnum.COMMUNICATION,
                                                                     TaskTypeEnum.SENSING)
        to_sched_steps={}
        to_sched_steps.update(to_sched_comm_steps_on_sensing_task)
        to_sched_steps.update(to_sched_comm_steps_on_comp_task)
        to_sched_steps.update(to_sched_comm_steps_on_comm_task)

        # 将字典的 items() 转换为列表并打乱顺序
        to_sched_steps = list(to_sched_steps.items())
        # random.shuffle(to_sched_steps)
        offloaded_steps={}
        print('to_offload_steps:', len(to_sched_steps))
        i = 0
        for step_id, step in to_sched_steps:
            task = self.taskScheduler.getTaskById(self.env, step.task_id)
            ddl=task.deadline
            task_id=task.task_id
            task_type=task.task_type
            last_step = task.getLastStepByStepId(step_id)
            if last_step is None:
                current_node_id = task.init_node_id
            else:
                current_node_id = last_step.current_node_id

            if current_node_id in self.used_node_ids:
                continue


            storage_usage_cache = self.storage_resources_usage_cache.get(current_node_id, {})
            current_node_position = self.nodeScheduler.getNodePositionById(self.env, current_node_id)

            if task_type==TaskTypeEnum.COMMUNICATION:
                target_position=None
                accuracy_require=0
                sensing_time_require=0
                required_trans_size = task.required_trans_size
                storage_available = self.resourceScheduler.checkStorageResourcesAvailable(self.env, current_node_id,
                                                                                          required_trans_size,
                                                                                          storage_usage_cache)
                if not storage_available:
                    continue
                tx_node_id=current_node_id
                rx_node_id = task.endpoint_node_id
                rx_storage_usage_cache = self.storage_resources_usage_cache.get(rx_node_id, {})
                rx_storage_available = self.resourceScheduler.checkStorageResourcesAvailable(self.env, rx_node_id,
                                                                                             required_trans_size,
                                                                                             rx_storage_usage_cache)
                tx_comm_cache=self.comm_resources_usage_cache.get(current_node_id, {})
                rx_comm_cache=self.comm_resources_usage_cache.get(rx_node_id, {})
                comm_available=self.resourceScheduler.checkCommResourcesAvailable(self.env, current_node_id, rx_node_id,
                                                                                  tx_comm_cache,rx_comm_cache)
                if not (rx_storage_available and comm_available):
                    continue
                rx_node_type=self.nodeScheduler.getNodeTypeById(self.env,rx_node_id)
                rx_lla_position = self.nodeScheduler.getNodePositionById(self.env, rx_node_id)
                target_xyz=geo_util.lla_to_ecef(*rx_lla_position)
                comm_task_state=self._encodeCommTaskState(simulation_time,required_trans_size,target_xyz,task.deadline,task.ttl)
                comp_task_state=self._encodeCompTaskState(empty=True)
                sensing_task_state=self._encodeSensingTaskState(empty=True)
                tx_position = self.nodeScheduler.getNodePositionById(self.env, tx_node_id)
                tx_view_sat_ids = self.viewScheduler.getVisibleSatIds(self.env, tx_position, D=3000,
                                                                      function_type=FunctionEnum.RELAY)
                rx_position = self.nodeScheduler.getNodePositionById(self.env, rx_node_id)
                rx_view_sat_ids = self.viewScheduler.getVisibleSatIds(self.env, rx_position, D=3000,
                                                                      function_type=FunctionEnum.RELAY)
                intersection_sat_ids = list(set(tx_view_sat_ids) & set(rx_view_sat_ids))
                obs_sat_ids = self.viewScheduler.getMiddleSats(self.env, tx_node_id, rx_node_id,intersection_sat_ids, k=dim_action)
            elif task.task_type==TaskTypeEnum.COMPUTATION:
                tx_node_id=None
                rx_node_id =None
                target_position=None
                accuracy_require=0
                sensing_time_require=0
                required_trans_size = task.required_compute_size
                storage_available = self.resourceScheduler.checkStorageResourcesAvailable(self.env, current_node_id,
                                                                                          required_trans_size,
                                                                                          storage_usage_cache)
                comm_cache=self.comm_resources_usage_cache.get(current_node_id, {})
                comm_available=self.resourceScheduler.checkCommTxResourcesAvailable(self.env, current_node_id,comm_cache,NodeTypeEnum.SAT)
                if not (comm_available and storage_available):
                    continue
                required_comp_size=required_trans_size
                comm_task_state=self._encodeCommTaskState(empty=True)
                comp_task_state=self._encodeCompTaskState(simulation_time,required_comp_size,task.deadline,task.ttl)
                sensing_task_state=self._encodeSensingTaskState(empty=True)
                obs_sat_ids=self.viewScheduler.getVisibleSatIds(self.env, current_node_position, K=dim_action,D=3000,
                                                                function_type=FunctionEnum.COMPUTATION)
            elif task.task_type==TaskTypeEnum.SENSING:
                tx_node_id = None
                rx_node_id = None
                # comm_cache=self.comm_resources_usage_cache.get(current_node_id, {})
                # comm_available=self.resourceScheduler.checkCommTxResourcesAvailable(self.env, current_node_id,comm_cache,NodeTypeEnum.SAT)
                # if not comm_available:
                #     continue
                required_trans_size=0
                target_position=task.sensing_position
                target_xyz=geo_util.lla_to_ecef(*target_position)
                accuracy_require=task.required_sensing_accuracy
                sensing_time_require=task.required_sensing_time
                comm_task_state=self._encodeCommTaskState(empty=True)
                comp_task_state=self._encodeCompTaskState(empty=True)
                sensing_task_state=self._encodeSensingTaskState(simulation_time,accuracy_require,sensing_time_require,target_xyz,task.deadline,task.ttl)
                obs_sat_ids=self.viewScheduler.getVisibleSatIds(self.env, current_node_position, K=dim_action,D=3000,
                                                                function_type=FunctionEnum.SENSING)
            else:
                raise TypeError('Invalid task type')

            node_basic_states=self.algorithmScheduler.getSatBasicStates(self.env,obs_sat_ids)
            node_comm_states=self.algorithmScheduler.getSatCommStates(self.env,obs_sat_ids,self.comm_resources_usage_cache)
            node_comp_states=self.algorithmScheduler.getSatCompStates(self.env,obs_sat_ids,self.comp_resources_usage_cache)
            node_sensing_states=self.algorithmScheduler.getSatSensingStates(self.env,obs_sat_ids,self.sensing_resources_usage_cache)
            node_storage_states=self.algorithmScheduler.getSatStorageStates(self.env,obs_sat_ids,self.storage_resources_usage_cache)
            encoded_node_states,masks,sorted_node_ids,time_masks=self._encodeNodeStates(dim_action,dim_obs_node_state,current_node_position,obs_sat_ids,node_basic_states,node_comm_states,node_comp_states,node_sensing_states,node_storage_states,
                                                                             task_type=task.task_type,accuracy_require=accuracy_require,sensing_time=sensing_time_require,space_require=required_trans_size,
                                                                             target_position=target_position,gen_id=current_node_id,tx_node_id=tx_node_id,rx_node_id=rx_node_id,ddl=ddl)
            useful_actions=sum(time_masks)
            # if useful_actions==0:
            #     step._fail(self.env.simulation_time)
            #     continue
            active_actions=sum(masks)
            if active_actions==0 or useful_actions==0:
                continue
            encoded_task_state=comm_task_state+comp_task_state+sensing_task_state
            encoded_one_hot=[task_type==TaskTypeEnum.COMMUNICATION,task_type==TaskTypeEnum.COMPUTATION,task_type==TaskTypeEnum.SENSING]
            encoded_state = np.concatenate([
                np.array(encoded_one_hot).flatten(),
                np.array(encoded_task_state).flatten(),
                np.array(encoded_node_states).flatten()
            ])
            encoded_masks=np.array(masks).flatten()
            action=self.MAPPO_env.takeAction(encoded_state,encoded_masks,task_type=task_type)
            offload_node_id=sorted_node_ids[action]
            if offload_node_id is None:
                continue
            offload_node_type = self.nodeScheduler.getNodeTypeById(self.env, offload_node_id)
            current_node_type = self.nodeScheduler.getNodeTypeById(self.env, current_node_id)
            if task.task_type == TaskTypeEnum.COMMUNICATION:
                to_trans_route = [current_node_id, offload_node_id, rx_node_id]
                node_type_list = [current_node_type, offload_node_type, rx_node_type]
                self._updateCommResourceUsageCache(to_trans_route, node_type_list)
                self._updateStorageResourceUsageCache(to_trans_route, required_trans_size)
            elif task.task_type == TaskTypeEnum.COMPUTATION:
                to_trans_route = [current_node_id, offload_node_id]
                node_type_list = [current_node_type, offload_node_type]
                self._updateCommResourceUsageCache(to_trans_route, node_type_list)
                self._updateStorageResourceUsageCache(to_trans_route, required_trans_size)
            elif task.task_type == TaskTypeEnum.SENSING:
                to_trans_route = [current_node_id, offload_node_id]
            else:
                raise TypeError('Invalid task type')
            self.taskScheduler.setCommStepScheInfo(self.env, step_id, required_trans_size, to_trans_route)

            self.used_node_ids.append(current_node_id)
            self.task_node_dict[task_id]=current_node_id
            node_idx=self.agent_idx_dict[current_node_id]
            self.combined_obs_space[task_type][node_idx,:]=encoded_state


            # 多余一个可行动作才加入buffer
            if active_actions>1:
                offloaded_steps[step_id]={
                    'node_id':current_node_id,
                    'task_id':task_id,
                    'task_type':task_type,
                    'action_masks':masks,
                    'action':action
                }

            i += 1

        if not self.eval:
            for step_id,offload_info in offloaded_steps.items():
                task_type=offload_info['task_type']
                self.exp_buffer.add(offload_info['node_id'],offload_info['task_id'],task_type,self.combined_obs_id[task_type],offload_info['action_masks'],
                                    offload_info['action'],agent_mask=True)
                last_task_id=self.last_offload_task_ids.get(offload_info['task_type']).get(offload_info['node_id'],None)
                if last_task_id is not None:
                    self.exp_buffer.setNextState(offload_info['node_id'],last_task_id,offload_info['task_type'],self.combined_obs_id[task_type])
                self.last_offload_task_ids[offload_info['task_type']][offload_info['node_id']] = offload_info['task_id']
            for task_type in TaskTypeEnum:
                self.MAPPO_env.addGlobalState(task_type,self.combined_obs_id[task_type],self.combined_obs_space[task_type])
            for task_type in TaskTypeEnum:
                self.combined_obs_id[task_type]+=1

        print('offload:', i)
        self.sum_offload+=i
        print('sum offload:', self.sum_offload)

    def scheduleCommOffloading(self):
        pass
    def scheduleCompOffloading(self):
        pass
    def scheduleSensingOffloading(self):
        pass
    def scheduleComputation(self):
        super().scheduleComputation()
    def scheduleSensing(self):
        super().scheduleSensing()

    def updateExperience(self):
        last_step_succ_tasks = self.env.task_manager.getLastSimIntervalFinishedTasks()
        last_step_fail_tasks = self.env.task_manager.getLastSimIntervalFailedTasks()

        succ_tasks={}
        fail_tasks={}
        for task_type_enum,task_dict in last_step_succ_tasks.items():
            succ_tasks.update(task_dict)
        for task_type_enum,task_dict in last_step_fail_tasks.items():
            fail_tasks.update(task_dict)


        for task_id,task in succ_tasks.items():
            task_type=task.task_type
            node_id=self.task_node_dict[task_id]
            reward=(task.ttl-(task.finish_time-task.arrival_time))/task.ttl
            if task_type == TaskTypeEnum.COMMUNICATION:
                self.suc_comm_rewards.append(reward)
            elif task_type == TaskTypeEnum.COMPUTATION:
                self.suc_comp_rewards.append(reward)
            elif task_type == TaskTypeEnum.SENSING:
                self.suc_sens_rewards.append(reward)
            reward=self.rewardNormalization(reward,task_type)
            self.exp_buffer.completeExperience(node_id,task_id,task_type,reward)
            self.sum_succ_task_num+=1
            self.sum_succ_task_reward+=reward
            self.sum_terminated_task_num+=1
            self.sum_terminated_task_reward+=reward
        for task_id,task in fail_tasks.items():
            task_type = task.task_type
            if task_id not in self.task_node_dict:
                continue
            node_id=self.task_node_dict[task_id]
            if task_type == TaskTypeEnum.COMMUNICATION:
                reward = -1.0
            elif task_type == TaskTypeEnum.COMPUTATION:
                reward = -0.95
            elif task_type == TaskTypeEnum.SENSING:
                reward = -1.0
            # reward = -1
            self.exp_buffer.completeExperience(node_id,task_id,task_type,reward)
            self.sum_terminated_task_num+=1
            self.sum_terminated_task_reward+=reward


        for task_type,exp_dicts in self.exp_buffer.get().items():
            for node_id,exp_dict in exp_dicts.items():
                to_delete_task_ids=[]
                for task_id,exp in exp_dict.items():
                    if exp['reward'] is not None and exp['next_combined_states_id'] is not None:
                        agent_idx=self.agent_idx_dict[node_id]
                        self.MAPPO_env.addExperience(task_type,agent_idx,exp['combined_states_id'],exp['action_mask'],exp['action'],exp['reward'],exp['next_combined_states_id'],exp['agent_mask'])
                        to_delete_task_ids.append(task_id)
                for task_id in to_delete_task_ids:
                    del exp_dict[task_id] # 直接操作buffer进行内容修改


    def getSuccTaskAvgReward(self):
        return self.sum_succ_task_reward/self.sum_succ_task_num if self.sum_succ_task_num>0 else 0
    def getSuccTaskSumReward(self):
        return self.sum_succ_task_reward
    def getTerminatedTaskAvgReward(self):
        return self.sum_terminated_task_reward/self.sum_terminated_task_num if self.sum_terminated_task_num>0 else 0
    def getTerminatedTaskSumReward(self):
        return self.sum_terminated_task_reward

    def rewardNormalization(self,reward,task_type_enum):
        if task_type_enum==TaskTypeEnum.COMMUNICATION:
            old_rewards=self.suc_comm_rewards
        elif task_type_enum==TaskTypeEnum.COMPUTATION:
            old_rewards=self.suc_comp_rewards
        elif task_type_enum==TaskTypeEnum.SENSING:
            old_rewards=self.suc_sens_rewards

        if len(old_rewards)<10:
            return reward

        # max_reward=max(old_rewards)
        # min_reward=min(old_rewards)
        # norm_reward=(reward-min_reward)/(max_reward-min_reward)
        # return norm_reward

        mean = np.mean(old_rewards[-100:])  # 滑动窗口
        std = np.std(old_rewards[-100:]) + 1e-6
        return 2*(reward - mean) / std




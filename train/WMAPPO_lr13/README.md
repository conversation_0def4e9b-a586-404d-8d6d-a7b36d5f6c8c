# Transformer世界模型训练系统

基于TransMAPPO框架的Transformer世界模型训练和集成系统，用于实现基于模型的强化学习(MBRL)。

## 系统概述

本系统实现了以下功能：
1. **多配置数据收集**: 使用不同任务负载配置收集状态序列数据
2. **Transformer世界模型**: 训练序列到序列的Transformer模型预测环境状态
3. **MBRL集成**: 将训练好的世界模型集成到TransMAPPO框架中

## 目录结构

```
world_model/
├── data_collection/          # 数据收集模块
│   ├── state_collector.py    # 状态序列收集器
│   └── collect_data.py       # 数据收集主脚本
├── model/                    # 模型相关模块
│   ├── transformer_world_model.py  # Transformer世界模型
│   ├── data_processor.py     # 数据处理器
│   └── trainer.py           # 模型训练器
├── mbrl_strategy.py         # MBRL策略模块
├── train_world_model.py     # 模型训练主脚本
├── run_world_model_pipeline.py  # 完整流水线脚本
└── README.md               # 本文档
```

## 快速开始

### 1. 完整流水线执行

运行完整的训练流水线（推荐）：

```bash
cd train/TransMAPPO_train5_all/world_model
python run_world_model_pipeline.py
```

这将依次执行：
- 数据收集（多种负载配置，每种200个episodes）
- 模型训练（50个epochs）
- 集成测试

### 2. 分步执行

#### 步骤1: 数据收集

```bash
python data_collection/collect_data.py
```

收集不同负载配置下的状态序列数据：
- `light_load`: 轻负载配置
- `medium_load`: 中等负载配置  
- `heavy_load`: 重负载配置

#### 步骤2: 训练世界模型

```bash
python train_world_model.py \
    --data_dir ./world_model_data \
    --num_epochs 50 \
    --batch_size 32 \
    --seq_len 50 \
    --d_model 256 \
    --device cuda
```

#### 步骤3: 使用训练好的模型

```python
from world_model.mbrl_strategy import MBRLStrategyModule

# 创建MBRL策略
strategy = MBRLStrategyModule(
    world_model_path="./world_model_checkpoints/best_model.pth",
    use_world_model=True
)

# 在TransMAPPO框架中使用
strategy.initialize(env)
```

## 配置参数

### 数据收集配置

- **episodes_per_config**: 每种配置运行的episode数（默认10）
- **max_simulation_time**: 每个episode的仿真时间（默认200s）
- **负载配置变体**: 自动生成轻、中、重三种负载配置

### 模型训练配置

- **seq_len**: 输入序列长度（默认50）
- **d_model**: Transformer模型维度（默认256）
- **nhead**: 注意力头数（默认8）
- **num_layers**: Transformer层数（默认4）
- **batch_size**: 批次大小（默认32）
- **num_epochs**: 训练轮数（默认50）

### MBRL配置

- **prediction_horizon**: 预测步数（默认10）
- **state_history_len**: 状态历史长度（默认20）

## 核心特性

### 1. 智能数据收集

- **多配置采样**: 自动生成不同任务负载配置
- **随机策略**: 使用随机策略而非固定策略收集数据
- **序列化存储**: 数据以pickle和CSV格式保存

### 2. 先进的Transformer架构

- **因果注意力**: 确保只能看到过去的信息
- **位置编码**: 处理序列的时间信息
- **多头注意力**: 捕获不同类型的依赖关系
- **残差连接**: 提高训练稳定性

### 3. 无缝集成

- **继承设计**: MBRLStrategyModule继承自MAPPOStrategyModule
- **API兼容**: 保持与现有TransMAPPO框架的兼容性
- **渐进式集成**: 可以选择性启用世界模型功能

## 数据流程

```
环境状态 → 状态收集器 → 序列数据 → 数据处理器 → 训练数据
                                                      ↓
预测状态 ← 世界模型 ← 训练器 ← 标准化数据 ← 数据加载器
```

## 性能优化

### 1. 数据处理优化

- **批量处理**: 使用DataLoader进行批量训练
- **数据标准化**: 自动标准化状态和动作数据
- **内存管理**: 定期清理已保存的序列数据

### 2. 训练优化

- **梯度裁剪**: 防止梯度爆炸
- **学习率调度**: 自适应学习率调整
- **早停机制**: 基于验证损失的早停
- **检查点保存**: 定期保存训练进度

### 3. 推理优化

- **模型缓存**: 缓存加载的世界模型
- **批量预测**: 支持批量状态预测
- **设备管理**: 自动选择GPU/CPU

## 监控和调试

### 1. TensorBoard监控

```bash
tensorboard --logdir ./world_model_checkpoints/logs
```

监控指标：
- 训练/验证损失
- 学习率变化
- 梯度范数

### 2. 日志输出

系统提供详细的日志输出：
- 数据收集进度
- 训练进度和指标
- 模型预测性能

### 3. 错误处理

- 自动错误恢复
- 详细错误信息
- 优雅降级（世界模型失败时使用原始策略）

## 扩展指南

### 1. 添加新的负载配置

在`MultiConfigDataCollector.create_config_variants()`中添加新配置：

```python
{
    'name': 'custom_load',
    'description': '自定义负载配置',
    'task_modifications': {
        'communication': {
            'task_size': [15000, 60000],
            'task_requirement_unit': 0.003,
        }
    }
}
```

### 2. 自定义世界模型架构

继承`TransformerWorldModel`类并重写相关方法：

```python
class CustomWorldModel(TransformerWorldModel):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 添加自定义层
```

### 3. 自定义评估指标

在`MBRLStrategyModule.evaluate_predicted_states()`中添加自定义评估逻辑。

## 故障排除

### 常见问题

1. **CUDA内存不足**: 减小batch_size或seq_len
2. **数据加载失败**: 检查数据目录路径和文件权限
3. **模型训练不收敛**: 调整学习率或模型架构
4. **集成测试失败**: 检查模型文件路径和依赖

### 性能调优

1. **训练速度慢**: 增加num_workers或使用更大的batch_size
2. **内存使用过高**: 减小模型维度或序列长度
3. **预测精度低**: 增加训练数据或调整模型架构


## 许可证

本项目遵循与SpaceTaskSim项目相同的许可证。

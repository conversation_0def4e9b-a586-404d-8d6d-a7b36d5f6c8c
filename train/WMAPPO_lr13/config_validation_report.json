{"validation_summary": {"total_variants": 5, "reference_configs": ["train_low", "train_mid_low", "train_mid", "train_mid_high", "train_high"], "validation_time": "2025年 06月 10日 星期二 12:01:01 CST"}, "detailed_comparison": {"low_load": {"reference_config": "train_low", "reference_params": {"task": {"communication_requirement_unit": 0.0005, "computation_requirement_unit": 6e-05, "sensing_time": [11, 22], "sensing_interval": [240, 120, 120, 120, 90]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "generated_params": {"task": {"communication_requirement_unit": 0.0005, "computation_requirement_unit": 6e-05, "sensing_time": [11, 22], "sensing_interval": [240, 120, 120, 120, 90]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "matches": true}, "mid_low_load": {"reference_config": "train_mid_low", "reference_params": {"task": {"communication_requirement_unit": 0.001, "computation_requirement_unit": 0.00012, "sensing_time": [11, 22], "sensing_interval": [120, 60, 60, 60, 45]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "generated_params": {"task": {"communication_requirement_unit": 0.001, "computation_requirement_unit": 0.00012, "sensing_time": [11, 22], "sensing_interval": [120, 60, 60, 60, 45]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "matches": true}, "mid_load": {"reference_config": "train_mid", "reference_params": {"task": {"communication_requirement_unit": 0.002, "computation_requirement_unit": 0.00024, "sensing_time": [11, 22], "sensing_interval": [60, 45, 45, 45, 30]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "generated_params": {"task": {"communication_requirement_unit": 0.002, "computation_requirement_unit": 0.00024, "sensing_time": [11, 22], "sensing_interval": [60, 45, 45, 45, 30]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "matches": true}, "mid_high_load": {"reference_config": "train_mid_high", "reference_params": {"task": {"communication_requirement_unit": 0.004, "computation_requirement_unit": 0.00048, "sensing_time": [11, 22], "sensing_interval": [30, 15, 15, 15, 10]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "generated_params": {"task": {"communication_requirement_unit": 0.004, "computation_requirement_unit": 0.00048, "sensing_time": [11, 22], "sensing_interval": [30, 15, 15, 15, 10]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "matches": true}, "high_load": {"reference_config": "train_high", "reference_params": {"task": {"communication_requirement_unit": 0.008, "computation_requirement_unit": 0.00096, "sensing_time": [11, 22], "sensing_interval": [15, 10, 10, 10, 5]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "generated_params": {"task": {"communication_requirement_unit": 0.008, "computation_requirement_unit": 0.00096, "sensing_time": [11, 22], "sensing_interval": [15, 10, 10, 10, 5]}, "resource": {"cycle_per_byte": 17, "satellite_cpu": 4, "camera_num": 6}}, "matches": true}}}
"""
世界模型使用示例
演示如何使用训练好的世界模型进行基于模型的强化学习
"""

import os
import sys
import numpy as np
import torch

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from mbrl_strategy import MBRLStrategyModule
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from spacetasksim.utils import file_util


def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("=" * 60)
    print("示例1: 基本使用方法")
    print("=" * 60)
    
    # 加载配置
    config_path = os.path.join(os.path.dirname(__file__), '../config.yaml')
    config = file_util.load_config(config_path)
    
    # 设置较短的仿真时间用于演示
    config['config_simulation']['max_simulation_time'] = 30
    
    # 创建环境
    env = SpaceTaskSimEnv(config)
    
    # 创建MBRL策略（如果有训练好的模型）
    world_model_path = "./world_model_checkpoints/best_model.pth"
    if os.path.exists(world_model_path):
        print(f"✅ 找到训练好的世界模型: {world_model_path}")
        strategy = MBRLStrategyModule(
            world_model_path=world_model_path,
            use_world_model=True
        )
    else:
        print("⚠️  未找到训练好的世界模型，使用原始策略")
        strategy = MBRLStrategyModule(use_world_model=False)
    
    # 创建评估模块
    evaluation = SpaceTaskSimEvaluation(env, strategy)
    
    # 初始化策略
    strategy.initialize(env, last_episode=None, final=False)
    
    # 运行仿真
    print("开始仿真...")
    step_count = 0
    while not env.isDone() and step_count < 300:
        strategy.scheduleStep()
        env.step()
        strategy.updateExperience()
        evaluation.evaluateStep()
        step_count += 1
        
        if step_count % 50 == 0:
            print(f"步数: {step_count}, 时间: {env.current_time:.1f}s")
    
    # 获取结果
    avg_reward = strategy.getTerminatedTaskSumReward()
    succ_ratio = evaluation.getCompletionRatio()
    
    print(f"\n仿真结果:")
    print(f"  总步数: {step_count}")
    print(f"  平均奖励: {avg_reward:.4f}")
    print(f"  成功率: {succ_ratio:.4f}")
    print(f"  世界模型状态: {'已启用' if strategy.use_world_model else '未启用'}")


def example_2_model_prediction():
    """示例2: 直接使用世界模型进行预测"""
    print("\n" + "=" * 60)
    print("示例2: 直接使用世界模型进行预测")
    print("=" * 60)
    
    world_model_path = "./world_model_checkpoints/best_model.pth"
    if not os.path.exists(world_model_path):
        print("⚠️  未找到训练好的世界模型，跳过此示例")
        return
    
    # 加载世界模型
    from world_model.model.transformer_world_model import TransformerWorldModel
    import pickle
    
    try:
        # 加载模型检查点
        checkpoint = torch.load(world_model_path, map_location='cpu')
        model_config = checkpoint['model_config']
        
        # 创建模型
        world_model = TransformerWorldModel(
            state_dim=model_config['state_dim'],
            action_dim=model_config['action_dim'],
            d_model=model_config['d_model'],
            max_seq_len=model_config['max_seq_len']
        )
        
        # 加载权重
        world_model.load_state_dict(checkpoint['model_state_dict'])
        world_model.eval()
        
        print(f"✅ 世界模型加载成功")
        print(f"  状态维度: {model_config['state_dim']}")
        print(f"  动作维度: {model_config['action_dim']}")
        print(f"  模型维度: {model_config['d_model']}")
        
        # 加载标准化器
        scaler_path = "./world_model_checkpoints/scalers.pkl"
        if os.path.exists(scaler_path):
            with open(scaler_path, 'rb') as f:
                scalers = pickle.load(f)
            state_scaler = scalers['state_scaler']
            action_scaler = scalers['action_scaler']
            print("✅ 标准化器加载成功")
        else:
            state_scaler = None
            action_scaler = None
            print("⚠️  未找到标准化器")
        
        # 创建示例数据进行预测
        seq_len = 10
        batch_size = 1
        
        # 生成随机状态和动作序列
        states = np.random.randn(batch_size, seq_len, model_config['state_dim'])
        actions = np.random.randint(0, 10, (batch_size, seq_len, model_config['action_dim']))
        
        # 标准化（如果有标准化器）
        if state_scaler is not None:
            states_flat = states.reshape(-1, states.shape[-1])
            states_normalized = state_scaler.transform(states_flat)
            states = states_normalized.reshape(states.shape)
        
        if action_scaler is not None:
            actions_flat = actions.reshape(-1, actions.shape[-1])
            actions_normalized = action_scaler.transform(actions_flat)
            actions = actions_normalized.reshape(actions.shape)
        
        # 转换为tensor
        states_tensor = torch.from_numpy(states).float()
        actions_tensor = torch.from_numpy(actions).float()
        
        # 进行预测
        with torch.no_grad():
            predicted_states = world_model(states_tensor, actions_tensor)
        
        print(f"\n预测结果:")
        print(f"  输入状态形状: {states_tensor.shape}")
        print(f"  输入动作形状: {actions_tensor.shape}")
        print(f"  预测状态形状: {predicted_states.shape}")
        print(f"  预测状态范围: [{predicted_states.min():.4f}, {predicted_states.max():.4f}]")
        
        # 计算预测误差（使用简单的持续性基线）
        baseline_prediction = states_tensor  # 简单基线：状态不变
        model_error = torch.mean((predicted_states - states_tensor) ** 2)
        baseline_error = torch.mean((baseline_prediction - states_tensor) ** 2)
        
        print(f"  模型预测误差 (MSE): {model_error:.6f}")
        print(f"  基线预测误差 (MSE): {baseline_error:.6f}")
        
    except Exception as e:
        print(f"❌ 世界模型预测失败: {e}")


def example_3_compare_strategies():
    """示例3: 比较原始策略和MBRL策略"""
    print("\n" + "=" * 60)
    print("示例3: 比较原始策略和MBRL策略")
    print("=" * 60)
    
    # 加载配置
    config_path = os.path.join(os.path.dirname(__file__), '../config.yaml')
    config = file_util.load_config(config_path)
    config['config_simulation']['max_simulation_time'] = 50
    
    results = {}
    
    # 测试原始策略
    print("\n测试原始策略...")
    env1 = SpaceTaskSimEnv(config)
    strategy1 = MBRLStrategyModule(use_world_model=False)
    evaluation1 = SpaceTaskSimEvaluation(env1, strategy1)
    strategy1.initialize(env1, last_episode=None, final=False)
    
    step_count1 = 0
    while not env1.isDone() and step_count1 < 500:
        strategy1.scheduleStep()
        env1.step()
        strategy1.updateExperience()
        evaluation1.evaluateStep()
        step_count1 += 1
    
    results['original'] = {
        'steps': step_count1,
        'reward': strategy1.getTerminatedTaskSumReward(),
        'success_ratio': evaluation1.getCompletionRatio()
    }
    
    # 测试MBRL策略（如果有模型）
    world_model_path = "./world_model_checkpoints/best_model.pth"
    if os.path.exists(world_model_path):
        print("\n测试MBRL策略...")
        env2 = SpaceTaskSimEnv(config)
        strategy2 = MBRLStrategyModule(
            world_model_path=world_model_path,
            use_world_model=True
        )
        evaluation2 = SpaceTaskSimEvaluation(env2, strategy2)
        strategy2.initialize(env2, last_episode=None, final=False)
        
        step_count2 = 0
        while not env2.isDone() and step_count2 < 500:
            strategy2.scheduleStep()
            env2.step()
            strategy2.updateExperience()
            evaluation2.evaluateStep()
            step_count2 += 1
        
        results['mbrl'] = {
            'steps': step_count2,
            'reward': strategy2.getTerminatedTaskSumReward(),
            'success_ratio': evaluation2.getCompletionRatio()
        }
    else:
        print("⚠️  未找到训练好的世界模型，跳过MBRL策略测试")
        results['mbrl'] = None
    
    # 比较结果
    print(f"\n策略比较结果:")
    print(f"{'策略':<15} {'步数':<10} {'平均奖励':<15} {'成功率':<10}")
    print("-" * 50)
    
    for strategy_name, result in results.items():
        if result is not None:
            print(f"{strategy_name:<15} {result['steps']:<10} {result['reward']:<15.4f} {result['success_ratio']:<10.4f}")
    
    if results['mbrl'] is not None:
        reward_improvement = results['mbrl']['reward'] - results['original']['reward']
        success_improvement = results['mbrl']['success_ratio'] - results['original']['success_ratio']
        
        print(f"\nMBRL相对于原始策略的改进:")
        print(f"  奖励改进: {reward_improvement:+.4f}")
        print(f"  成功率改进: {success_improvement:+.4f}")


def main():
    """主函数"""
    print("🌟 世界模型使用示例")
    print("本示例演示如何使用训练好的Transformer世界模型")
    
    try:
        # 示例1: 基本使用
        example_1_basic_usage()
        
        # 示例2: 模型预测
        example_2_model_prediction()
        
        # 示例3: 策略比较
        example_3_compare_strategies()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例执行完成!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

"""
基于模型的强化学习策略模块
集成Transformer世界模型到TransMAPPO框架中
"""
from copy import deepcopy

import torch
import numpy as np
import pickle
import os
import sys
from typing import Dict, List, Tuple, Optional, Any

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from strategy import MAPPOStrategyModule
from model.transformer_world_model import TransformerWorldModel
from spacetasksim.enum.task_type_enum import TaskTypeEnum


class MBRLStrategyModule(MAPPOStrategyModule):
    """基于模型的强化学习策略模块"""
    
    def __init__(self, world_model_path: Optional[str] = None, use_world_model: bool = True):
        """
        初始化MBRL策略模块
        
        Args:
            world_model_path: 世界模型检查点路径
            use_world_model: 是否使用世界模型
        """
        super().__init__()
        
        self.use_world_model = use_world_model
        self.world_model = None
        self.world_model_device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.state_scaler = None
        self.action_scaler = None
        
        # 世界模型相关配置
        self.prediction_horizon = 10  # 预测步数
        self.state_history_len = 10   #20 状态历史长度
        self.state_history = {task_type: {} for task_type in TaskTypeEnum}
        self.action_history = {task_type: {} for task_type in TaskTypeEnum}
        
        # 加载世界模型
        if use_world_model and world_model_path:
            self.load_world_model(world_model_path)
    
    def load_world_model(self, model_path: str):
        """
        加载训练好的世界模型
        
        Args:
            model_path: 模型检查点路径
        """
        try:
            print(f"加载世界模型: {model_path}")
            
            # 加载检查点
            checkpoint = torch.load(model_path, map_location=self.world_model_device)
            model_config = checkpoint['model_config']

            # 创建模型
            self.world_model = TransformerWorldModel(
                state_dim=model_config['state_dim'],
                action_dim=model_config['action_dim'],
                d_model=model_config['d_model'],
                max_seq_len=model_config['max_seq_len']
            )
            
            # 加载模型权重
            self.world_model.load_state_dict(checkpoint['model_state_dict'])
            self.world_model.to(self.world_model_device)
            self.world_model.eval()
            
            print(f"世界模型加载成功，参数数量: {sum(p.numel() for p in self.world_model.parameters()):,}")
            
            # 加载标准化器
            scaler_path = os.path.join(os.path.dirname(model_path), 'scalers.pkl')
            if os.path.exists(scaler_path):
                self.load_scalers(scaler_path)
                print("标准化器加载成功")
            else:
                print("警告: 未找到标准化器文件")
                
        except Exception as e:
            print(f"加载世界模型失败: {e}")
            self.use_world_model = False
    
    def load_scalers(self, scaler_path: str):
        """加载标准化器"""
        with open(scaler_path, 'rb') as f:
            scalers = pickle.load(f)
        self.state_scaler = scalers['state_scaler']
        self.action_scaler = scalers['action_scaler']
    
    def update_state_history(self,node_id:str, task_type: TaskTypeEnum, state: np.ndarray, action: int):
        """
        更新状态和动作历史
        
        Args:
            node_id: 节点ID
            task_type: 任务类型
            state: 当前状态
            action: 当前动作
        """
        # # 处理状态维度
        # if state.ndim == 2:  # [n_agents, state_dim]
        #     state_flat = state.flatten()
        # else:
        #     state_flat = state
        
        # 更新状态历史
        if node_id not in self.state_history[task_type]:
            self.state_history[task_type][node_id] = []
        self.state_history[task_type][node_id].append(deepcopy(state))
        if len(self.state_history[task_type][node_id]) > self.state_history_len:
            self.state_history[task_type][node_id].pop(0)
        
        # 更新动作历史
        if node_id not in self.action_history[task_type]:
            self.action_history[task_type][node_id] = []
        self.action_history[task_type][node_id].append(action)
        if len(self.action_history[task_type][node_id]) > self.state_history_len:
            self.action_history[task_type][node_id].pop(0)
    
    def predict_future_states(self, 
                             node_id:str,task_type: TaskTypeEnum,
                             future_actions: List[int]=[0]) -> Optional[np.ndarray]:
        """
        使用世界模型预测未来状态
        
        Args:
            task_type: 任务类型
            future_actions: 未来动作序列
            
        Returns:
            预测的未来状态序列，如果预测失败则返回None
        """
        if not self.use_world_model or self.world_model is None:
            return None
        
        # 检查是否有足够的历史数据
        if (node_id not in self.state_history[task_type] or
                len(self.state_history[task_type][node_id]) < 2):
            return None
        
        try:
            # 准备输入数据
            states = np.array(self.state_history[task_type][node_id])
            actions = np.array(self.action_history[task_type][node_id]).reshape(-1, 1)
            
            # 标准化
            if self.state_scaler is not None:
                states = self.state_scaler.transform(states)
            if self.action_scaler is not None:
                actions = self.action_scaler.transform(actions)
            
            # 转换为tensor
            states_tensor = torch.from_numpy(states).float().unsqueeze(0).to(self.world_model_device)
            actions_tensor = torch.from_numpy(actions).float().unsqueeze(0).to(self.world_model_device)
            
            # 准备未来动作
            future_actions_array = np.array(future_actions).reshape(-1, 1)
            if self.action_scaler is not None:
                future_actions_array = self.action_scaler.transform(future_actions_array)
            
            # 执行多步预测
            with torch.no_grad():
                current_states = states_tensor
                current_actions = actions_tensor
                predicted_states = []
                
                for i, future_action in enumerate(future_actions_array):
                    # 预测下一个状态
                    next_state = self.world_model.predict_next_state(current_states, current_actions)
                    
                    # 反标准化
                    if self.state_scaler is not None:
                        next_state_np = self.state_scaler.inverse_transform(next_state.cpu().numpy())
                    else:
                        next_state_np = next_state.cpu().numpy()
                    
                    predicted_states.append(next_state_np[0])
                    
                    # 更新状态和动作序列用于下一步预测
                    next_state_tensor = torch.from_numpy(
                        self.state_scaler.transform(next_state_np) if self.state_scaler else next_state_np
                    ).float().to(self.world_model_device)
                    
                    future_action_tensor = torch.from_numpy(future_actions_array[i:i+1]).float().to(self.world_model_device)
                    
                    # 更新序列（保持固定长度）
                    current_states = torch.cat([current_states[:, 1:], next_state_tensor.unsqueeze(0)], dim=1)
                    current_actions = torch.cat([current_actions[:, 1:], future_action_tensor.unsqueeze(0)], dim=1)
            
            return np.array(predicted_states)
            
        except Exception as e:
            print(f"世界模型预测失败: {e}")
            return None
    
    def model_based_action_selection(self, 
                                   node_id:str,
                                   task_type: TaskTypeEnum,
                                   current_state: np.ndarray,
                                   available_actions: List[int]) -> int:
        """
        基于模型的动作选择
        
        Args:
            task_type: 任务类型
            current_state: 当前状态
            available_actions: 可用动作列表
            
        Returns:
            选择的动作
        """
        if not self.use_world_model or len(available_actions) <= 1:
            # 如果不使用世界模型或只有一个动作，使用原始策略
            return available_actions[0] if available_actions else 0
        
        best_action = available_actions[0]
        best_value = float('-inf')
        
        # 为每个可用动作评估未来价值
        for action in available_actions:
            # 创建未来动作序列（简单策略：重复当前动作）
            future_actions = [action] * min(self.prediction_horizon, 5)
            
            # 预测未来状态
            predicted_states = self.predict_future_states(node_id,task_type, future_actions)
            
            if predicted_states is not None:
                # 计算预测状态的价值（这里使用简单的启发式）
                value = self.evaluate_predicted_states(predicted_states, task_type)
                
                if value > best_value:
                    best_value = value
                    best_action = action
        
        return best_action
    
    def evaluate_predicted_states(self, predicted_states: np.ndarray, task_type: TaskTypeEnum) -> float:
        """
        评估预测状态的价值
        
        Args:
            predicted_states: 预测的状态序列
            task_type: 任务类型
            
        Returns:
            状态价值
        """
        # 简单的启发式评估：状态变化的稳定性和趋势
        if len(predicted_states) < 2:
            return 0.0
        
        # 计算状态变化的方差（越小越好，表示更稳定）
        state_changes = np.diff(predicted_states, axis=0)
        stability_score = -np.mean(np.var(state_changes, axis=0))
        
        # 计算状态的平均值（根据任务类型调整）
        avg_state = np.mean(predicted_states, axis=0)

        # 定义维度（根据 parseMAPPODimArgs()）
        dim_one_hot = len(TaskTypeEnum)  # 假设为3（COMM, COMP, SENSING）
        dim_obs_task_state = 5 + 2 + 6  # COMM(5) + COMP(2) + SENSING(6) = 13
        dim_obs_node_state = 4 + 3 + 2 + 3 + 1 + 2  # basic(4) + comm(3) + comp(2) + sensing(3) + storage(1) + available(2)
        dim_obs_node = 30  # 节点数量

        # 通信相关维度：所有节点的 comm_state (idle_ratio, encode_s2s_power_db, encode_s2s_bandwidth)
        comm_offset_in_node = 4  # basic(4)
        comm_dims = [
            dim_one_hot + dim_obs_task_state + node_id * dim_obs_node_state + comm_offset_in_node + i
            for node_id in range(dim_obs_node)
            for i in range(3)  # 每节点3维
        ]

        # 计算相关维度：所有节点的 comp_state (idle_cpu_ratio, encode_avg_cpu_frequency)
        comp_offset_in_node = 4 + 3  # basic(4) + comm(3)
        comp_dims = [
            dim_one_hot + dim_obs_task_state + node_id * dim_obs_node_state + comp_offset_in_node + i
            for node_id in range(dim_obs_node)
            for i in range(2)  # 每节点2维
        ]

        # 感知相关维度：所有节点的 sensing_state (idle_camera_ratio, avg_camera_accuracy, encode_elevation)
        sensing_offset_in_node = 4 + 3 + 2  # basic(4) + comm(3) + comp(2)
        sensing_dims = [
            dim_one_hot + dim_obs_task_state + node_id * dim_obs_node_state + sensing_offset_in_node + i
            for node_id in range(dim_obs_node)
            for i in range(3)  # 每节点3维
        ]

        # 任务特定评估
        if task_type == TaskTypeEnum.COMMUNICATION:
            value = stability_score + np.mean(avg_state[comm_dims])  # 通信资源均值
        elif task_type == TaskTypeEnum.COMPUTATION:
            value = stability_score + np.mean(avg_state[comp_dims])  # 计算资源均值
        elif task_type == TaskTypeEnum.SENSING:
            value = stability_score + np.mean(avg_state[sensing_dims])  # 感知资源均值
        else:
            value = stability_score

        
        return value
    
    def scheduleStep(self):
        """重写调度步骤，集成世界模型"""
        # 调用原始的调度逻辑
        super().scheduleStepByWorldModel(self.predict_future_states)
        
        # 更新状态历史（在每次调度后）
        for task_type in TaskTypeEnum:
            for node_id in self.used_node_ids[task_type]:
                node_idx=self.agent_idx_dict[node_id]
                current_state = self.combined_obs_space[task_type][node_idx][:self.combined_obs_space[task_type].shape[1] // 2]
                # state一半真实一半预测，取前一半
                last_action = self.combined_actions[task_type][node_idx]
                self.update_state_history(node_id,task_type, current_state, last_action)
    
    def enhanced_action_selection(self,
                                task_type: TaskTypeEnum,
                                encoded_state: np.ndarray,
                                encoded_masks: np.ndarray,
                                available_actions: List[int]) -> int:
        """
        增强的动作选择，结合原始策略和世界模型
        
        Args:
            task_type: 任务类型
            encoded_state: 编码状态
            encoded_masks: 编码掩码
            available_actions: 可用动作列表
            
        Returns:
            选择的动作
        """
        # 获取原始策略的动作
        original_action = self.MAPPO_env.takeAction(encoded_state, encoded_masks, task_type=task_type)
        
        # 如果使用世界模型且有多个可用动作，进行模型基础的动作选择
        if self.use_world_model and len(available_actions) > 1:
            model_action = self.model_based_action_selection(
                task_type, 
                self.combined_obs_space[task_type], 
                available_actions
            )
            
            # 简单的动作融合策略：如果模型动作在可用动作中，使用模型动作
            if model_action in available_actions:
                return model_action
        
        return original_action
    
    def reset(self):
        """重置策略模块"""
        super().reset()
        
        # 清空状态历史
        self.state_history = {task_type: {} for task_type in TaskTypeEnum}
        self.action_history = {task_type: {} for task_type in TaskTypeEnum}

"""
数据处理模块
用于处理收集的状态序列数据，准备用于Transformer训练
"""

import torch
import numpy as np
import pickle
import os
from typing import List, Dict, Tuple, Optional, Any
from torch.utils.data import Dataset, DataLoader
import glob
from sklearn.preprocessing import StandardScaler
import json


class StateSequenceDataset(Dataset):
    """状态序列数据集"""
    
    def __init__(self, 
                 sequences: List[Dict],
                 seq_len: int = 50,
                 overlap: int = 10,
                 normalize: bool = True):
        """
        初始化数据集
        
        Args:
            sequences: 状态序列列表
            seq_len: 序列长度
            overlap: 序列重叠长度
            normalize: 是否标准化数据
        """
        self.seq_len = seq_len
        self.overlap = overlap
        self.normalize = normalize
        
        # 处理序列数据
        self.processed_sequences = []
        self.state_scaler = StandardScaler() if normalize else None
        self.action_scaler = StandardScaler() if normalize else None
        
        self._process_sequences(sequences)
        
    def _process_sequences(self, sequences: List[Dict]):
        """处理原始序列数据"""
        all_states = []
        all_actions = []
        
        # 收集所有状态和动作用于标准化
        for seq in sequences:
            states = np.array(seq['states'])
            actions = np.array(seq['actions'])
            
            if len(states) < 2:  # 至少需要2个状态
                continue
                
            # 展平状态数据用于标准化
            if states.ndim == 3:  # [seq_len, n_agents, state_dim]
                states_flat = states.reshape(-1, states.shape[-1])
            else:
                states_flat = states
            
            all_states.append(states_flat)
            
            # 处理动作数据
            if actions.ndim == 1:
                actions = actions.reshape(-1, 1)
            all_actions.append(actions)
        
        if not all_states:
            raise ValueError("没有有效的序列数据")
        
        # 拟合标准化器
        if self.normalize:
            all_states_concat = np.vstack(all_states)
            all_actions_concat = np.vstack(all_actions)
            
            self.state_scaler.fit(all_states_concat)
            self.action_scaler.fit(all_actions_concat)
        
        # 创建训练样本
        for seq in sequences:
            self._create_samples_from_sequence(seq)
    
    def _create_samples_from_sequence(self, seq: Dict):
        """从单个序列创建训练样本"""
        states = np.array(seq['states'])
        actions = np.array(seq['actions'])
        
        if len(states) < 2:
            return
        
        # 处理状态维度
        if states.ndim == 3:  # [seq_len, n_agents, state_dim]
            # 展平agent维度
            states = states.reshape(states.shape[0], -1)
        
        # 处理动作维度
        if actions.ndim == 1:
            actions = actions.reshape(-1, 1)
        
        # 标准化
        if self.normalize:
            states = self.state_scaler.transform(states)
            actions = self.action_scaler.transform(actions)
        
        # 创建滑动窗口样本
        step = self.seq_len - self.overlap
        for i in range(0, len(states) - self.seq_len, step):
            input_states = states[i:i+self.seq_len]
            input_actions = actions[i:i+self.seq_len]
            target_states = states[i+1:i+self.seq_len+1]  # 下一个状态作为目标
            
            self.processed_sequences.append({
                'input_states': input_states.astype(np.float32),
                'input_actions': input_actions.astype(np.float32),
                'target_states': target_states.astype(np.float32),
                'task_type': seq['task_type'],
                'episode': seq['episode'],
                'node_id': seq['node_id']
            })
    
    def __len__(self) -> int:
        return len(self.processed_sequences)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.processed_sequences[idx]
        
        return {
            'input_states': torch.from_numpy(sample['input_states']),
            'input_actions': torch.from_numpy(sample['input_actions']),
            'target_states': torch.from_numpy(sample['target_states']),
            'task_type': sample['task_type'],
            'episode': sample['episode'],
            'node_id': sample['node_id']
        }
    
    def get_state_dim(self) -> int:
        """获取状态维度"""
        if self.processed_sequences:
            return self.processed_sequences[0]['input_states'].shape[-1]
        return 0
    
    def get_action_dim(self) -> int:
        """获取动作维度"""
        if self.processed_sequences:
            return self.processed_sequences[0]['input_actions'].shape[-1]
        return 0


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, data_dir: str):
        """
        初始化数据处理器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        
    def load_sequences_from_files(self, pattern: str = "*.pkl") -> List[Dict]:
        """
        从文件加载序列数据
        
        Args:
            pattern: 文件匹配模式
            
        Returns:
            序列数据列表
        """
        all_sequences = []
        
        # 查找所有匹配的pickle文件
        file_pattern = os.path.join(self.data_dir, "**", pattern)
        pickle_files = glob.glob(file_pattern, recursive=True)
        
        print(f"找到 {len(pickle_files)} 个数据文件")
        
        for file_path in pickle_files:
            try:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                if 'sequences' in data:
                    sequences = data['sequences']
                    # 展平不同任务类型的序列
                    for task_type, task_sequences in sequences.items():
                        all_sequences.extend(task_sequences)
                
                print(f"从 {file_path} 加载了 {len(sequences)} 个序列")
                
            except Exception as e:
                print(f"加载文件 {file_path} 失败: {e}")
                continue
        
        print(f"总共加载了 {len(all_sequences)} 个序列")
        return all_sequences
    
    def create_datasets(self, 
                       sequences: List[Dict],
                       seq_len: int = 50,
                       overlap: int = 10,
                       train_ratio: float = 0.8,
                       val_ratio: float = 0.1,
                       normalize: bool = True) -> Tuple[StateSequenceDataset, StateSequenceDataset, StateSequenceDataset]:
        """
        创建训练、验证和测试数据集
        
        Args:
            sequences: 序列数据
            seq_len: 序列长度
            overlap: 序列重叠长度
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            normalize: 是否标准化
            
        Returns:
            (训练集, 验证集, 测试集)
        """
        # 按episode分割数据以避免数据泄露
        episodes = list(set(seq['episode'] for seq in sequences))
        episodes.sort()
        
        n_train = int(len(episodes) * train_ratio)
        n_val = int(len(episodes) * val_ratio)
        
        train_episodes = set(episodes[:n_train])
        val_episodes = set(episodes[n_train:n_train+n_val])
        test_episodes = set(episodes[n_train+n_val:])
        
        train_sequences = [seq for seq in sequences if seq['episode'] in train_episodes]
        val_sequences = [seq for seq in sequences if seq['episode'] in val_episodes]
        test_sequences = [seq for seq in sequences if seq['episode'] in test_episodes]
        
        print(f"数据集分割:")
        print(f"  训练集: {len(train_sequences)} 序列 ({len(train_episodes)} episodes)")
        print(f"  验证集: {len(val_sequences)} 序列 ({len(val_episodes)} episodes)")
        print(f"  测试集: {len(test_sequences)} 序列 ({len(test_episodes)} episodes)")
        
        # 创建数据集
        train_dataset = StateSequenceDataset(train_sequences, seq_len, overlap, normalize)
        val_dataset = StateSequenceDataset(val_sequences, seq_len, overlap, normalize)
        test_dataset = StateSequenceDataset(test_sequences, seq_len, overlap, normalize)
        
        # 使用训练集的标准化器
        if normalize:
            val_dataset.state_scaler = train_dataset.state_scaler
            val_dataset.action_scaler = train_dataset.action_scaler
            test_dataset.state_scaler = train_dataset.state_scaler
            test_dataset.action_scaler = train_dataset.action_scaler
        
        return train_dataset, val_dataset, test_dataset
    
    def create_dataloaders(self,
                          train_dataset: StateSequenceDataset,
                          val_dataset: StateSequenceDataset,
                          test_dataset: StateSequenceDataset,
                          batch_size: int = 32,
                          num_workers: int = 4) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        创建数据加载器
        
        Args:
            train_dataset: 训练数据集
            val_dataset: 验证数据集
            test_dataset: 测试数据集
            batch_size: 批次大小
            num_workers: 工作进程数
            
        Returns:
            (训练加载器, 验证加载器, 测试加载器)
        """
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
        
        return train_loader, val_loader, test_loader
    
    def save_scalers(self, dataset: StateSequenceDataset, save_path: str):
        """保存标准化器"""
        if dataset.normalize:
            scalers = {
                'state_scaler': dataset.state_scaler,
                'action_scaler': dataset.action_scaler
            }
            with open(save_path, 'wb') as f:
                pickle.dump(scalers, f)
            print(f"标准化器已保存到: {save_path}")
    
    def load_scalers(self, load_path: str) -> Tuple[StandardScaler, StandardScaler]:
        """加载标准化器"""
        with open(load_path, 'rb') as f:
            scalers = pickle.load(f)
        return scalers['state_scaler'], scalers['action_scaler']

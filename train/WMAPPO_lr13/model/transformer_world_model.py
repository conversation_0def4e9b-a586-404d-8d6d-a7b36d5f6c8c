"""
Transformer世界模型实现
用于预测环境状态序列的Transformer模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Tuple, Optional, Dict, Any


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:x.size(0), :]


class TransformerWorldModel(nn.Module):
    """Transformer世界模型"""

    def __init__(self, 
                 state_dim: int,
                 action_dim: int,
                 d_model: int = 256,
                 nhead: int = 8,
                 num_layers: int = 4,
                 dim_feedforward: int = 2048,
                 dropout: float = 0.1,
                 max_seq_len: int = 1000):
        """
        初始化Transformer世界模型
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            d_model: 模型维度
            nhead: 注意力头数
            num_layers: Transformer层数
            dim_feedforward: 前馈网络维度
            dropout: Dropout率
            max_seq_len: 最大序列长度
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.d_model = d_model
        self.max_seq_len = max_seq_len
        
        # 输入嵌入层
        self.state_embedding = nn.Linear(state_dim, d_model)
        self.action_embedding = nn.Linear(action_dim, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model, max_seq_len)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # 输出层
        self.output_projection = nn.Linear(d_model, state_dim)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, 
                states: torch.Tensor, 
                actions: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            states: 状态序列 [batch_size, seq_len, state_dim]
            actions: 动作序列 [batch_size, seq_len, action_dim]
            mask: 注意力掩码 [batch_size, seq_len]
            
        Returns:
            预测的下一个状态 [batch_size, seq_len, state_dim]
        """
        batch_size, seq_len, _ = states.shape
        
        # 嵌入状态和动作
        state_emb = self.state_embedding(states)  # [batch_size, seq_len, d_model]
        action_emb = self.action_embedding(actions)  # [batch_size, seq_len, d_model]
        
        # 组合状态和动作嵌入
        combined_emb = state_emb + action_emb  # [batch_size, seq_len, d_model]
        
        # 添加位置编码
        combined_emb = combined_emb.transpose(0, 1)  # [seq_len, batch_size, d_model]
        combined_emb = self.pos_encoding(combined_emb)
        combined_emb = combined_emb.transpose(0, 1)  # [batch_size, seq_len, d_model]
        
        # 创建因果掩码（确保只能看到过去的信息）
        causal_mask = self._generate_square_subsequent_mask(seq_len).to(states.device)
        
        # 通过Transformer编码器
        if mask is not None:
            # 将padding mask转换为注意力掩码格式
            src_key_padding_mask = ~mask.bool()  # True表示需要忽略的位置
        else:
            src_key_padding_mask = None
        
        transformer_out = self.transformer_encoder(
            combined_emb,
            mask=causal_mask,
            src_key_padding_mask=src_key_padding_mask
        )  # [batch_size, seq_len, d_model]
        
        # 输出投影
        next_states = self.output_projection(transformer_out)  # [batch_size, seq_len, state_dim]
        
        return next_states
    
    def _generate_square_subsequent_mask(self, sz: int) -> torch.Tensor:
        """生成因果掩码"""
        mask = torch.triu(torch.ones(sz, sz), diagonal=1)
        mask = mask.masked_fill(mask == 1, float('-inf'))
        return mask
    
    def predict_next_state(self, 
                          states: torch.Tensor, 
                          actions: torch.Tensor,
                          mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        预测下一个状态
        
        Args:
            states: 历史状态序列 [batch_size, seq_len, state_dim]
            actions: 历史动作序列 [batch_size, seq_len, action_dim]
            mask: 序列掩码 [batch_size, seq_len]
            
        Returns:
            预测的下一个状态 [batch_size, state_dim]
        """
        with torch.no_grad():
            next_states = self.forward(states, actions, mask)
            # 返回序列的最后一个预测状态
            return next_states[:, -1, :]  # [batch_size, state_dim]
    
    def rollout(self, 
                initial_state: torch.Tensor,
                actions: torch.Tensor,
                horizon: int) -> torch.Tensor:
        """
        执行多步预测rollout
        
        Args:
            initial_state: 初始状态 [batch_size, state_dim]
            actions: 动作序列 [batch_size, horizon, action_dim]
            horizon: 预测步数
            
        Returns:
            预测的状态序列 [batch_size, horizon, state_dim]
        """
        batch_size = initial_state.shape[0]
        device = initial_state.device
        
        # 初始化状态序列
        states = torch.zeros(batch_size, horizon + 1, self.state_dim, device=device)
        states[:, 0] = initial_state
        
        for t in range(horizon):
            # 使用当前的状态历史和动作预测下一个状态
            current_states = states[:, :t+1]  # [batch_size, t+1, state_dim]
            current_actions = actions[:, :t+1]  # [batch_size, t+1, action_dim]
            
            # 预测下一个状态
            next_state = self.predict_next_state(current_states, current_actions)
            states[:, t+1] = next_state
        
        return states[:, 1:]  # 返回预测的状态序列，不包括初始状态


class WorldModelLoss(nn.Module):
    """世界模型损失函数"""
    
    def __init__(self, 
                 state_loss_weight: float = 1.0,
                 l2_reg_weight: float = 1e-4):
        """
        初始化损失函数
        
        Args:
            state_loss_weight: 状态预测损失权重
            l2_reg_weight: L2正则化权重
        """
        super().__init__()
        self.state_loss_weight = state_loss_weight
        self.l2_reg_weight = l2_reg_weight
        
    def forward(self, 
                predicted_states: torch.Tensor,
                target_states: torch.Tensor,
                mask: Optional[torch.Tensor] = None,
                model: Optional[nn.Module] = None) -> Dict[str, torch.Tensor]:
        """
        计算损失
        
        Args:
            predicted_states: 预测状态 [batch_size, seq_len, state_dim]
            target_states: 目标状态 [batch_size, seq_len, state_dim]
            mask: 序列掩码 [batch_size, seq_len]
            model: 模型实例（用于L2正则化）
            
        Returns:
            损失字典
        """
        # 状态预测损失（MSE）
        state_loss = F.mse_loss(predicted_states, target_states, reduction='none')
        
        if mask is not None:
            # 应用掩码
            mask = mask.unsqueeze(-1).expand_as(state_loss)
            state_loss = state_loss * mask
            state_loss = state_loss.sum() / mask.sum()
        else:
            state_loss = state_loss.mean()
        
        total_loss = self.state_loss_weight * state_loss
        
        # L2正则化
        l2_loss = 0.0
        if model is not None and self.l2_reg_weight > 0:
            for param in model.parameters():
                l2_loss += torch.norm(param, p=2)
            l2_loss = self.l2_reg_weight * l2_loss
            total_loss += l2_loss
        
        return {
            'total_loss': total_loss,
            'state_loss': state_loss,
            'l2_loss': l2_loss
        }

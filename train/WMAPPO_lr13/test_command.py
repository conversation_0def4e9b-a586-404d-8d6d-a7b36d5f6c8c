#!/usr/bin/env python3
"""
简单测试脚本，用于验证命令执行是否正常
"""

import sys
import os

print("✅ 测试脚本开始执行")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print(f"脚本路径: {__file__}")
print(f"脚本目录: {os.path.dirname(__file__)}")

# 检查是否能导入基本模块
try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")

try:
    import numpy as np
    print(f"✅ NumPy版本: {np.__version__}")
except ImportError as e:
    print(f"❌ NumPy导入失败: {e}")

print("✅ 测试脚本执行完成")

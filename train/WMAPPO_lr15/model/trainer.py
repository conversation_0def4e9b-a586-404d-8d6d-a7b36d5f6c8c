"""
Transformer世界模型训练器
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import json
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import time

from .transformer_world_model import TransformerWorldModel, WorldModelLoss
from .data_processor import StateSequenceDataset


class WorldModelTrainer:
    """世界模型训练器"""
    
    def __init__(self,
                 model: TransformerWorldModel,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 test_loader: DataLoader,
                 device: str = 'cuda',
                 save_dir: str = './world_model_checkpoints'):
        """
        初始化训练器
        
        Args:
            model: Transformer世界模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            test_loader: 测试数据加载器
            device: 设备
            save_dir: 模型保存目录
        """
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        self.device = device
        self.save_dir = save_dir
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 损失函数
        self.criterion = WorldModelLoss()
        
        # 优化器
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=1e-4,
            weight_decay=1e-4
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        # TensorBoard记录器
        self.writer = SummaryWriter(os.path.join(save_dir, 'logs'))
        
        # 训练状态
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.train_losses = []
        self.val_losses = []
        
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_state_loss = 0.0
        total_l2_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {self.epoch+1} Training')
        
        for batch in pbar:
            # 移动数据到设备
            input_states = batch['input_states'].to(self.device)
            input_actions = batch['input_actions'].to(self.device)
            target_states = batch['target_states'].to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            predicted_states = self.model(input_states, input_actions)
            
            # 计算损失
            loss_dict = self.criterion(
                predicted_states, 
                target_states, 
                model=self.model
            )
            
            # 反向传播
            loss_dict['total_loss'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 累计损失
            total_loss += loss_dict['total_loss'].item()
            total_state_loss += loss_dict['state_loss'].item()
            total_l2_loss += loss_dict['l2_loss']
            num_batches += 1
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f"{loss_dict['total_loss'].item():.4f}",
                'State': f"{loss_dict['state_loss'].item():.4f}"
            })
        
        # 计算平均损失
        avg_loss = total_loss / num_batches
        avg_state_loss = total_state_loss / num_batches
        avg_l2_loss = total_l2_loss / num_batches
        
        return {
            'total_loss': avg_loss,
            'state_loss': avg_state_loss,
            'l2_loss': avg_l2_loss
        }
    
    def validate(self) -> Dict[str, float]:
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        total_state_loss = 0.0
        total_l2_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            pbar = tqdm(self.val_loader, desc='Validation')
            
            for batch in pbar:
                # 移动数据到设备
                input_states = batch['input_states'].to(self.device)
                input_actions = batch['input_actions'].to(self.device)
                target_states = batch['target_states'].to(self.device)
                
                # 前向传播
                predicted_states = self.model(input_states, input_actions)
                
                # 计算损失
                loss_dict = self.criterion(
                    predicted_states, 
                    target_states, 
                    model=self.model
                )
                
                # 累计损失
                total_loss += loss_dict['total_loss'].item()
                total_state_loss += loss_dict['state_loss'].item()
                total_l2_loss += loss_dict['l2_loss']
                num_batches += 1
                
                # 更新进度条
                pbar.set_postfix({
                    'Loss': f"{loss_dict['total_loss'].item():.4f}",
                    'State': f"{loss_dict['state_loss'].item():.4f}"
                })
        
        # 计算平均损失
        avg_loss = total_loss / num_batches
        avg_state_loss = total_state_loss / num_batches
        avg_l2_loss = total_l2_loss / num_batches
        
        return {
            'total_loss': avg_loss,
            'state_loss': avg_state_loss,
            'l2_loss': avg_l2_loss
        }
    
    def test(self) -> Dict[str, float]:
        """测试模型"""
        self.model.eval()
        total_loss = 0.0
        total_state_loss = 0.0
        num_batches = 0
        
        predictions = []
        targets = []
        
        with torch.no_grad():
            pbar = tqdm(self.test_loader, desc='Testing')
            
            for batch in pbar:
                # 移动数据到设备
                input_states = batch['input_states'].to(self.device)
                input_actions = batch['input_actions'].to(self.device)
                target_states = batch['target_states'].to(self.device)
                
                # 前向传播
                predicted_states = self.model(input_states, input_actions)
                
                # 计算损失
                loss_dict = self.criterion(predicted_states, target_states)
                
                # 累计损失
                total_loss += loss_dict['total_loss'].item()
                total_state_loss += loss_dict['state_loss'].item()
                num_batches += 1
                
                # 保存预测和目标用于分析
                predictions.append(predicted_states.cpu().numpy())
                targets.append(target_states.cpu().numpy())
                
                # 更新进度条
                pbar.set_postfix({
                    'Loss': f"{loss_dict['total_loss'].item():.4f}",
                    'State': f"{loss_dict['state_loss'].item():.4f}"
                })
        
        # 计算平均损失
        avg_loss = total_loss / num_batches
        avg_state_loss = total_state_loss / num_batches
        
        # 计算额外的评估指标
        predictions = np.concatenate(predictions, axis=0)
        targets = np.concatenate(targets, axis=0)
        
        # 计算MAE和RMSE
        mae = np.mean(np.abs(predictions - targets))
        rmse = np.sqrt(np.mean((predictions - targets) ** 2))
        
        return {
            'total_loss': avg_loss,
            'state_loss': avg_state_loss,
            'mae': mae,
            'rmse': rmse,
            'predictions': predictions,
            'targets': targets
        }
    
    def train(self, num_epochs: int, save_every: int = 10):
        """训练模型"""
        print(f"开始训练，共 {num_epochs} 个epochs")
        print(f"设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        start_time = time.time()
        
        for epoch in range(num_epochs):
            self.epoch = epoch
            
            # 训练
            train_metrics = self.train_epoch()
            self.train_losses.append(train_metrics['total_loss'])
            
            # 验证
            val_metrics = self.validate()
            self.val_losses.append(val_metrics['total_loss'])
            
            # 学习率调度
            self.scheduler.step(val_metrics['total_loss'])
            
            # 记录到TensorBoard
            self.writer.add_scalar('Loss/Train', train_metrics['total_loss'], epoch)
            self.writer.add_scalar('Loss/Validation', val_metrics['total_loss'], epoch)
            self.writer.add_scalar('Loss/Train_State', train_metrics['state_loss'], epoch)
            self.writer.add_scalar('Loss/Validation_State', val_metrics['state_loss'], epoch)
            self.writer.add_scalar('Learning_Rate', self.optimizer.param_groups[0]['lr'], epoch)
            
            # 打印进度
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {train_metrics['total_loss']:.6f}")
            print(f"  Val Loss: {val_metrics['total_loss']:.6f}")
            print(f"  LR: {self.optimizer.param_groups[0]['lr']:.2e}")
            
            # 保存最佳模型
            if val_metrics['total_loss'] < self.best_val_loss:
                self.best_val_loss = val_metrics['total_loss']
                self.save_checkpoint('best_model.pth', is_best=True)
                print(f"  新的最佳模型已保存 (Val Loss: {self.best_val_loss:.6f})")
            
            # 定期保存检查点
            if (epoch + 1) % save_every == 0:
                self.save_checkpoint(f'checkpoint_epoch_{epoch+1}.pth')
                print(f"  检查点已保存")
            
            print()
        
        # 训练完成
        total_time = time.time() - start_time
        print(f"训练完成! 总用时: {total_time/3600:.2f} 小时")
        
        # 最终测试
        print("开始最终测试...")
        test_metrics = self.test()
        print(f"测试结果:")
        print(f"  Test Loss: {test_metrics['total_loss']:.6f}")
        print(f"  MAE: {test_metrics['mae']:.6f}")
        print(f"  RMSE: {test_metrics['rmse']:.6f}")
        
        # 保存最终模型
        self.save_checkpoint('final_model.pth')
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'test_metrics': test_metrics
        }
    
    def save_checkpoint(self, filename: str, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'model_config': {
                'state_dim': self.model.state_dim,
                'action_dim': self.model.action_dim,
                'd_model': self.model.d_model,
                'max_seq_len': self.model.max_seq_len
            }
        }
        
        filepath = os.path.join(self.save_dir, filename)
        torch.save(checkpoint, filepath)
        
        if is_best:
            # 同时保存为best模型
            best_path = os.path.join(self.save_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
    
    def load_checkpoint(self, filename: str):
        """加载检查点"""
        filepath = os.path.join(self.save_dir, filename)
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.epoch = checkpoint['epoch']
        self.best_val_loss = checkpoint['best_val_loss']
        self.train_losses = checkpoint['train_losses']
        self.val_losses = checkpoint['val_losses']
        
        print(f"检查点已加载: {filepath}")
        print(f"Epoch: {self.epoch}, Best Val Loss: {self.best_val_loss:.6f}")

"""
世界模型训练完整流水线
按照用户要求依次执行：数据收集 -> 模型训练 -> 集成测试
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime


# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


def run_command(command, description, cwd=None):
    """
    运行命令并处理输出

    Args:
        command: 要执行的命令 (字符串或列表)
        description: 命令描述
        cwd: 工作目录
    """
    print(f"\n{'='*60}")
    print(f"开始执行: {description}")

    # 如果command是字符串，转换为列表
    if isinstance(command, str):
        command_list = command.split()
        print(f"命令: {command}")
    else:
        command_list = command
        print(f"命令: {' '.join(command)}")

    print(f"工作目录: {cwd if cwd else '当前目录'}")
    print(f"{'='*60}")

    start_time = time.time()

    try:
        # 确保工作目录存在
        if cwd and not os.path.exists(cwd):
            print(f"❌ 工作目录不存在: {cwd}")
            return False

        result = subprocess.run(
            command_list,
            cwd=cwd if cwd else None,
            # capture_output=True,
            text=True,
            stdout = sys.stdout,
            stderr = sys.stderr,
            # timeout=3600  # 1小时超时
        )

        end_time = time.time()
        duration = end_time - start_time

        print(f"\n执行完成，耗时: {duration:.2f}秒")

        if result.returncode == 0:
            print("✅ 执行成功")
            # if result.stdout:
            #     print("输出:")
            #     print(result.stdout)
        else:
            print("❌ 执行失败")
            print(f"返回码: {result.returncode}")
            # if result.stdout:
            #     print("标准输出:")
            #     print(result.stdout)
            # if result.stderr:
            #     print("错误信息:")
            #     print(result.stderr)
            print('position:1',result)
            return False

    except subprocess.TimeoutExpired:
        print("❌ 执行超时")
        print('position:2', result)
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        import traceback
        traceback.print_exc()
        print('position:3', result)
        return False

    return True


def step1_collect_data(args):
    """步骤1: 收集不同负载配置下的数据"""
    print(f"\n🚀 步骤1: 数据收集")
    print(f"将使用不同的任务负载配置收集状态序列数据")
    print(f"数据保存目录: {args.data_dir}")

    # 数据收集脚本路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(current_dir, 'data_collection', 'collect_data.py')

    # 检查脚本是否存在
    if not os.path.exists(script_path):
        print(f"❌ 数据收集脚本不存在: {script_path}")
        return False

    # 构建命令列表
    # command = ["python3", script_path] sys.executable,
    command = [sys.executable, script_path] # 使用当前Python解释器

    # 执行数据收集
    current_dir = os.path.dirname(os.path.abspath(__file__))
    success = run_command(
        command=command,
        description="收集多配置状态序列数据",
        cwd=current_dir
    )

    if success:
        print("✅ 数据收集完成")
        # 检查数据文件
        data_dir = "./world_model_data"
        if os.path.exists(data_dir):
            file_count = sum(len(files) for _, _, files in os.walk(data_dir))
            print(f"数据目录: {data_dir}")
            print(f"生成文件数: {file_count}")
        return True
    else:
        print('success:',success)
        print("❌ 数据收集失败")
        return False


def step2_train_world_model(args):
    """步骤2: 训练Transformer世界模型"""
    print(f"\n🚀 步骤2: 训练Transformer世界模型")
    print(f"使用收集的数据训练世界模型")

    # 训练脚本路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(current_dir, 'train_world_model.py')

    # 检查脚本是否存在
    if not os.path.exists(script_path):
        print(f"❌ 训练脚本不存在: {script_path}")
        return False

    # 构建训练命令列表
    command = [
        # "python3", script_path,
        sys.executable, script_path,
        "--data_dir", args.data_dir,
        "--seq_len", str(args.seq_len),
        "--batch_size", str(args.batch_size),
        "--num_epochs", str(args.num_epochs),
        "--d_model", str(args.d_model),
        "--nhead", str(args.nhead),
        "--num_layers", str(args.num_layers),
        "--learning_rate", str(args.learning_rate),
        "--device", args.device,
        "--save_dir", args.save_dir,
        "--save_every", str(args.save_every)
    ]

    if args.normalize:
        command.append("--normalize")

    # 执行训练
    current_dir = os.path.dirname(os.path.abspath(__file__))
    success = run_command(
        command=command,
        description="训练Transformer世界模型",
        cwd=current_dir
    )

    if success:
        print("✅ 世界模型训练完成")
        # 检查模型文件
        if os.path.exists(args.save_dir):
            model_files = [f for f in os.listdir(args.save_dir) if f.endswith('.pth')]
            print(f"模型保存目录: {args.save_dir}")
            print(f"生成模型文件: {model_files}")
        return True
    else:
        print("❌ 世界模型训练失败")
        return False


def step3_test_integration(args):
    """步骤3: 测试世界模型集成"""
    print(f"\n🚀 步骤3: 测试世界模型集成")
    print(f"测试世界模型与TransMAPPO的集成")
    
    # 创建测试脚本
    test_script_content = f'''
import os
import sys
import yaml
import colorama
from pyinstrument import Profiler
from torch.utils.tensorboard import SummaryWriter

sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from mbrl_strategy import MBRLStrategyModule
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from spacetasksim.utils import file_util

# 加载配置 
config_path = os.path.join(os.path.dirname(__file__), '../config.yaml')
config = file_util.load_config(config_path)

last_episode = 39
max_episode = 100
checkpoint=50

# 启用tensorboard记录
writer = SummaryWriter("./logs")

# 创建环境和策略
env = SpaceTaskSimEnv(config)

# 测试世界模型路径
world_model_path = os.path.join("{args.save_dir}", "best_model.pth")
if not os.path.exists(world_model_path):
    world_model_path = None
    print("警告: 未找到训练好的世界模型，将使用原始策略")

sche_strategy_module = MBRLStrategyModule(
    world_model_path=world_model_path,
    use_world_model=world_model_path is not None
)

evaluation_module = SpaceTaskSimEvaluation(env, sche_strategy_module)
# 初始化策略
sche_strategy_module.initialize(env, last_episode=last_episode, final=True)

print("开始集成测试...")
for episode in range(last_episode + 1, max_episode + 1):
    # 启动episode性能监控
    # episode_profiler.start()

    while not env.isDone():
        sche_strategy_module.scheduleStep()
        env.step()
        sche_strategy_module.updateExperience()
        evaluation_module.evaluateStep()
        evaluation_module.recordStepInfo()
    evaluation_module.stepInfoToFile(episode)
    evaluation_module.drawStepInfoByFile(episode)
    evaluation_module.episodeInfoToFile(episode)
    evaluation_module.drawEpisodeInfoByFile()

    avg_reward = sche_strategy_module.getTerminatedTaskAvgReward()
    sum_reward= sche_strategy_module.getTerminatedTaskSumReward()
    succ_ratio = evaluation_module.getCompletionRatio()

    sche_strategy_module.train()  # 训练模型，交互结束后再训练

    sche_strategy_module.saveModel(episode, final=True, succ_ratio=succ_ratio)  # 保存模型
    if episode % checkpoint == 0:
        sche_strategy_module.saveModel(episode, final=False, succ_ratio=succ_ratio)

    # 记录训练效果
    writer.add_scalar("avg_reward", avg_reward, episode)
    writer.add_scalar("sum_reward", sum_reward, episode)
    writer.add_scalar("succ_ratio", succ_ratio, episode)

    # reset
    evaluation_module.resetStepEvaluation()

    env.reset() # 输出预存信息时不要reset
    sche_strategy_module.reset()
    # # 结束性能监控并打印报告
    # episode_profiler.stop()
    # episode_profiler.print()
    # # # 重置性能监控
    # episode_profiler.reset()

env.close()

print(f"\\n集成测试完成:")
print(f"  总步数: {{step_count}}")
print(f"  平均奖励: {{avg_reward:.4f}}")
print(f"  成功率: {{succ_ratio:.4f}}")
print(f"  世界模型状态: {{'已加载' if strategy_module.use_world_model else '未使用'}}")

if strategy_module.use_world_model:
    print("✅ 世界模型集成测试成功")
else:
    print("⚠️  使用原始策略完成测试")
'''
    
    # 保存测试脚本
    current_dir = os.path.dirname(os.path.abspath(__file__))
    test_script_path = os.path.join(current_dir, 'test_integration.py')
    with open(test_script_path, 'w') as f:
        f.write(test_script_content)
    
    # 执行测试
    # command = ["python3", test_script_path]
    command = [sys.executable, test_script_path]


    current_dir = os.path.dirname(os.path.abspath(__file__))
    success = run_command(
        command=command,
        description="测试世界模型集成",
        cwd=current_dir
    )
    
    # 清理测试脚本
    if os.path.exists(test_script_path):
        os.remove(test_script_path)
    
    if success:
        print("✅ 集成测试完成")
        return True
    else:
        print("❌ 集成测试失败")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='世界模型训练完整流水线')
    
    # 流水线控制参数
    parser.add_argument('--skip_data_collection', action='store_true',
                       help='跳过数据收集步骤')
    parser.add_argument('--skip_training', action='store_true',
                       help='跳过模型训练步骤')
    parser.add_argument('--skip_testing', action='store_true',
                       help='跳过集成测试步骤')
    
    # 数据相关参数
    parser.add_argument('--data_dir', type=str, default='../WMAPPO/world_model_data',
                       help='数据目录路径')
    parser.add_argument('--seq_len', type=int, default=50,
                       help='输入序列长度')
    parser.add_argument('--normalize', action='store_true', default=True,
                       help='是否标准化数据')
    
    # 训练相关参数
    parser.add_argument('--batch_size', type=int, default=32,
                       help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=50,
                       help='训练轮数')
    parser.add_argument('--d_model', type=int, default=256,
                       help='Transformer模型维度')
    parser.add_argument('--nhead', type=int, default=8,
                       help='注意力头数')
    parser.add_argument('--num_layers', type=int, default=4,
                       help='Transformer层数')
    parser.add_argument('--learning_rate', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--device', type=str, default='cuda',
                       help='训练设备')
    parser.add_argument('--save_dir', type=str, default='../WMAPPO/world_model_checkpoints',
                       help='模型保存目录')
    parser.add_argument('--save_every', type=int, default=10,
                       help='每多少个epoch保存一次模型')
    
    args = parser.parse_args()
    
    print(f"🌟 世界模型训练流水线开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"参数配置:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")
    
    start_time = time.time()
    success_steps = 0
    total_steps = 3
    
    # # 步骤1: 数据收集
    # if not args.skip_data_collection:
    #     if step1_collect_data(args):
    #         success_steps += 1
    #     else:
    #         print("❌ 数据收集失败，流水线终止")
    #         return
    # else:
    #     print("⏭️  跳过数据收集步骤")
    #     success_steps += 1
    #
    # # 步骤2: 模型训练
    # if not args.skip_training:
    #     if step2_train_world_model(args):
    #         success_steps += 1
    #     else:
    #         print("❌ 模型训练失败，流水线终止")
    #         return
    # else:
    #     print("⏭️  跳过模型训练步骤")
    #     success_steps += 1
    
    # 步骤3: 集成测试
    if not args.skip_testing:
        if step3_test_integration(args):
            success_steps += 1
        else:
            print("⚠️  集成测试失败，但流水线继续")
    else:
        print("⏭️  跳过集成测试步骤")
        success_steps += 1
    
    # 总结
    end_time = time.time()
    total_duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print(f"🎉 世界模型训练流水线完成")
    print(f"总耗时: {total_duration/3600:.2f} 小时")
    print(f"成功步骤: {success_steps}/{total_steps}")
    
    if success_steps == total_steps:
        print("✅ 所有步骤执行成功!")
        print(f"\n📁 生成的文件:")
        print(f"  - 数据目录: {args.data_dir}")
        print(f"  - 模型目录: {args.save_dir}")
        print(f"\n🚀 现在可以使用训练好的世界模型进行基于模型的强化学习了!")
    else:
        print("⚠️  部分步骤执行失败，请检查错误信息")
    
    print(f"{'='*60}")


if __name__ == "__main__":
    main()

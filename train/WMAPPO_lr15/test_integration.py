
import os
import sys
import yaml
import colorama
from pyinstrument import Profiler
from torch.utils.tensorboard import SummaryWriter

sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from mbrl_strategy import MBRLStrategyModule
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from spacetasksim.utils import file_util

# 加载配置
config_path = os.path.join(os.path.dirname(__file__), '../config.yaml')
config = file_util.load_config(config_path)

last_episode = 50
max_episode = 200
checkpoint=50

# 启用tensorboard记录
writer = SummaryWriter("./logs")

# 创建环境和策略
env = SpaceTaskSimEnv(config)

# 测试世界模型路径
world_model_path = os.path.join("./world_model_checkpoints", "best_model.pth")
if not os.path.exists(world_model_path):
    world_model_path = None
    print("警告: 未找到训练好的世界模型，将使用原始策略")

sche_strategy_module = MBRLStrategyModule(
    world_model_path=world_model_path,
    use_world_model=world_model_path is not None
)

evaluation_module = SpaceTaskSimEvaluation(env, sche_strategy_module)

# 初始化策略
sche_strategy_module.initialize(env, last_episode=last_episode, final=False)

print("开始集成测试...")
for episode in range(last_episode + 1, max_episode + 1):
    # 启动episode性能监控
    # episode_profiler.start()

    while not env.isDone():
        sche_strategy_module.scheduleStep()
        env.step()
        sche_strategy_module.updateExperience()
        evaluation_module.evaluateStep()
        evaluation_module.recordStepInfo()
    evaluation_module.stepInfoToFile(episode)
    evaluation_module.drawStepInfoByFile(episode)
    evaluation_module.episodeInfoToFile(episode)
    evaluation_module.drawEpisodeInfoByFile()

    avg_reward = sche_strategy_module.getTerminatedTaskSumReward()
    sum_reward= sche_strategy_module.getTerminatedTaskSumReward()
    succ_ratio = evaluation_module.getCompletionRatio()

    sche_strategy_module.train()  # 训练模型，交互结束后再训练

    sche_strategy_module.saveModel(episode, final=True, succ_ratio=succ_ratio)  # 保存模型
    if episode % checkpoint == 0:
        sche_strategy_module.saveModel(episode, final=False, succ_ratio=succ_ratio)

    # 记录训练效果
    writer.add_scalar("avg_reward", avg_reward, episode)
    writer.add_scalar("sum_reward", sum_reward, episode)
    writer.add_scalar("succ_ratio", succ_ratio, episode)

    # reset
    evaluation_module.resetStepEvaluation()

    env.reset() # 输出预存信息时不要reset
    sche_strategy_module.reset()
    # # 结束性能监控并打印报告
    # episode_profiler.stop()
    # episode_profiler.print()
    # # # 重置性能监控
    # episode_profiler.reset()

env.close()

print(f"\n集成测试完成:")
print(f"  总步数: {step_count}")
print(f"  平均奖励: {avg_reward:.4f}")
print(f"  成功率: {succ_ratio:.4f}")
print(f"  世界模型状态: {'已加载' if strategy_module.use_world_model else '未使用'}")

if strategy_module.use_world_model:
    print("✅ 世界模型集成测试成功")
else:
    print("⚠️  使用原始策略完成测试")

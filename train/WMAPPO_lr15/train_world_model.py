"""
训练Transformer世界模型的主脚本
"""

import os
import sys
import torch
import argparse
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from model.transformer_world_model import TransformerWorldModel
from model.data_processor import DataProcessor
from model.trainer import WorldModelTrainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练Transformer世界模型')
    
    # 数据相关参数
    parser.add_argument('--data_dir', type=str, default='./world_model_data',
                       help='数据目录路径')
    parser.add_argument('--seq_len', type=int, default=50,
                       help='输入序列长度')
    parser.add_argument('--overlap', type=int, default=10,
                       help='序列重叠长度')
    parser.add_argument('--normalize', action='store_true', default=True,
                       help='是否标准化数据')
    
    # 模型相关参数
    parser.add_argument('--d_model', type=int, default=256,
                       help='Transformer模型维度')
    parser.add_argument('--nhead', type=int, default=8,
                       help='注意力头数')
    parser.add_argument('--num_layers', type=int, default=4,
                       help='Transformer层数')
    parser.add_argument('--dim_feedforward', type=int, default=2048,
                       help='前馈网络维度')
    parser.add_argument('--dropout', type=float, default=0.1,
                       help='Dropout率')
    
    # 训练相关参数
    parser.add_argument('--batch_size', type=int, default=32,
                       help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=50,
                       help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                       help='权重衰减')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载器工作进程数')
    
    # 其他参数
    parser.add_argument('--device', type=str, default='cuda',
                       help='训练设备')
    parser.add_argument('--save_dir', type=str, default='./world_model_checkpoints',
                       help='模型保存目录')
    parser.add_argument('--save_every', type=int, default=10,
                       help='每多少个epoch保存一次模型')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的检查点路径')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 设置设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA不可用，使用CPU")
        args.device = 'cpu'
    
    print(f"使用设备: {args.device}")
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 保存训练配置
    config_path = os.path.join(args.save_dir, 'training_config.json')
    with open(config_path, 'w') as f:
        json.dump(vars(args), f, indent=2)
    print(f"训练配置已保存到: {config_path}")
    
    # 数据处理
    print("开始加载和处理数据...")
    data_processor = DataProcessor(args.data_dir)
    
    # 加载序列数据
    sequences = data_processor.load_sequences_from_files()
    if not sequences:
        print("错误: 没有找到有效的序列数据")
        return
    
    print(f"加载了 {len(sequences)} 个序列")
    
    # 创建数据集
    train_dataset, val_dataset, test_dataset = data_processor.create_datasets(
        sequences=sequences,
        seq_len=args.seq_len,
        overlap=args.overlap,
        normalize=args.normalize
    )
    
    print(f"数据集大小:")
    print(f"  训练集: {len(train_dataset)} 样本")
    print(f"  验证集: {len(val_dataset)} 样本")
    print(f"  测试集: {len(test_dataset)} 样本")
    
    # 获取数据维度
    state_dim = train_dataset.get_state_dim()
    action_dim = train_dataset.get_action_dim()
    
    print(f"数据维度:")
    print(f"  状态维度: {state_dim}")
    print(f"  动作维度: {action_dim}")
    
    # 保存标准化器
    if args.normalize:
        scaler_path = os.path.join(args.save_dir, 'scalers.pkl')
        data_processor.save_scalers(train_dataset, scaler_path)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = data_processor.create_dataloaders(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        test_dataset=test_dataset,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )
    
    # 创建模型
    print("创建Transformer世界模型...")
    model = TransformerWorldModel(
        state_dim=state_dim,
        action_dim=action_dim,
        d_model=args.d_model,
        nhead=args.nhead,
        num_layers=args.num_layers,
        dim_feedforward=args.dim_feedforward,
        dropout=args.dropout,
        max_seq_len=args.seq_len
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = WorldModelTrainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        test_loader=test_loader,
        device=args.device,
        save_dir=args.save_dir
    )
    
    # 更新优化器参数
    trainer.optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=args.weight_decay
    )
    
    # 恢复训练（如果指定）
    if args.resume:
        print(f"从检查点恢复训练: {args.resume}")
        trainer.load_checkpoint(args.resume)
    
    # 开始训练
    print("开始训练...")
    training_results = trainer.train(
        num_epochs=args.num_epochs,
        save_every=args.save_every
    )
    
    # 保存训练结果
    results_path = os.path.join(args.save_dir, 'training_results.json')
    with open(results_path, 'w') as f:
        # 转换numpy数组为列表以便JSON序列化
        serializable_results = {
            'train_losses': training_results['train_losses'],
            'val_losses': training_results['val_losses'],
            'test_metrics': {
                k: float(v) if isinstance(v, (int, float)) else v.tolist() if hasattr(v, 'tolist') else v
                for k, v in training_results['test_metrics'].items()
                if k not in ['predictions', 'targets']  # 排除大数组
            }
        }
        json.dump(serializable_results, f, indent=2)
    
    print(f"训练结果已保存到: {results_path}")
    print("训练完成!")


if __name__ == "__main__":
    main()

"""
世界模型模块
基于Transformer的世界模型训练和集成系统
"""

__version__ = "1.0.0"
__author__ = "SpaceTaskSim Team"

# 导入主要类和函数
from .mbrl_strategy import MBRLStrategyModule
from .model.transformer_world_model import TransformerWorldModel, WorldModelLoss
from .model.data_processor import DataProcessor, StateSequenceDataset
from .model.trainer import WorldModelTrainer
from .data_collection.state_collector import StateSequenceCollector, MultiConfigDataCollector
from .strategy import MAPPOStrategyModule

__all__ = [
    'MBRLStrategyModule',
    'TransformerWorldModel',
    'WorldModelLoss',
    'DataProcessor',
    'StateSequenceDataset',
    'WorldModelTrainer',
    'StateSequenceCollector',
    'MultiConfigDataCollector',
    'MAPPOStrategyModule'
]

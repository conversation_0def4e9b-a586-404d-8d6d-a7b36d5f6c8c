"""
数据收集主脚本
使用不同配置运行TransMAPPO环境，收集状态序列数据用于训练世界模型
"""
import gc
import os
import sys

import psutil
import yaml
import copy
import colorama
from pyinstrument import Profiler

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from ..data_collect_strategy import MAPPOStrategyModule
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from spacetasksim.utils import file_util

from state_collector import MultiConfigDataCollector, StateSequenceCollector

# import tracemalloc
# # 启动内存跟踪
# tracemalloc.start()

colorama.init(autoreset=True)


# def print_memory_snapshot():
#     snapshot = tracemalloc.take_snapshot()  # 获取内存快照
#     top_stats = snapshot.statistics('lineno')  # 统计内存按行号的使用情况
#
#     print("[ Top 10 Memory Consumers ]")
#     for stat in top_stats[:10]:
#         print(stat)

def modify_config_for_variant(base_config:dict, variant:dict) -> dict:
    """
    根据变体配置修改基础配置

    Args:
        base_config: 基础配置字典
        variant: 变体配置字典

    Returns:
        修改后的配置字典
    """
    config = copy.deepcopy(base_config)

    # 修改任务配置
    if 'task_modifications' in variant:
        for task_type, modifications in variant['task_modifications'].items():
            if task_type in config['config_task']:
                config['config_task'][task_type].update(modifications)

    # 修改资源配置
    if 'resource_modifications' in variant:
        for resource_type, modifications in variant['resource_modifications'].items():
            if resource_type in config['config_resource']:
                # 深度更新嵌套字典
                def deep_update(target_dict, update_dict):
                    for key, value in update_dict.items():
                        if key in target_dict and isinstance(target_dict[key], dict) and isinstance(value, dict):
                            deep_update(target_dict[key], value)
                        else:
                            target_dict[key] = value

                deep_update(config['config_resource'][resource_type], modifications)

    return config


def collect_data_for_config(config: dict, config_name: str, episodes: int = 10, 
                          max_simulation_time: int = 200, save_dir: str = "./world_model_data"):
    """
    为指定配置收集数据
    
    Args:
        config: 环境配置
        config_name: 配置名称
        episodes: 运行的episode数量
        max_simulation_time: 每个episode的最大仿真时间
        save_dir: 数据保存目录
    """
    print(f"\n{'='*50}")
    print(f"开始收集配置 '{config_name}' 的数据")
    print(f"Episodes: {episodes}, 仿真时间: {max_simulation_time}s")
    print(f"{'='*50}")
    
    # 创建数据收集器
    collector = StateSequenceCollector(os.path.join(save_dir, config_name))
    
    # 修改配置中的仿真时间
    config['config_simulation']['max_simulation_time'] = max_simulation_time
    
    # 初始化环境和策略
    env = SpaceTaskSimEnv(config)
    strategy_module = MAPPOStrategyModule()
    evaluation_module = SpaceTaskSimEvaluation(env, strategy_module)
    
    # 不加载预训练模型，使用随机策略
    strategy_module.initialize(env, last_episode=None, final=False)
    
    for episode in range(1, episodes + 1):
        print(f"\n--- Episode {episode}/{episodes} ---")
        
        step_count = 0
        while not env.isDone():
            strategy_module.scheduleStep()
            env.step()
            strategy_module.updateExperience()
            evaluation_module.evaluateStep()
            evaluation_module.recordStepInfo()
            step_count += 1

        
        # 从经验缓冲区收集数据
        config_info = {
            'config_name': config_name,
            'episode': episode,
            'max_simulation_time': max_simulation_time,
            'total_steps': step_count,
            'final_time': env.simulation_time
        }
        
        collector.collect_from_exp_buffer(strategy_module.exp_buffer, episode, config_info)
        
        # 获取episode统计信息
        avg_reward = strategy_module.getTerminatedTaskSumReward()
        succ_ratio = evaluation_module.getCompletionRatio()
        
        print(f"  Episode {episode} 完成:")
        print(f"    总步数: {step_count}")
        print(f"    平均奖励: {avg_reward:.4f}")
        print(f"    成功率: {succ_ratio:.4f}")
        print(f"    收集序列数: {collector.metadata['total_sequences']}")
        
        # 每2个episode保存一次数据
        # if episode % 2 == 0:
        #     collector.save_sequences(f"{config_name}_episode_{episode}")
        #     collector.clear_sequences()  # 清空已保存的数据以节省内存
        collector.save_sequences(f"{config_name}_episode_{episode}")
        collector.clear_sequences()  # 清空已保存的数据以节省内存


        # 重置环境
        print(f"Memory before clear: {psutil.Process().memory_info().rss / 1024 / 1024:.2f} MB")
        evaluation_module.resetStepEvaluation()
        env.reset()
        strategy_module.reset()
        gc.collect()  # 强制垃圾回收
        print(f"Memory after clear: {psutil.Process().memory_info().rss / 1024 / 1024:.2f} MB")

        # print_memory_snapshot()
    
    # 保存最终数据
    if collector.metadata['total_sequences'] > 0:
        collector.save_sequences(f"{config_name}_final")
    
    # 打印统计信息
    stats = collector.get_statistics()
    print(f"\n配置 '{config_name}' 数据收集完成:")
    print(f"  总序列数: {stats['total_sequences']}")
    print(f"  平均序列长度: {stats['avg_sequence_length']:.2f}")
    print(f"  各任务类型序列数: {stats['sequences_by_task_type']}")




def main():
    """主函数"""
    # 配置参数
    base_config_path = os.path.join(os.path.dirname(__file__), '../../config.yaml')
    save_dir = "./world_model_data"
    episodes_per_config = 20  # 每个配置运行的episode数
    max_simulation_time = 200  # 每个episode的仿真时间
    
    # 加载基础配置
    if not os.path.exists(base_config_path):
        print(f"错误: 配置文件不存在: {base_config_path}")
        return
    
    base_config = file_util.load_config(base_config_path)
    
    # 创建多配置数据收集器
    multi_collector = MultiConfigDataCollector(base_config_path, save_dir)
    config_variants = multi_collector.create_config_variants()
    
    print(f"将收集 {len(config_variants)} 种配置的数据:")
    for variant in config_variants:
        print(f"  - {variant['name']}: {variant['description']}")
    
    # 为每种配置收集数据
    for variant in config_variants:
        try:
            # 修改配置
            modified_config = modify_config_for_variant(base_config, variant)
            
            # 收集数据
            collect_data_for_config(
                config=modified_config,
                config_name=variant['name'],
                episodes=episodes_per_config,
                max_simulation_time=max_simulation_time,
                save_dir=save_dir
            )
            
        except Exception as e:
            print(f"配置 '{variant['name']}' 数据收集失败: {e}")
            continue
    
    print(f"\n{'='*50}")
    print("所有配置的数据收集完成!")
    print(f"数据保存在: {save_dir}")
    print(f"{'='*50}")


if __name__ == "__main__":
    main()

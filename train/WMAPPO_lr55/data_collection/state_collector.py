"""
状态序列数据收集器
用于从TransMAPPO的exp_buffer中收集combined_obs_space序列数据
"""
import copy
import gc
import numpy as np
import pandas as pd
import pickle
import os
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import json
from datetime import datetime
import psutil

from spacetasksim.enum.task_type_enum import TaskTypeEnum


class StateSequenceCollector:
    """收集和管理状态序列数据的类"""
    
    def __init__(self, save_dir: str = "./world_model_data"):
        """
        初始化状态序列收集器
        
        Args:
            save_dir: 数据保存目录
        """
        self.save_dir = save_dir
        self.sequences = {task_type: [] for task_type in TaskTypeEnum}
        self.metadata = {
            'collection_start_time': datetime.now().isoformat(),
            'total_sequences': 0,
            'sequence_lengths': [],
            'config_info': {}
        }
        
        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        
    def collect_from_exp_buffer(self, exp_buffer, episode: int, config_info: Dict = None):
        """
        从经验缓冲区收集状态序列
        
        Args:
            exp_buffer: MAPPOStrategyModule的ReplayBuffer实例
            episode: 当前episode编号
            config_info: 配置信息
        """
        if config_info:
            self.metadata['config_info'] = config_info
            
        buffer_data = exp_buffer.get()

        print(f"Memory: {psutil.Process().memory_info().rss / 1024 / 1024:.2f} MB")

        for task_type, exp_dicts in buffer_data.items():
            for node_id, exp_dict in exp_dicts.items():
                # 按task_id排序以保证时间顺序
                sorted_exps = sorted(exp_dict.items(), key=lambda x: x[0])
                # sorted_exps=list(exp_dict.items())
                
                if len(sorted_exps) < 2:  # 至少需要2个状态才能形成序列
                    continue
                    
                sequence = {
                    'episode': episode,
                    'task_type': task_type.name,
                    'node_id': node_id,
                    'states': [],
                    'actions': [],
                    'rewards': [],
                    'action_masks': [],
                    'agent_masks': [],
                    'timestamps': []
                }
                
                for task_id, exp in sorted_exps:
                    if exp['combined_states'] is not None:
                        node_idx=exp['agent_idx']
                        sequence['states'].append(copy.deepcopy(exp['combined_states'][node_idx,:]))
                        sequence['actions'].append(exp['action'] if exp['action'] is not None else -1)
                        sequence['rewards'].append(exp['reward'] if exp['reward'] is not None else 0.0)
                        sequence['action_masks'].append(copy.deepcopy(exp['action_mask']) if exp['action_mask'] is not None else [])
                        sequence['agent_masks'].append(exp['agent_mask'] if exp['agent_mask'] is not None else False)
                        sequence['timestamps'].append(task_id)  # 使用task_id作为时间戳
                
                if len(sequence['states']) >= 2:  # 确保序列长度足够
                    self.sequences[task_type].append(sequence)
                    self.metadata['sequence_lengths'].append(len(sequence['states']))
        
        self.metadata['total_sequences'] = sum(len(seqs) for seqs in self.sequences.values())
        print(f"Episode {episode}: 收集到 {self.metadata['total_sequences']} 个状态序列")

        print(f"Memory: {psutil.Process().memory_info().rss / 1024 / 1024:.2f} MB")
        
    def save_sequences(self, filename_prefix: str = "state_sequences"):
        """
        保存收集的状态序列到文件
        
        Args:
            filename_prefix: 文件名前缀
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存为pickle格式（用于训练）
        pickle_file = os.path.join(self.save_dir, f"{filename_prefix}_{timestamp}.pkl")
        with open(pickle_file, 'wb') as f:
            pickle.dump({
                'sequences': self.sequences,
                'metadata': self.metadata
            }, f)
        
        # 保存元数据为JSON
        metadata_file = os.path.join(self.save_dir, f"{filename_prefix}_metadata_{timestamp}.json")
        with open(metadata_file, 'w') as f:
            json.dump(self.metadata, f, indent=2)
        
        # 保存统计信息为CSV
        stats_data = []
        for task_type, sequences in self.sequences.items():
            for seq in sequences:
                stats_data.append({
                    'task_type': task_type.name,
                    'episode': seq['episode'],
                    'node_id': seq['node_id'],
                    'sequence_length': len(seq['states']),
                    'avg_reward': np.mean(seq['rewards']) if seq['rewards'] else 0.0,
                    'total_reward': np.sum(seq['rewards']) if seq['rewards'] else 0.0
                })
        
        if stats_data:
            stats_df = pd.DataFrame(stats_data)
            stats_file = os.path.join(self.save_dir, f"{filename_prefix}_stats_{timestamp}.csv")
            stats_df.to_csv(stats_file, index=False)
        
        print(f"数据已保存到:")
        print(f"  - 序列数据: {pickle_file}")
        print(f"  - 元数据: {metadata_file}")
        print(f"  - 统计信息: {stats_file}")
        
        return pickle_file, metadata_file, stats_file
    
    def clear_sequences(self):
        """清空已收集的序列数据"""
        self.sequences = {task_type: [] for task_type in TaskTypeEnum}
        self.metadata['total_sequences'] = 0
        self.metadata['sequence_lengths'] = []
        gc.collect()
        
    def get_statistics(self) -> Dict:
        """获取收集数据的统计信息"""
        stats = {
            'total_sequences': self.metadata['total_sequences'],
            'sequences_by_task_type': {task_type.name: len(seqs) for task_type, seqs in self.sequences.items()},
            'avg_sequence_length': np.mean(self.metadata['sequence_lengths']) if self.metadata['sequence_lengths'] else 0,
            'max_sequence_length': max(self.metadata['sequence_lengths']) if self.metadata['sequence_lengths'] else 0,
            'min_sequence_length': min(self.metadata['sequence_lengths']) if self.metadata['sequence_lengths'] else 0
        }
        return stats


class MultiConfigDataCollector:
    """多配置数据收集器，用于收集不同负载配置下的数据"""
    
    def __init__(self, base_config_path: str, save_dir: str = "./world_model_data"):
        """
        初始化多配置数据收集器
        
        Args:
            base_config_path: 基础配置文件路径
            save_dir: 数据保存目录
        """
        self.base_config_path = base_config_path
        self.save_dir = save_dir
        self.collectors = {}
        
        
    def create_config_variants(self) -> list:
        """创建配置变体（复制自state_collector.py）"""
        variants = [
            {
                'name': 'low_load',
                'description': '低负载配置 (参考train_low)',
                'task_modifications': {
                    'communication': {
                        'task_size': [10000, 50000],
                        'task_requirement_unit': 0.0005,
                        'task_TTL_range': [10, 20]
                    },
                    'computation': {
                        'task_size': [1000, 5000],
                        'task_requirement_unit': 0.00006,
                        'task_TTL_range': [10, 20]
                    },
                    'sensing': {
                        'sensing_time': [11, 22],
                        'sensing_interval': [240, 120, 120, 120, 90]
                    }
                },
                'resource_modifications': {
                    'config_computation': {
                        'cycle_per_byte': 17,
                        'satellite': {
                            'cpu': 4
                        }
                    },
                    'config_sensing': {
                        'camera': 6
                    }
                }
            },
            {
                'name': 'mid_low_load',
                'description': '中低负载配置 (参考train_mid_low)',
                'task_modifications': {
                    'communication': {
                        'task_size': [10000, 50000],
                        'task_requirement_unit': 0.0010,
                        'task_TTL_range': [10, 20]
                    },
                    'computation': {
                        'task_size': [1000, 5000],
                        'task_requirement_unit': 0.00012,
                        'task_TTL_range': [10, 20]
                    },
                    'sensing': {
                        'sensing_time': [11, 22],
                        'sensing_interval': [120,60,60,60,45]
                    }
                },
                'resource_modifications': {
                    'config_computation': {
                        'cycle_per_byte': 17,
                        'satellite': {
                            'cpu': 4
                        }
                    },
                    'config_sensing': {
                        'camera': 6
                    }
                }
            },
            {
                'name': 'mid_load',
                'description': '中负载配置 (参考train)',
                'task_modifications': {
                    'communication': {
                        'task_size': [10000, 50000],
                        'task_requirement_unit': 0.0020,
                        'task_TTL_range': [10, 20]
                    },
                    'computation': {
                        'task_size': [1000, 5000],
                        'task_requirement_unit': 0.00024,
                        'task_TTL_range': [10, 20]
                    },
                    'sensing': {
                        'sensing_time': [11, 22],
                        'sensing_interval': [60,45,45,45,30]
                    }
                },
                'resource_modifications': {
                    'config_computation': {
                        'cycle_per_byte': 17,
                        'satellite': {
                            'cpu': 4
                        }
                    },
                    'config_sensing': {
                        'camera': 6
                    }
                }
            },
            {
                'name': 'mid_high_load',
                'description': '中高负载配置 (参考train_mid_high)',
                'task_modifications': {
                    'communication': {
                        'task_size': [10000, 50000],
                        'task_requirement_unit': 0.0040,
                        'task_TTL_range': [10, 20]
                    },
                    'computation': {
                        'task_size': [1000, 5000],
                        'task_requirement_unit': 0.00048,
                        'task_TTL_range': [10, 20]
                    },
                    'sensing': {
                        'sensing_time': [11, 22],
                        'sensing_interval': [30, 15,15,15,10]
                    }
                },
                'resource_modifications': {
                    'config_computation': {
                        'cycle_per_byte': 17,
                        'satellite': {
                            'cpu': 4
                        }
                    },
                    'config_sensing': {
                        'camera': 6
                    }
                }
            },
            {
                'name': 'high_load',
                'description': '高负载配置 (参考train_high)',
                'task_modifications': {
                    'communication': {
                        'task_size': [10000, 50000],
                        'task_requirement_unit': 0.0080,
                        'task_TTL_range': [10, 20]
                    },
                    'computation': {
                        'task_size': [1000, 5000],
                        'task_requirement_unit': 0.00096,
                        'task_TTL_range': [10, 20]
                    },
                    'sensing': {
                        'sensing_time': [11, 22],
                        'sensing_interval': [15,10,10,10,5]
                    }
                },
                'resource_modifications': {
                    'config_computation': {
                        'cycle_per_byte': 17,
                        'satellite': {
                            'cpu': 4
                        }
                    },
                    'config_sensing': {
                        'camera': 6
                    }
                }
            },
        ]
        return variants

    
    def get_collector(self, config_name: str) -> StateSequenceCollector:
        """
        获取指定配置的数据收集器
        
        Args:
            config_name: 配置名称
            
        Returns:
            状态序列收集器实例
        """
        if config_name not in self.collectors:
            collector_dir = os.path.join(self.save_dir, config_name)
            self.collectors[config_name] = StateSequenceCollector(collector_dir)
        
        return self.collectors[config_name]

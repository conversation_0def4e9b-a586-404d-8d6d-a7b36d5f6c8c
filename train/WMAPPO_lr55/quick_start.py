#!/usr/bin/env python3
"""
快速启动脚本
一键运行世界模型训练流水线
"""

import os
import sys
import argparse

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='世界模型快速启动')
    parser.add_argument('--mode', type=str, choices=['full', 'demo', 'test'], default='demo',
                       help='运行模式: full(完整训练), demo(演示), test(快速测试)')
    
    args = parser.parse_args()
    
    print("🚀 世界模型快速启动")
    print(f"运行模式: {args.mode}")
    
    if args.mode == 'full':
        # 完整训练模式
        print("启动完整训练流水线...")
        cmd = "python run_world_model_pipeline.py --num_epochs 100 --batch_size 64"
        
    elif args.mode == 'demo':
        # 演示模式：较少的epochs和较小的batch size
        print("启动演示模式...")
        cmd = "python run_world_model_pipeline.py --num_epochs 20 --batch_size 16 --d_model 128 --num_layers 2"
        
    elif args.mode == 'test':
        # 测试模式：最小配置，快速验证
        print("启动测试模式...")
        cmd = "python run_world_model_pipeline.py --num_epochs 5 --batch_size 8 --d_model 64 --num_layers 2 --seq_len 20"
    
    print(f"执行命令: {cmd}")
    print("=" * 60)
    
    # 执行命令
    os.system(cmd)

if __name__ == "__main__":
    main()

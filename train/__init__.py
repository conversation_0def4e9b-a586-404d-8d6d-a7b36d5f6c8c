from .random.strategy import StrategyModule as Random_StrategyModule
from .greedy.strategy import StrategyModule as Greedy_StrategyModule
from .heuristic.strategy import StrategyModule as Heuristic_StrategyModule
from .predict.strategy import StrategyModule as Predict_StrategyModule

from .COMA_AC.strategy import COMA_AC_StrategyModule,parseCOMA_ACTrainArgs as parseCOMA_ACArgs
from .D3QN.strategy import D3QN_StrategyModule,parseD3QNTrainArgs as parseD3QNArgs

from .PTMAPPO_Reptile.strategy import MAPPOStrategyModule as PTRMAPPO_StrategyModule ,parseMAPPOTrainArgs as parsePTRMAPPOArgs
from .WMAPPO.mbrl_strategy import MBRLStrategyModule as WMAPPO_StrategyModule
from .WMAPPO.strategy import parseMAPPOTrainArgs as parseWMAPPOArgs


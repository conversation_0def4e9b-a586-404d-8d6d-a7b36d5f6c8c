import os
import sys
import yaml
import colorama
from pyinstrument import Profiler
from spacetasksim.spacetasksim_env import SpaceTaskSimEnv
from train.random.strategy import StrategyModule
from spacetasksim.spacetasksim_evaluation import SpaceTaskSimEvaluation
from spacetasksim.utils import file_util

colorama.init(autoreset=True)

config_path = sys.argv[1] if len(sys.argv) > 1 else os.path.join(os.path.dirname(__file__), '../config.yaml')
config = file_util.load_config(config_path)

last_episode = 12
max_episode = 200

# 注意：全局性能监控与episode性能监控不能同时使用
# # 定义全局性能监控
# global_profiler= Profiler()
# # 启动全局性能监控
# global_profiler.start()

# 定义episode性能监控
episode_profiler= Profiler()

sche_strategy_module=StrategyModule()
env=SpaceTaskSimEnv(config)
evaluation_module=SpaceTaskSimEvaluation(env,sche_strategy_module)
sche_strategy_module.initialize(env)
for episode in range(last_episode + 1, max_episode + 1):
    # 启动episode性能监控
    episode_profiler.start()
    while not env.isDone():
        sche_strategy_module.scheduleStep()
        env.step()
        evaluation_module.evaluateStep()
        evaluation_module.recordStepInfo()
    evaluation_module.stepInfoToFile(episode)
    evaluation_module.drawStepInfoByFile(episode)
    evaluation_module.episodeInfoToFile(episode)
    evaluation_module.drawEpisodeInfoByFile()
    evaluation_module.resetStepEvaluation()
    env.reset() # 输出预存信息时不要reset
    sche_strategy_module.reset()
    # 结束性能监控并打印报告
    episode_profiler.stop()
    episode_profiler.print()
    # 重置性能监控
    episode_profiler.reset()

env.close()


# # 结束性能监控并打印报告
# global_profiler.stop()
# global_profiler.print()
# # 重置性能监控
# global_profiler.reset()